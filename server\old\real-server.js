
const express = require('express');
const cors = require('cors');
const swaggerUi = require('swagger-ui-express');
const swaggerJsdoc = require('swagger-jsdoc');

// Try to load database, fallback to mock if fails
let getConnection, sql;
let useMockData = false;

try {
    const db = require('./config/database');
    getConnection = db.getConnection;
    sql = db.sql;
    // Database connection loaded silently
} catch (error) {
    // Database connection failed, using mock data
    useMockData = true;

    // Mock database functions
    getConnection = () => Promise.reject(new Error('Database not available'));
    sql = {
        NVarChar: 'nvarchar',
        Int: 'int',
        Date: 'date'
    };
}

// Load WhatsApp service with error handling
let whatsappService;
try {
    whatsappService = require('./services/whatsappService');
    // WhatsApp service loaded as singleton
    // WhatsApp service loaded silently
} catch (error) {
    // Error loading WhatsApp service, using mock
    // Create a mock service to prevent crashes
    whatsappService = {
        getStatus: () => ({
            status: 'error',
            isReady: false,
            qrCode: null,
            sessionInfo: null,
            lastActivity: null,
            messageQueueLength: 0,
            isProcessingQueue: false,
            reconnectAttempts: 0,
            error: 'Service not loaded'
        }),
        initialize: () => Promise.reject(new Error('Service not loaded')),
        disconnect: () => Promise.reject(new Error('Service not loaded')),
        sendMessage: () => Promise.reject(new Error('Service not loaded'))
    };
}

const app = express();
const PORT = 7080;

// Swagger configuration
const swaggerOptions = {
    definition: {
        openapi: '3.0.0',
        info: {
            title: 'Educational Centers Management API',
            version: '1.0.0',
            description: 'API لإدارة المراكز التعليمية - نظام شامل لإدارة الطلاب والمعلمين والحضور والمالية',
            contact: {
                name: 'فريق التطوير',
                email: '<EMAIL>'
            }
        },
        servers: [
            {
                url: `http://localhost:${PORT}`,
                description: 'خادم التطوير المحلي'
            },
            {
                url: `http://*************:${PORT}`,
                description: 'خادم الشبكة المحلية'
            },
            {
                url: `http://terraaa.ddns.net:${PORT}`,
                description: 'خادم الإنتاج'
            }
        ],
        components: {
            schemas: {
                User: {
                    type: 'object',
                    properties: {
                        id: { type: 'integer', example: 1 },
                        username: { type: 'string', example: 'admin' },
                        name: { type: 'string', example: 'مستخدم النظام' },
                        role: { type: 'string', example: 'admin' },
                        institution_id: { type: 'integer', example: 1 },
                        branch_id: { type: 'integer', example: 1 },
                        permissions: {
                            type: 'array',
                            items: { type: 'string' },
                            example: ['students', 'teachers', 'attendance', 'financial']
                        }
                    }
                },
                LoginRequest: {
                    type: 'object',
                    required: ['username', 'password'],
                    properties: {
                        username: { type: 'string', example: 'admin' },
                        password: { type: 'string', example: 'password123' }
                    }
                },
                ApiResponse: {
                    type: 'object',
                    properties: {
                        success: { type: 'boolean' },
                        message: { type: 'string' },
                        data: { type: 'object' }
                    }
                }
            }
        }
    },
    apis: ['./real-server.js'], // مسار الملف الحالي
};

const swaggerSpec = swaggerJsdoc(swaggerOptions);

// Middleware
app.use(cors({
    origin: [
        'http://localhost:7090',
        'http://localhost:3000',
        'http://127.0.0.1:7090',
        'http://*************:7090',
        'http://terraaa.ddns.net:7090',
        'https://terraaa.ddns.net:7090'
    ],
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization']
}));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Middleware to add user tracking
app.use((req, res, next) => {
    // Get user ID from headers or session (for now default to 1)
    req.userId = req.headers['x-user-id'] || req.session?.userId || 1;
    req.institutionId = req.headers['x-institution-id'] || req.session?.institutionId || 1;
    next();
});

// تسجيل جميع الطلبات للمراقبة
app.use((req, res, next) => {
    console.log(`📝 ${req.method} ${req.path} - ${new Date().toISOString()}`);
    next();
});

// Swagger UI setup
app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerSpec, {
    customCss: '.swagger-ui .topbar { display: none }',
    customSiteTitle: 'Educational Centers API Documentation',
    swaggerOptions: {
        persistAuthorization: true,
    }
}));

// Swagger JSON endpoint
app.get('/api-docs.json', (req, res) => {
    res.setHeader('Content-Type', 'application/json');
    res.send(swaggerSpec);
});

// Handle favicon
app.get('/favicon.ico', (req, res) => res.status(204).end());

/**
 * @swagger
 * /health:
 *   get:
 *     summary: فحص حالة الخادم
 *     description: يتحقق من حالة الخادم واتصال قاعدة البيانات
 *     tags: [System]
 *     responses:
 *       200:
 *         description: الخادم يعمل بشكل طبيعي
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "OK"
 *                 message:
 *                   type: string
 *                   example: "Server is running"
 *                 database:
 *                   type: string
 *                   example: "Connected"
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 *       500:
 *         description: خطأ في الخادم
 */
// Health check route
app.get('/health', async (req, res) => {
    try {
        const pool = await getConnection();
        const result = await pool.request().query('SELECT 1 as test');

        res.json({
            status: 'OK',
            message: 'Server is running with real database!',
            timestamp: new Date().toISOString(),
            port: PORT,
            database: result.recordset.length > 0,
            version: '2.0.0'
        });
    } catch (error) {
        res.status(500).json({
            status: 'ERROR',
            message: 'Database connection failed',
            timestamp: new Date().toISOString(),
            port: PORT,
            database: false,
            error: error.message
        });
    }
});

// API Health check
app.get('/api/health', async (req, res) => {
    try {
        const pool = await getConnection();
        const result = await pool.request().query('SELECT 1 as test');

        res.json({
            status: 'OK',
            message: 'API is working with database!',
            timestamp: new Date().toISOString(),
            port: PORT,
            database: result.recordset.length > 0,
            version: '2.0.0'
        });
    } catch (error) {
        res.status(500).json({
            status: 'ERROR',
            message: 'Database connection failed',
            timestamp: new Date().toISOString(),
            port: PORT,
            database: false,
            error: error.message
        });
    }
});

// Setup database tables
app.post('/api/setup-database', async (req, res) => {
    try {
        const pool = await getConnection();

        // Create Subjects table
        await pool.request().query(`
            IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Subjects' AND xtype='U')
            BEGIN
                CREATE TABLE Subjects (
                    id INT IDENTITY(1,1) PRIMARY KEY,
                    subject_code NVARCHAR(50) NOT NULL,
                    subject_name NVARCHAR(255) NOT NULL,
                    institution_id INT NOT NULL,
                    created_at DATETIME DEFAULT GETDATE(),
                    updated_at DATETIME DEFAULT GETDATE(),
                    is_active BIT DEFAULT 1,
                    is_deleted BIT DEFAULT 0,
                    created_by INT NULL,
                    updated_by INT NULL
                );
            END
        `);

        // Create Branches table
        await pool.request().query(`
            IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Branches' AND xtype='U')
            BEGIN
                CREATE TABLE Branches (
                    id INT IDENTITY(1,1) PRIMARY KEY,
                    branch_code NVARCHAR(50) NOT NULL,
                    branch_name NVARCHAR(255) NOT NULL,
                    address NVARCHAR(500) NULL,
                    phone1 NVARCHAR(20) NULL,
                    phone2 NVARCHAR(20) NULL,
                    institution_id INT NOT NULL,
                    created_at DATETIME DEFAULT GETDATE(),
                    updated_at DATETIME DEFAULT GETDATE(),
                    is_active BIT DEFAULT 1,
                    is_deleted BIT DEFAULT 0,
                    created_by INT NULL,
                    updated_by INT NULL
                );
            END
        `);

        // Add columns to Groups table
        try {
            await pool.request().query(`
                IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Groups') AND name = 'subject_id')
                BEGIN
                    ALTER TABLE Groups ADD subject_id INT NULL;
                END
            `);

            await pool.request().query(`
                IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Groups') AND name = 'branch_id')
                BEGIN
                    ALTER TABLE Groups ADD branch_id INT NULL;
                END
            `);
        } catch (error) {
            console.log('Groups table columns already exist or Groups table not found');
        }

        // Insert sample data
        await pool.request().query(`
            IF NOT EXISTS (SELECT * FROM Subjects WHERE subject_code = 'MATH')
            BEGIN
                INSERT INTO Subjects (subject_code, subject_name, institution_id) VALUES
                ('MATH', N'الرياضيات', 1),
                ('ARABIC', N'اللغة العربية', 1),
                ('ENGLISH', N'اللغة الإنجليزية', 1),
                ('SCIENCE', N'العلوم', 1),
                ('PHYSICS', N'الفيزياء', 1),
                ('CHEMISTRY', N'الكيمياء', 1),
                ('BIOLOGY', N'الأحياء', 1),
                ('HISTORY', N'التاريخ', 1),
                ('GEOGRAPHY', N'الجغرافيا', 1),
                ('RELIGION', N'التربية الدينية', 1);
            END
        `);

        await pool.request().query(`
            IF NOT EXISTS (SELECT * FROM Branches WHERE branch_code = 'BR001')
            BEGIN
                INSERT INTO Branches (branch_code, branch_name, address, phone1, institution_id) VALUES
                ('BR001', N'الفرع الرئيسي', N'شارع الجامعة، المدينة', '01234567890', 1),
                ('BR002', N'الفرع الأول', N'شارع النيل، الحي الثاني', '01234567891', 1),
                ('BR003', N'الفرع الثاني', N'شارع السلام، الحي الثالث', '01234567892', 1);
            END
        `);

        res.json({
            success: true,
            message: 'تم إعداد قاعدة البيانات بنجاح'
        });
    } catch (error) {
        console.error('Error setting up database:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في إعداد قاعدة البيانات',
            error: error.message
        });
    }
});

// Mock data for fallback
const mockStudents = [
    {
        id: 1, student_code: 'STU001', student_name: 'أحمد محمد علي',
        student_phone: '01234567890', father_phone: '01234567891', mother_phone: '01234567892',
        is_exempt_from_fees: 0, is_exempt_from_books: 0, created_at: '2024-01-15',
        institution_name: 'مؤسسة التعليم الأولى', branch_name: 'الفرع الرئيسي',
        grade_name: 'الصف الأول الثانوي', group_name: 'المجموعة الأولى',
        teacher_name: 'أستاذ محمد', teacher_code: 'T001',
        center_id: 1, grade_id: 33, group_id: 1, teacher_id: 48
    },
    {
        id: 2, student_code: 'STU002', student_name: 'فاطمة أحمد حسن',
        student_phone: '01234567893', father_phone: '01234567894', mother_phone: '01234567895',
        is_exempt_from_fees: 1, is_exempt_from_books: 0, created_at: '2024-01-16',
        institution_name: 'مؤسسة التعليم الأولى', branch_name: 'الفرع الرئيسي',
        grade_name: 'الصف الثاني الثانوي', group_name: 'المجموعة الثانية',
        teacher_name: 'أستاذة سارة', teacher_code: 'T002',
        center_id: 1, grade_id: 34, group_id: 2, teacher_id: 49
    }
];

// GET /api/students - Get all students with fallback to mock data
app.get('/api/students', async (req, res) => {
    try {
        console.log('📊 Fetching students with params:', req.query);

        const {
            page = 1,
            limit = 10,
            search = '',
            centerID,
            gradeID,
            groupID,
            teacherID
        } = req.query;

        // Try to use real database first
        try {
            const pool = await getConnection();

            // Build WHERE clause dynamically
            let whereConditions = ['1=1'];
            const params = [];

            if (centerID && centerID !== 'all') {
                whereConditions.push('s.institution_id = @centerID');
                params.push({ name: 'centerID', type: sql.Int, value: parseInt(centerID) });
            }

            if (gradeID && gradeID !== 'all') {
                whereConditions.push('s.grade_id = @gradeID');
                params.push({ name: 'gradeID', type: sql.Int, value: parseInt(gradeID) });
            }

            if (groupID && groupID !== 'all') {
                whereConditions.push('sg.group_id = @groupID');
                params.push({ name: 'groupID', type: sql.Int, value: parseInt(groupID) });
            }

            if (teacherID && teacherID !== 'all') {
                whereConditions.push('st.teacher_id = @teacherID');
                params.push({ name: 'teacherID', type: sql.Int, value: parseInt(teacherID) });
            }

            if (search) {
                whereConditions.push('(s.student_name LIKE @search OR s.student_code LIKE @search)');
                params.push({ name: 'search', type: sql.NVarChar, value: `%${search}%` });
            }

            const whereClause = whereConditions.join(' AND ');
            const offset = (page - 1) * limit;

            const query = `
                SELECT DISTINCT
                    s.id, s.student_code, s.student_name,
                    ISNULL(s.student_phone, '') as student_phone,
                    ISNULL(s.father_phone, '') as father_phone,
                    ISNULL(s.mother_phone, '') as mother_phone,
                    ISNULL(s.is_exempt_from_fees, 0) as is_exempt_from_fees,
                    ISNULL(s.is_exempt_from_books, 0) as is_exempt_from_books,
                    s.created_at,
                    FORMAT(s.created_at, 'yyyy-MM-dd') as created_at_formatted,
                    ISNULL(i.institution_name, 'غير محدد') as institution_name,
                    ISNULL(b.branch_name, 'غير محدد') as branch_name,
                    ISNULL(g.grade_name, 'غير محدد') as grade_name,
                    ISNULL(gr.group_name, 'غير محدد') as group_name,
                    ISNULL(t.teacher_name, 'غير محدد') as teacher_name,
                    ISNULL(t.teacher_code, '') as teacher_code
                FROM Students s
                LEFT JOIN Institutions i ON s.institution_id = i.id
                LEFT JOIN Branches b ON s.branch_id = b.id
                LEFT JOIN Grades g ON s.grade_id = g.id
                LEFT JOIN StudentGroups sg ON s.id = sg.student_id
                LEFT JOIN Groups gr ON sg.group_id = gr.id
                LEFT JOIN StudentTeachers st ON s.id = st.student_id
                LEFT JOIN Teachers t ON st.teacher_id = t.id
                WHERE ${whereClause}
                ORDER BY s.created_at DESC
                OFFSET ${offset} ROWS
                FETCH NEXT ${limit} ROWS ONLY
            `;

            const countQuery = `
                SELECT COUNT(DISTINCT s.id) as total
                FROM Students s
                LEFT JOIN StudentGroups sg ON s.id = sg.student_id
                LEFT JOIN StudentTeachers st ON s.id = st.student_id
                WHERE ${whereClause}
            `;

            const request = pool.request();
            params.forEach(param => {
                request.input(param.name, param.type, param.value);
            });

            const [result, countResult] = await Promise.all([
                request.query(query),
                request.query(countQuery)
            ]);

            console.log('✅ Using real database for students');

            res.json({
                success: true,
                data: result.recordset,
                pagination: {
                    page: parseInt(page),
                    limit: parseInt(limit),
                    total: countResult.recordset[0].total,
                    pages: Math.ceil(countResult.recordset[0].total / limit)
                }
            });

        } catch (dbError) {
            console.error('❌ Database error in students API:', dbError.message);
            res.status(500).json({
                success: false,
                message: 'خطأ في جلب بيانات الطلاب من قاعدة البيانات',
                error: dbError.message
            });
        }
    } catch (error) {
        console.error('Error fetching students:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في جلب بيانات الطلاب',
            error: error.message
        });
    }
});

// Continue with other APIs...

// POST /api/students - Create new student with auto-generated code
app.post('/api/students', async (req, res) => {
    try {
        const pool = await getConnection();
        const { student_name, student_phone, institution_id, branch_id, grade_id, teacher_ids, group_ids } = req.body;

        // Validate required fields
        if (!student_name || !institution_id) {
            return res.status(400).json({
                success: false,
                message: 'اسم الطالب والمؤسسة مطلوبان'
            });
        }

        // Check for duplicate student name in the same institution
        const nameCheckResult = await pool.request()
            .input('student_name', sql.NVarChar, student_name)
            .input('institution_id', sql.Int, institution_id)
            .query(`
                SELECT student_name, student_code
                FROM Students
                WHERE student_name = @student_name
                AND institution_id = @institution_id
                AND is_deleted = 0
            `);

        if (nameCheckResult.recordset.length > 0) {
            const existingStudent = nameCheckResult.recordset[0];
            return res.status(400).json({
                success: false,
                message: `الطالب "${existingStudent.student_name}" مسجل مسبقاً بالكود: ${existingStudent.student_code}`
            });
        }

        // Check for duplicate phone number if provided
        if (student_phone) {
            const phoneCheckResult = await pool.request()
                .input('student_phone', sql.NVarChar, student_phone)
                .input('institution_id', sql.Int, institution_id)
                .query(`
                    SELECT student_name, student_code
                    FROM Students
                    WHERE student_phone = @student_phone
                    AND institution_id = @institution_id
                    AND is_deleted = 0
                `);

            if (phoneCheckResult.recordset.length > 0) {
                const existingStudent = phoneCheckResult.recordset[0];
                return res.status(400).json({
                    success: false,
                    message: `رقم الهاتف مسجل مسبقاً للطالب "${existingStudent.student_name}" بالكود: ${existingStudent.student_code}`
                });
            }
        }

        // Check for duplicate father phone if provided
        if (req.body.father_phone) {
            const fatherPhoneCheckResult = await pool.request()
                .input('father_phone', sql.NVarChar, req.body.father_phone)
                .input('institution_id', sql.Int, institution_id)
                .query(`
                    SELECT student_name, student_code
                    FROM Students
                    WHERE father_phone = @father_phone
                    AND institution_id = @institution_id
                    AND is_deleted = 0
                `);

            if (fatherPhoneCheckResult.recordset.length > 0) {
                const existingStudent = fatherPhoneCheckResult.recordset[0];
                return res.status(400).json({
                    success: false,
                    message: `رقم هاتف ولي الأمر مسجل مسبقاً للطالب "${existingStudent.student_name}" بالكود: ${existingStudent.student_code}`
                });
            }
        }

        // Validate phone number format
        const phoneRegex = /^[0-9+\-\s()]*$/;
        if (student_phone && !phoneRegex.test(student_phone)) {
            return res.status(400).json({
                success: false,
                message: 'رقم هاتف الطالب غير صحيح'
            });
        }

        if (req.body.father_phone && !phoneRegex.test(req.body.father_phone)) {
            return res.status(400).json({
                success: false,
                message: 'رقم هاتف ولي الأمر غير صحيح'
            });
        }

        if (req.body.mother_phone && !phoneRegex.test(req.body.mother_phone)) {
            return res.status(400).json({
                success: false,
                message: 'رقم هاتف الأم غير صحيح'
            });
        }

        // Validate student name length
        if (student_name.length < 2) {
            return res.status(400).json({
                success: false,
                message: 'اسم الطالب يجب أن يكون أكثر من حرفين'
            });
        }

        if (student_name.length > 255) {
            return res.status(400).json({
                success: false,
                message: 'اسم الطالب يجب أن يكون أقل من 255 حرف'
            });
        }

        // Validate that branch belongs to the institution if branch is provided
        if (branch_id) {
            const branchCheckResult = await pool.request()
                .input('branch_id', sql.Int, branch_id)
                .input('institution_id', sql.Int, institution_id)
                .query(`
                    SELECT id
                    FROM Branches
                    WHERE id = @branch_id
                    AND institution_id = @institution_id
                    AND is_active = 1 AND is_deleted = 0
                `);

            if (branchCheckResult.recordset.length === 0) {
                return res.status(400).json({
                    success: false,
                    message: 'الفرع المختار غير صحيح أو لا ينتمي للمؤسسة'
                });
            }
        }

        // Validate selected groups belong to the institution and branch
        if (req.body.group_ids && req.body.group_ids.length > 0) {
            for (const groupId of req.body.group_ids) {
                const groupCheckResult = await pool.request()
                    .input('group_id', sql.Int, groupId)
                    .input('institution_id', sql.Int, institution_id)
                    .input('branch_id', sql.Int, branch_id || null)
                    .query(`
                        SELECT id, group_name, grade_id
                        FROM Groups
                        WHERE id = @group_id
                        AND institution_id = @institution_id
                        AND (@branch_id IS NULL OR branch_id = @branch_id)
                        AND is_active = 1 AND is_deleted = 0
                    `);

                if (groupCheckResult.recordset.length === 0) {
                    return res.status(400).json({
                        success: false,
                        message: `المجموعة المختارة غير صحيحة أو لا تنتمي للمؤسسة/الفرع`
                    });
                }
            }
        }

        // Validate selected teachers belong to the institution and branch
        if (req.body.teacher_ids && req.body.teacher_ids.length > 0) {
            for (const teacherId of req.body.teacher_ids) {
                const teacherCheckResult = await pool.request()
                    .input('teacher_id', sql.Int, teacherId)
                    .input('institution_id', sql.Int, institution_id)
                    .query(`
                        SELECT t.id, t.teacher_name
                        FROM Teachers t
                        WHERE t.id = @teacher_id
                        AND t.institution_id = @institution_id
                        AND t.is_active = 1 AND t.is_deleted = 0
                    `);

                if (teacherCheckResult.recordset.length === 0) {
                    return res.status(400).json({
                        success: false,
                        message: `المدرس المختار غير صحيح أو لا ينتمي للمؤسسة`
                    });
                }

                // Check if teacher belongs to the selected branch
                if (branch_id) {
                    const teacherBranchCheckResult = await pool.request()
                        .input('teacher_id', sql.Int, teacherId)
                        .input('branch_id', sql.Int, branch_id)
                        .query(`
                            SELECT tb.id
                            FROM TeacherBranches tb
                            WHERE tb.teacher_id = @teacher_id
                            AND tb.branch_id = @branch_id
                            AND tb.is_active = 1
                        `);

                    if (teacherBranchCheckResult.recordset.length === 0) {
                        return res.status(400).json({
                            success: false,
                            message: `المدرس المختار لا ينتمي للفرع المحدد`
                        });
                    }
                }
            }
        }

        // Generate student code automatically
        const codeResult = await pool.request()
            .input('institution_id', sql.Int, institution_id)
            .query(`
                SELECT TOP 1 student_code
                FROM Students
                WHERE institution_id = @institution_id AND student_code LIKE 'STU%'
                ORDER BY CAST(SUBSTRING(student_code, 4, LEN(student_code)) AS INT) DESC
            `);

        let nextCode = 'STU001';
        if (codeResult.recordset.length > 0) {
            const lastCode = codeResult.recordset[0].student_code;
            const lastNumber = parseInt(lastCode.substring(3));
            nextCode = `STU${String(lastNumber + 1).padStart(3, '0')}`;
        }

        const studentData = {
            ...req.body,
            student_code: nextCode
        };
        
        const query = `
            INSERT INTO Students (
                student_code, student_name, student_phone, father_phone,
                mother_phone, gender, institution_id, branch_id, grade_id, is_exempt_from_fees,
                is_exempt_from_books, created_at, updated_at, is_active, is_deleted
            ) VALUES (
                @student_code, @student_name, @student_phone, @father_phone,
                @mother_phone, @gender, @institution_id, @branch_id, @grade_id, @is_exempt_from_fees,
                @is_exempt_from_books, GETDATE(), GETDATE(), 1, 0
            );
            SELECT SCOPE_IDENTITY() as StudentID;
        `;
        
        const request = pool.request();
        request.input('student_code', sql.NVarChar, studentData.student_code);
        request.input('student_name', sql.NVarChar, studentData.student_name);
        request.input('student_phone', sql.NVarChar, studentData.student_phone || null);
        request.input('father_phone', sql.NVarChar, studentData.father_phone || null);
        request.input('mother_phone', sql.NVarChar, studentData.mother_phone || null);
        request.input('gender', sql.NVarChar, studentData.gender || 'male');
        request.input('institution_id', sql.Int, studentData.institution_id);
        request.input('branch_id', sql.Int, studentData.branch_id || null);
        request.input('grade_id', sql.Int, studentData.grade_id || null);
        request.input('is_exempt_from_fees', sql.Bit, studentData.is_exempt_from_fees || false);
        request.input('is_exempt_from_books', sql.Bit, studentData.is_exempt_from_books || false);
        
        const result = await request.query(query);
        const studentID = result.recordset[0].StudentID;

        // Insert student-teacher relationships if teachers are selected
        if (studentData.teacher_ids && studentData.teacher_ids.length > 0) {
            for (const teacherId of studentData.teacher_ids) {
                await pool.request()
                    .input('student_id', sql.Int, studentID)
                    .input('teacher_id', sql.Int, teacherId)
                    .query(`
                        INSERT INTO StudentTeachers (student_id, teacher_id, created_at, is_active)
                        VALUES (@student_id, @teacher_id, GETDATE(), 1)
                    `);
            }
        }

        // Insert student-group relationships if groups are selected
        if (studentData.group_ids && studentData.group_ids.length > 0) {
            for (const groupId of studentData.group_ids) {
                await pool.request()
                    .input('student_id', sql.Int, studentID)
                    .input('group_id', sql.Int, groupId)
                    .input('institution_id', sql.Int, studentData.institution_id)
                    .query(`
                        INSERT INTO StudentGroups (student_id, group_id, institution_id, created_at)
                        VALUES (@student_id, @group_id, @institution_id, GETDATE())
                    `);
            }
        }

        res.status(201).json({
            success: true,
            message: 'تم إضافة الطالب بنجاح',
            data: { id: studentID, ...studentData }
        });
    } catch (error) {
        console.error('Error creating student:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في إضافة الطالب',
            error: error.message
        });
    }
});

// GET /api/grades/by-branch/:branchId - Get grades for a specific branch
app.get('/api/grades/by-branch/:branchId', async (req, res) => {
    try {
        const pool = await getConnection();
        const { branchId } = req.params;

        const result = await pool.request()
            .input('branchId', sql.Int, branchId)
            .query(`
                SELECT gr.id, gr.grade_name
                FROM Grades gr
                WHERE gr.branch_id = @branchId
                AND gr.is_active = 1 AND gr.is_deleted = 0
                ORDER BY gr.grade_name
            `);

        res.json({
            success: true,
            data: result.recordset
        });
    } catch (error) {
        console.error('Error fetching grades by branch:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في جلب الصفوف',
            error: error.message
        });
    }
});

// GET /api/grades/:id - Get single grade details
app.get('/api/grades/:id', async (req, res) => {
    try {
        const pool = await getConnection();
        const { id } = req.params;

        const result = await pool.request()
            .input('id', sql.Int, parseInt(id))
            .query(`
                SELECT
                    g.id,
                    g.grade_name,
                    g.grade_code,
                    g.institution_id,
                    g.branch_id,
                    g.description,
                    g.created_at,
                    i.institution_name,
                    b.branch_name
                FROM Grades g
                LEFT JOIN Institutions i ON g.institution_id = i.id
                LEFT JOIN Branches b ON g.branch_id = b.id
                WHERE g.id = @id AND g.is_deleted = 0
            `);

        if (result.recordset.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'الصف غير موجود'
            });
        }

        res.json({
            success: true,
            data: result.recordset[0]
        });
    } catch (error) {
        console.error('Error fetching grade:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في جلب بيانات الصف',
            error: error.message
        });
    }
});

// GET /api/grades/next-code - Get next grade code for institution
app.get('/api/grades/next-code', async (req, res) => {
    try {
        const pool = await getConnection();
        const { institutionID } = req.query;

        if (!institutionID) {
            return res.status(400).json({
                success: false,
                message: 'معرف المؤسسة مطلوب'
            });
        }

        const result = await pool.request()
            .input('institution_id', sql.Int, institutionID)
            .query(`
                SELECT TOP 1 grade_code
                FROM Grades
                WHERE institution_id = @institution_id
                AND grade_code LIKE 'GRD%'
                ORDER BY CAST(SUBSTRING(grade_code, 4, LEN(grade_code)) AS INT) DESC
            `);

        let nextCode = 'GRD001';
        if (result.recordset.length > 0) {
            const lastCode = result.recordset[0].grade_code;
            const lastNumber = parseInt(lastCode.substring(3));
            nextCode = `GRD${String(lastNumber + 1).padStart(3, '0')}`;
        }

        res.json({
            success: true,
            data: {
                nextCode: nextCode
            }
        });
    } catch (error) {
        console.error('Error getting next grade code:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في جلب الكود التالي',
            error: error.message
        });
    }
});

// GET /api/grades - Get all grades with pagination and filters
app.get('/api/grades', async (req, res) => {
    try {
        const pool = await getConnection();
        const { page = 1, limit = 10, search = '', centerID, branchID } = req.query;

        const offset = (page - 1) * limit;
        let whereClause = 'WHERE g.is_deleted = 0';
        const params = [];

        if (centerID) {
            whereClause += ' AND g.institution_id = @centerID';
            params.push({ name: 'centerID', type: sql.Int, value: parseInt(centerID) });
        }

        if (branchID) {
            whereClause += ' AND g.branch_id = @branchID';
            params.push({ name: 'branchID', type: sql.Int, value: parseInt(branchID) });
        }

        if (search) {
            whereClause += ' AND (g.grade_name LIKE @search OR g.grade_code LIKE @search)';
            params.push({ name: 'search', type: sql.NVarChar, value: `%${search}%` });
        }

        const query = `
            SELECT
                g.id,
                g.grade_code,
                g.grade_name,
                g.institution_id,
                g.branch_id,
                FORMAT(g.created_at, 'yyyy-MM-dd') as created_at,
                g.is_active,
                i.institution_name,
                b.branch_name,
                (SELECT COUNT(*) FROM Students s WHERE s.grade_id = g.id AND s.is_deleted = 0) as student_count
            FROM Grades g
            LEFT JOIN Institutions i ON g.institution_id = i.id
            LEFT JOIN Branches b ON g.branch_id = b.id
            ${whereClause}
            ORDER BY g.created_at DESC
            OFFSET @offset ROWS FETCH NEXT @limit ROWS ONLY
        `;

        const countQuery = `
            SELECT COUNT(*) as total
            FROM Grades g
            ${whereClause}
        `;

        const request = pool.request()
            .input('offset', sql.Int, offset)
            .input('limit', sql.Int, parseInt(limit));

        params.forEach(param => {
            request.input(param.name, param.type, param.value);
        });

        const [result, countResult] = await Promise.all([
            request.query(query),
            request.query(countQuery)
        ]);

        res.json({
            success: true,
            data: result.recordset,
            total: countResult.recordset[0].total,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total: countResult.recordset[0].total,
                totalPages: Math.ceil(countResult.recordset[0].total / limit)
            }
        });
    } catch (error) {
        console.error('Error fetching grades:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في جلب الصفوف',
            error: error.message
        });
    }
});

// POST /api/grades - Create new grade
app.post('/api/grades', async (req, res) => {
    try {
        const pool = await getConnection();
        const { grade_code, grade_name, institution_id, branch_id } = req.body;

        if (!grade_name || !institution_id) {
            return res.status(400).json({
                success: false,
                message: 'اسم الصف والمؤسسة مطلوبان'
            });
        }

        // Generate grade code if not provided
        let finalGradeCode = grade_code;
        if (!finalGradeCode) {
            const codeResult = await pool.request()
                .input('institution_id', sql.Int, institution_id)
                .query(`
                    SELECT TOP 1 grade_code
                    FROM Grades
                    WHERE institution_id = @institution_id AND grade_code LIKE 'GRD%'
                    ORDER BY CAST(SUBSTRING(grade_code, 4, LEN(grade_code)) AS INT) DESC
                `);

            let nextNumber = 1;
            if (codeResult.recordset.length > 0) {
                const lastCode = codeResult.recordset[0].grade_code;
                const lastNumber = parseInt(lastCode.substring(3));
                nextNumber = lastNumber + 1;
            }
            finalGradeCode = `GRD${nextNumber.toString().padStart(3, '0')}`;
        }

        const result = await pool.request()
            .input('grade_code', sql.NVarChar, finalGradeCode)
            .input('grade_name', sql.NVarChar, grade_name)
            .input('institution_id', sql.Int, institution_id)
            .input('branch_id', sql.Int, branch_id || null)
            .query(`
                INSERT INTO Grades (grade_code, grade_name, institution_id, branch_id, created_at, is_active, is_deleted)
                OUTPUT INSERTED.id, INSERTED.grade_code, INSERTED.grade_name
                VALUES (@grade_code, @grade_name, @institution_id, @branch_id, GETDATE(), 1, 0)
            `);

        res.json({
            success: true,
            message: 'تم إضافة الصف بنجاح',
            data: result.recordset[0]
        });
    } catch (error) {
        console.error('Error creating grade:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في إضافة الصف',
            error: error.message
        });
    }
});

// PUT /api/grades/:id - Update grade
app.put('/api/grades/:id', async (req, res) => {
    try {
        const pool = await getConnection();
        const { id } = req.params;
        const { grade_name, branch_id } = req.body;

        if (!grade_name) {
            return res.status(400).json({
                success: false,
                message: 'اسم الصف مطلوب'
            });
        }

        const result = await pool.request()
            .input('id', sql.Int, id)
            .input('grade_name', sql.NVarChar, grade_name)
            .input('branch_id', sql.Int, branch_id || null)
            .query(`
                UPDATE Grades
                SET grade_name = @grade_name, branch_id = @branch_id, updated_at = GETDATE()
                OUTPUT INSERTED.id, INSERTED.grade_code, INSERTED.grade_name
                WHERE id = @id AND is_deleted = 0
            `);

        if (result.recordset.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'الصف غير موجود'
            });
        }

        res.json({
            success: true,
            message: 'تم تحديث الصف بنجاح',
            data: result.recordset[0]
        });
    } catch (error) {
        console.error('Error updating grade:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في تحديث الصف',
            error: error.message
        });
    }
});

// DELETE /api/grades/:id - Delete grade
app.delete('/api/grades/:id', async (req, res) => {
    try {
        const pool = await getConnection();
        const { id } = req.params;

        // Check if grade is used by students
        const usageCheck = await pool.request()
            .input('id', sql.Int, id)
            .query(`
                SELECT COUNT(*) as student_count
                FROM Students
                WHERE grade_id = @id AND is_deleted = 0
            `);

        if (usageCheck.recordset[0].student_count > 0) {
            return res.status(400).json({
                success: false,
                message: 'لا يمكن حذف الصف لأنه مرتبط بطلاب'
            });
        }

        const result = await pool.request()
            .input('id', sql.Int, id)
            .query(`
                UPDATE Grades
                SET is_deleted = 1, updated_at = GETDATE()
                WHERE id = @id AND is_deleted = 0
            `);

        if (result.rowsAffected[0] === 0) {
            return res.status(404).json({
                success: false,
                message: 'الصف غير موجود'
            });
        }

        res.json({
            success: true,
            message: 'تم حذف الصف بنجاح'
        });
    } catch (error) {
        console.error('Error deleting grade:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في حذف الصف',
            error: error.message
        });
    }
});

// GET /api/teachers/by-branch-grade - Get teachers for specific branch and grade
app.get('/api/teachers/by-branch-grade', async (req, res) => {
    try {
        const pool = await getConnection();
        const { branchId, gradeId } = req.query;

        let whereClause = 'WHERE t.is_active = 1 AND t.is_deleted = 0';
        let joinClause = '';
        const params = [];

        if (branchId) {
            joinClause += ' INNER JOIN TeacherBranches tb ON t.id = tb.teacher_id';
            whereClause += ' AND tb.branch_id = @branchId AND tb.is_active = 1';
            params.push({ name: 'branchId', type: sql.Int, value: parseInt(branchId) });
        }

        if (gradeId) {
            whereClause += ' AND tg.grade_id = @gradeId';
            params.push({ name: 'gradeId', type: sql.Int, value: parseInt(gradeId) });
        }

        const query = `
            SELECT DISTINCT t.id, t.teacher_name, t.teacher_code, t.specialization
            FROM Teachers t
            INNER JOIN TeacherGrades tg ON t.id = tg.teacher_id
            ${joinClause}
            ${whereClause}
            AND tg.is_active = 1
            ORDER BY t.teacher_name
        `;

        const request = pool.request();
        params.forEach(param => {
            request.input(param.name, param.type, param.value);
        });

        const result = await request.query(query);

        res.json({
            success: true,
            data: result.recordset
        });
    } catch (error) {
        console.error('Error fetching teachers by branch and grade:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في جلب المدرسين',
            error: error.message
        });
    }
});

// GET /api/groups/by-teachers - Get groups for specific teachers, branch, and grade
app.get('/api/groups/by-teachers', async (req, res) => {
    try {
        const pool = await getConnection();
        const { teacherIds, branchId, gradeId } = req.query;

        if (!teacherIds) {
            return res.json({
                success: true,
                data: []
            });
        }

        const teacherIdArray = Array.isArray(teacherIds) ? teacherIds : [teacherIds];
        const teacherIdList = teacherIdArray.map(id => parseInt(id)).join(',');

        let whereClause = `WHERE g.is_active = 1 AND g.is_deleted = 0 AND g.teacher_id IN (${teacherIdList})`;
        const params = [];

        if (branchId) {
            whereClause += ' AND g.branch_id = @branchId';
            params.push({ name: 'branchId', type: sql.Int, value: parseInt(branchId) });
        }

        if (gradeId) {
            whereClause += ' AND g.grade_id = @gradeId';
            params.push({ name: 'gradeId', type: sql.Int, value: parseInt(gradeId) });
        }

        const query = `
            SELECT g.id, g.group_name, g.group_code, g.description, g.teacher_id, t.teacher_name, s.subject_name
            FROM Groups g
            LEFT JOIN Teachers t ON g.teacher_id = t.id
            LEFT JOIN Subjects s ON g.subject_id = s.id
            ${whereClause}
            ORDER BY g.group_name
        `;

        const request = pool.request();
        params.forEach(param => {
            request.input(param.name, param.type, param.value);
        });

        const result = await request.query(query);

        res.json({
            success: true,
            data: result.recordset
        });
    } catch (error) {
        console.error('Error fetching groups by teachers:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في جلب المجموعات',
            error: error.message
        });
    }
});

// GET /api/centers - Get all institutions
app.get('/api/centers', async (req, res) => {
    try {
        const pool = await getConnection();
        
        const query = `
            SELECT
                i.id,
                i.institution_name as name
            FROM Institutions i
            WHERE i.is_deleted = 0
            ORDER BY i.institution_name
        `;
        
        const result = await pool.request().query(query);
        
        res.json({
            success: true,
            data: result.recordset
        });
    } catch (error) {
        console.error('Error fetching institutions:', error);
        res.status(500).json({
            success: false,
            message: 'Error fetching institutions',
            error: error.message
        });
    }
});

// GET /api/institutions - Alias for centers (institutions)
app.get('/api/institutions', async (req, res) => {
    try {
        const pool = await getConnection();

        const query = `
            SELECT
                i.id,
                i.institution_name as name
            FROM Institutions i
            WHERE i.is_deleted = 0
            ORDER BY i.institution_name
        `;

        const result = await pool.request().query(query);

        res.json({
            success: true,
            data: result.recordset
        });
    } catch (error) {
        console.error('Error fetching institutions:', error);
        res.status(500).json({
            success: false,
            message: 'Error fetching institutions',
            error: error.message
        });
    }
});

// GET /api/centers/:id/branches - Get branches of an institution
app.get('/api/centers/:id/branches', async (req, res) => {
    try {
        const pool = await getConnection();
        const { id } = req.params;
        
        const query = `
            SELECT 
                b.*,
                COUNT(DISTINCT s.id) as TotalStudents
            FROM Branches b
            LEFT JOIN Students s ON b.id = s.branch_id AND s.is_deleted = 0
            WHERE b.institution_id = @institutionID AND b.is_deleted = 0
            GROUP BY b.id, b.branch_code, b.branch_name, b.address, 
                     b.phone1, b.phone2, b.institution_id, b.created_at, 
                     b.updated_at, b.is_active, b.is_deleted, b.created_by, b.updated_by
            ORDER BY b.branch_name
        `;
        
        const result = await pool.request()
            .input('institutionID', sql.Int, id)
            .query(query);
        
        res.json({
            success: true,
            data: result.recordset
        });
    } catch (error) {
        console.error('Error fetching institution branches:', error);
        res.status(500).json({
            success: false,
            message: 'Error fetching institution branches',
            error: error.message
        });
    }
});

// POST /api/centers - Create new center
app.post('/api/centers', async (req, res) => {
    try {
        const pool = await getConnection();
        const { institution_name } = req.body;

        if (!institution_name) {
            return res.status(400).json({
                success: false,
                message: 'اسم المركز مطلوب'
            });
        }

        // Generate institution code
        const codeResult = await pool.request().query(`
            SELECT TOP 1 institution_code
            FROM Institutions
            WHERE institution_code LIKE 'INST-%'
            ORDER BY CAST(SUBSTRING(institution_code, 6, LEN(institution_code)) AS INT) DESC
        `);

        let nextCode = 'INST-001';
        if (codeResult.recordset.length > 0) {
            const lastCode = codeResult.recordset[0].institution_code;
            const lastNumber = parseInt(lastCode.substring(5));
            const nextNumber = lastNumber + 1;
            nextCode = `INST-${nextNumber.toString().padStart(3, '0')}`;
        }

        const result = await pool.request()
            .input('institution_code', sql.NVarChar, nextCode)
            .input('institution_name', sql.NVarChar, institution_name)
            .query(`
                INSERT INTO Institutions (institution_code, institution_name, created_at, is_active, is_deleted)
                OUTPUT INSERTED.id, INSERTED.institution_code, INSERTED.institution_name, INSERTED.created_at
                VALUES (@institution_code, @institution_name, GETDATE(), 1, 0)
            `);

        res.json({
            success: true,
            message: 'تم إضافة المركز بنجاح',
            data: result.recordset[0]
        });
    } catch (error) {
        console.error('Error creating center:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في إضافة المركز',
            error: error.message
        });
    }
});

// GET /api/groups - Get all groups with pagination and filters
app.get('/api/groups', async (req, res) => {
    try {
        const pool = await getConnection();
        const {
            page = 1,
            limit = 10,
            search = '',
            centerID,
            gradeID,
            teacherID
        } = req.query;

        const offset = (page - 1) * limit;

        let whereClause = 'WHERE g.is_deleted = 0';
        const params = [];

        if (search) {
            whereClause += ` AND (g.group_name LIKE @search OR g.group_code LIKE @search)`;
            params.push({ name: 'search', type: sql.NVarChar, value: `%${search}%` });
        }

        // Always require institution filter for data isolation
        if (centerID) {
            whereClause += ' AND g.institution_id = @centerID';
            params.push({ name: 'centerID', type: sql.Int, value: parseInt(centerID) });
        } else {
            // If no centerID provided, return empty result for security
            return res.json({
                success: true,
                data: [],
                pagination: { page: 1, limit: parseInt(limit), total: 0, pages: 0 }
            });
        }

        if (gradeID) {
            whereClause += ' AND g.grade_id = @gradeID';
            params.push({ name: 'gradeID', type: sql.Int, value: parseInt(gradeID) });
        }

        if (teacherID) {
            whereClause += ' AND g.teacher_id = @teacherID';
            params.push({ name: 'teacherID', type: sql.Int, value: parseInt(teacherID) });
        }

        const query = `
            SELECT
                g.id, g.group_code, g.group_name, g.teacher_id,
                t.teacher_name,
                gr.grade_name,
                s.subject_name,
                b.branch_name,
                i.institution_name,
                g.created_at,
                (SELECT COUNT(*) FROM StudentGroups sg WHERE sg.group_id = g.id) as student_count
            FROM Groups g
            LEFT JOIN Teachers t ON g.teacher_id = t.id
            LEFT JOIN Grades gr ON g.grade_id = gr.id
            LEFT JOIN Subjects s ON g.subject_id = s.id
            LEFT JOIN Branches b ON g.branch_id = b.id
            LEFT JOIN Institutions i ON g.institution_id = i.id
            ${whereClause}
            ORDER BY g.created_at DESC
            OFFSET @offset ROWS FETCH NEXT @limit ROWS ONLY
        `;

        const countQuery = `
            SELECT COUNT(*) as total
            FROM Groups g
            ${whereClause}
        `;

        const request = pool.request()
            .input('offset', sql.Int, offset)
            .input('limit', sql.Int, parseInt(limit));

        params.forEach(param => {
            request.input(param.name, param.type, param.value);
        });

        const [result, countResult] = await Promise.all([
            request.query(query),
            request.query(countQuery)
        ]);

        res.json({
            success: true,
            data: result.recordset,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total: countResult.recordset[0].total,
                pages: Math.ceil(countResult.recordset[0].total / limit)
            }
        });
    } catch (error) {
        console.error('Error fetching groups:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في جلب المجموعات',
            error: error.message
        });
    }
});

// GET /api/groups/next-code - Get next group code for institution
app.get('/api/groups/next-code', async (req, res) => {
    try {
        const pool = await getConnection();
        const { institutionID } = req.query;

        if (!institutionID) {
            return res.status(400).json({
                success: false,
                message: 'معرف المؤسسة مطلوب'
            });
        }

        const result = await pool.request()
            .input('institutionID', sql.Int, institutionID)
            .query(`
                SELECT TOP 1 group_code
                FROM Groups
                WHERE institution_id = @institutionID AND group_code LIKE 'GRP%'
                ORDER BY CAST(SUBSTRING(group_code, 4, LEN(group_code)) AS INT) DESC
            `);

        let nextCode = 'GRP001';
        if (result.recordset.length > 0) {
            const lastCode = result.recordset[0].group_code;
            const lastNumber = parseInt(lastCode.substring(3));
            const nextNumber = lastNumber + 1;
            nextCode = `GRP${nextNumber.toString().padStart(3, '0')}`;
        }

        res.json({
            success: true,
            data: { nextCode }
        });
    } catch (error) {
        console.error('Error getting next group code:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في الحصول على الكود التالي'
        });
    }
});

// POST /api/groups - Create new group
app.post('/api/groups', async (req, res) => {
    try {
        const pool = await getConnection();
        const { group_code, group_name, teacher_id, grade_id, subject_id, branch_id, institution_id, description } = req.body;

        if (!group_name || !teacher_id || !grade_id || !subject_id || !branch_id || !institution_id) {
            return res.status(400).json({
                success: false,
                message: 'جميع الحقول المطلوبة يجب ملؤها'
            });
        }

        // Generate group code if not provided
        let finalGroupCode = group_code;
        if (!finalGroupCode) {
            const codeResult = await pool.request()
                .input('institution_id', sql.Int, institution_id)
                .query(`
                    SELECT TOP 1 group_code
                    FROM Groups
                    WHERE institution_id = @institution_id AND group_code LIKE 'GRP%'
                    ORDER BY CAST(SUBSTRING(group_code, 4, LEN(group_code)) AS INT) DESC
                `);

            let nextNumber = 1;
            if (codeResult.recordset.length > 0) {
                const lastCode = codeResult.recordset[0].group_code;
                const lastNumber = parseInt(lastCode.substring(3));
                nextNumber = lastNumber + 1;
            }
            finalGroupCode = `GRP${nextNumber.toString().padStart(3, '0')}`;
        }

        const result = await pool.request()
            .input('group_code', sql.NVarChar, finalGroupCode)
            .input('group_name', sql.NVarChar, group_name)
            .input('teacher_id', sql.Int, teacher_id)
            .input('grade_id', sql.Int, grade_id)
            .input('subject_id', sql.Int, subject_id)
            .input('branch_id', sql.Int, branch_id)
            .input('institution_id', sql.Int, institution_id)
            .input('description', sql.NVarChar, description || null)
            .query(`
                INSERT INTO Groups (group_code, group_name, teacher_id, grade_id, subject_id, branch_id, institution_id, description, created_at, is_active, is_deleted)
                OUTPUT INSERTED.id, INSERTED.group_code, INSERTED.group_name
                VALUES (@group_code, @group_name, @teacher_id, @grade_id, @subject_id, @branch_id, @institution_id, @description, GETDATE(), 1, 0)
            `);

        res.json({
            success: true,
            message: 'تم إضافة المجموعة بنجاح',
            data: result.recordset[0]
        });
    } catch (error) {
        console.error('Error creating group:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في إضافة المجموعة',
            error: error.message
        });
    }
});

// PUT /api/groups/:id - Update group
app.put('/api/groups/:id', async (req, res) => {
    try {
        const pool = await getConnection();
        const { id } = req.params;
        const { group_code, group_name, teacher_id, grade_id, subject_id, branch_id, description } = req.body;

        if (!group_name || !teacher_id || !grade_id || !subject_id || !branch_id) {
            return res.status(400).json({
                success: false,
                message: 'جميع الحقول المطلوبة يجب ملؤها'
            });
        }

        const result = await pool.request()
            .input('id', sql.Int, id)
            .input('group_code', sql.NVarChar, group_code)
            .input('group_name', sql.NVarChar, group_name)
            .input('teacher_id', sql.Int, teacher_id)
            .input('grade_id', sql.Int, grade_id)
            .input('subject_id', sql.Int, subject_id)
            .input('branch_id', sql.Int, branch_id)
            .input('description', sql.NVarChar, description || null)
            .query(`
                UPDATE Groups
                SET group_code = @group_code,
                    group_name = @group_name,
                    teacher_id = @teacher_id,
                    grade_id = @grade_id,
                    subject_id = @subject_id,
                    branch_id = @branch_id,
                    description = @description,
                    updated_at = GETDATE()
                WHERE id = @id AND is_deleted = 0
            `);

        if (result.rowsAffected[0] === 0) {
            return res.status(404).json({
                success: false,
                message: 'المجموعة غير موجودة'
            });
        }

        res.json({
            success: true,
            message: 'تم تحديث المجموعة بنجاح'
        });
    } catch (error) {
        console.error('Error updating group:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في تحديث المجموعة',
            error: error.message
        });
    }
});

// DELETE /api/groups/:id - Delete group
app.delete('/api/groups/:id', async (req, res) => {
    try {
        const pool = await getConnection();
        const { id } = req.params;

        const result = await pool.request()
            .input('id', sql.Int, id)
            .query(`
                UPDATE Groups
                SET is_deleted = 1, updated_at = GETDATE()
                WHERE id = @id AND is_deleted = 0
            `);

        if (result.rowsAffected[0] === 0) {
            return res.status(404).json({
                success: false,
                message: 'المجموعة غير موجودة'
            });
        }

        res.json({
            success: true,
            message: 'تم حذف المجموعة بنجاح'
        });
    } catch (error) {
        console.error('Error deleting group:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في حذف المجموعة',
            error: error.message
        });
    }
});

// GET /api/centers/:id/groups - Get groups of a branch
app.get('/api/centers/:id/groups', async (req, res) => {
    try {
        const pool = await getConnection();
        const { id } = req.params;
        const { gradeID, teacherID } = req.query;

        let whereClause = 'WHERE g.branch_id = @branchID AND g.is_deleted = 0';
        const params = [{ name: 'branchID', type: sql.Int, value: parseInt(id) }];

        if (gradeID) {
            whereClause += ' AND g.grade_id = @gradeID';
            params.push({ name: 'gradeID', type: sql.Int, value: parseInt(gradeID) });
        }

        if (teacherID) {
            whereClause += ' AND g.teacher_id = @teacherID';
            params.push({ name: 'teacherID', type: sql.Int, value: parseInt(teacherID) });
        }

        const query = `
            SELECT
                g.id,
                g.group_name,
                g.group_code,
                g.teacher_id,
                g.grade_id,
                g.subject_id,
                g.branch_id,
                g.institution_id,
                ISNULL(t.teacher_name, 'غير محدد') as teacher_name,
                ISNULL(t.teacher_code, '') as teacher_code,
                ISNULL(gr.grade_name, 'غير محدد') as grade_name,
                ISNULL(s.subject_name, 'غير محدد') as subject_name,
                COUNT(DISTINCT sg.student_id) as TotalStudents
            FROM Groups g
            LEFT JOIN Teachers t ON g.teacher_id = t.id AND t.is_deleted = 0
            LEFT JOIN Grades gr ON g.grade_id = gr.id AND gr.is_deleted = 0
            LEFT JOIN Subjects s ON g.subject_id = s.id AND s.is_deleted = 0
            LEFT JOIN StudentGroups sg ON g.id = sg.group_id
            ${whereClause}
            GROUP BY g.id, g.group_name, g.group_code, g.teacher_id, g.grade_id, g.subject_id,
                     g.branch_id, g.institution_id, t.teacher_name, t.teacher_code,
                     gr.grade_name, s.subject_name
            ORDER BY g.group_name
        `;

        const request = pool.request();
        params.forEach(param => {
            request.input(param.name, param.type, param.value);
        });

        const result = await request.query(query);

        res.json({
            success: true,
            data: result.recordset
        });
    } catch (error) {
        console.error('Error fetching institution groups:', error);
        res.status(500).json({
            success: false,
            message: 'Error fetching institution groups',
            error: error.message
        });
    }
});

// GET /api/subjects - Get all subjects with pagination and filters
app.get('/api/subjects', async (req, res) => {
    try {
        const pool = await getConnection();
        const { page = 1, limit = 10, search = '', institutionID, centerID, teacherID } = req.query;

        const offset = (page - 1) * limit;
        let whereClause = 'WHERE s.is_deleted = 0';
        let joinClause = '';
        let searchCondition = '';
        const params = [];

        // Support both institutionID and centerID for compatibility
        const institutionId = institutionID || centerID;
        if (institutionId) {
            whereClause += ' AND s.institution_id = @institutionID';
            params.push({ name: 'institutionID', type: sql.Int, value: parseInt(institutionId) });
        }

        if (search) {
            searchCondition = ` AND (s.subject_name LIKE '%${search}%' OR s.subject_code LIKE '%${search}%')`;
        }

        if (teacherID) {
            joinClause = ` INNER JOIN TeacherSubjects ts ON s.id = ts.subject_id`;
            whereClause += ` AND ts.teacher_id = @teacherID AND ts.is_active = 1`;
            params.push({ name: 'teacherID', type: sql.Int, value: parseInt(teacherID) });
        }

        const query = `
            SELECT TOP ${limit} * FROM (
                SELECT
                    s.id,
                    s.subject_code,
                    s.subject_name,
                    s.institution_id,
                    FORMAT(s.created_at, 'yyyy-MM-dd') as created_at,
                    s.is_active,
                    i.institution_name,
                    ROW_NUMBER() OVER (ORDER BY s.created_at DESC) as rn
                FROM Subjects s
                LEFT JOIN Institutions i ON s.institution_id = i.id
                ${joinClause}
                ${whereClause}${searchCondition}
            ) ranked
            WHERE rn > ${offset}
            ORDER BY rn
        `;

        const countQuery = `
            SELECT COUNT(DISTINCT s.id) as total
            FROM Subjects s
            LEFT JOIN Institutions i ON s.institution_id = i.id
            ${joinClause}
            ${whereClause}${searchCondition}
        `;

        const request = pool.request();
        params.forEach(param => {
            request.input(param.name, param.type, param.value);
        });

        const [result, countResult] = await Promise.all([
            request.query(query),
            request.query(countQuery)
        ]);

        res.json({
            success: true,
            data: result.recordset,
            pagination: {
                total: countResult.recordset[0].total,
                page: parseInt(page),
                limit: parseInt(limit),
                totalPages: Math.ceil(countResult.recordset[0].total / limit)
            }
        });
    } catch (error) {
        console.error('Error fetching subjects:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في جلب المواد',
            error: error.message
        });
    }
});

// POST /api/subjects - Create new subject
app.post('/api/subjects', async (req, res) => {
    try {
        const pool = await getConnection();
        const { subject_code, subject_name, institution_id } = req.body;

        if (!subject_name || !institution_id) {
            return res.status(400).json({
                success: false,
                message: 'اسم المادة والمؤسسة مطلوبان'
            });
        }

        // Generate subject code if not provided
        let finalSubjectCode = subject_code;
        if (!finalSubjectCode) {
            const codeResult = await pool.request()
                .input('institution_id', sql.Int, institution_id)
                .query(`
                    SELECT TOP 1 subject_code
                    FROM Subjects
                    WHERE institution_id = @institution_id
                    AND subject_code LIKE 'SUB%'
                    ORDER BY CAST(SUBSTRING(subject_code, 4, LEN(subject_code)) AS INT) DESC
                `);

            if (codeResult.recordset.length > 0) {
                const lastCode = codeResult.recordset[0].subject_code;
                const lastNumber = parseInt(lastCode.substring(3));
                finalSubjectCode = `SUB${String(lastNumber + 1).padStart(3, '0')}`;
            } else {
                finalSubjectCode = 'SUB001';
            }
        }

        const result = await pool.request()
            .input('subject_code', sql.NVarChar, finalSubjectCode)
            .input('subject_name', sql.NVarChar, subject_name)
            .input('institution_id', sql.Int, institution_id)
            .query(`
                INSERT INTO Subjects (subject_code, subject_name, institution_id, created_at, is_active, is_deleted)
                OUTPUT INSERTED.id, INSERTED.subject_code, INSERTED.subject_name
                VALUES (@subject_code, @subject_name, @institution_id, GETDATE(), 1, 0)
            `);

        res.json({
            success: true,
            message: 'تم إضافة المادة بنجاح',
            data: result.recordset[0]
        });
    } catch (error) {
        console.error('Error creating subject:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في إضافة المادة',
            error: error.message
        });
    }
});

// PUT /api/subjects/:id - Update subject
app.put('/api/subjects/:id', async (req, res) => {
    try {
        const pool = await getConnection();
        const { id } = req.params;
        const { subject_name } = req.body;

        if (!subject_name) {
            return res.status(400).json({
                success: false,
                message: 'اسم المادة مطلوب'
            });
        }

        const result = await pool.request()
            .input('id', sql.Int, id)
            .input('subject_name', sql.NVarChar, subject_name)
            .query(`
                UPDATE Subjects
                SET subject_name = @subject_name, updated_at = GETDATE()
                OUTPUT INSERTED.id, INSERTED.subject_code, INSERTED.subject_name
                WHERE id = @id AND is_deleted = 0
            `);

        if (result.recordset.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'المادة غير موجودة'
            });
        }

        res.json({
            success: true,
            message: 'تم تحديث المادة بنجاح',
            data: result.recordset[0]
        });
    } catch (error) {
        console.error('Error updating subject:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في تحديث المادة',
            error: error.message
        });
    }
});

// DELETE /api/subjects/:id - Delete subject
app.delete('/api/subjects/:id', async (req, res) => {
    try {
        const pool = await getConnection();
        const { id } = req.params;

        // Check if subject is used in any groups or teacher assignments
        const usageCheck = await pool.request()
            .input('id', sql.Int, id)
            .query(`
                SELECT
                    (SELECT COUNT(*) FROM Groups WHERE subject_id = @id AND is_deleted = 0) as groups_count,
                    (SELECT COUNT(*) FROM TeacherSubjects WHERE subject_id = @id AND is_active = 1) as teachers_count
            `);

        const usage = usageCheck.recordset[0];
        if (usage.groups_count > 0 || usage.teachers_count > 0) {
            return res.status(400).json({
                success: false,
                message: 'لا يمكن حذف المادة لأنها مستخدمة في مجموعات أو مرتبطة بمدرسين'
            });
        }

        const result = await pool.request()
            .input('id', sql.Int, id)
            .query(`
                UPDATE Subjects
                SET is_deleted = 1, updated_at = GETDATE()
                WHERE id = @id AND is_deleted = 0
            `);

        if (result.rowsAffected[0] === 0) {
            return res.status(404).json({
                success: false,
                message: 'المادة غير موجودة'
            });
        }

        res.json({
            success: true,
            message: 'تم حذف المادة بنجاح'
        });
    } catch (error) {
        console.error('Error deleting subject:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في حذف المادة',
            error: error.message
        });
    }
});

// GET /api/subjects/next-code - Get next subject code for institution
app.get('/api/subjects/next-code', async (req, res) => {
    try {
        const pool = await getConnection();
        const { institutionID } = req.query;

        if (!institutionID) {
            return res.status(400).json({
                success: false,
                message: 'معرف المؤسسة مطلوب'
            });
        }

        const result = await pool.request()
            .input('institution_id', sql.Int, institutionID)
            .query(`
                SELECT TOP 1 subject_code
                FROM Subjects
                WHERE institution_id = @institution_id
                AND subject_code LIKE 'SUB%'
                ORDER BY CAST(SUBSTRING(subject_code, 4, LEN(subject_code)) AS INT) DESC
            `);

        let nextCode = 'SUB001';
        if (result.recordset.length > 0) {
            const lastCode = result.recordset[0].subject_code;
            const lastNumber = parseInt(lastCode.substring(3));
            nextCode = `SUB${String(lastNumber + 1).padStart(3, '0')}`;
        }

        res.json({
            success: true,
            data: {
                nextCode: nextCode
            }
        });
    } catch (error) {
        console.error('Error getting next subject code:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في جلب الكود التالي',
            error: error.message
        });
    }
});

// GET /api/branches - Get all branches with pagination and filters
app.get('/api/branches', async (req, res) => {
    try {
        const pool = await getConnection();
        const { page = 1, limit = 10, search = '', institutionID } = req.query;

        const offset = (page - 1) * limit;
        let whereClause = 'WHERE b.is_deleted = 0';
        const params = [];

        if (institutionID) {
            whereClause += ' AND b.institution_id = @institutionID';
            params.push({ name: 'institutionID', type: sql.Int, value: parseInt(institutionID) });
        }

        if (search) {
            whereClause += ' AND (b.branch_name LIKE @search OR b.branch_code LIKE @search)';
            params.push({ name: 'search', type: sql.NVarChar, value: `%${search}%` });
        }

        const query = `
            SELECT
                b.id,
                b.branch_code,
                b.branch_name,
                b.address,
                b.phone1,
                b.phone2,
                b.institution_id,
                FORMAT(b.created_at, 'yyyy-MM-dd') as created_at,
                b.is_active,
                i.institution_name,
                (SELECT COUNT(*) FROM Students s WHERE s.branch_id = b.id AND s.is_deleted = 0) as student_count,
                (SELECT COUNT(*) FROM Teachers t INNER JOIN TeacherBranches tb ON t.id = tb.teacher_id WHERE tb.branch_id = b.id AND tb.is_active = 1) as teacher_count
            FROM Branches b
            LEFT JOIN Institutions i ON b.institution_id = i.id
            ${whereClause}
            ORDER BY b.created_at DESC
            OFFSET @offset ROWS FETCH NEXT @limit ROWS ONLY
        `;

        const countQuery = `
            SELECT COUNT(*) as total
            FROM Branches b
            ${whereClause}
        `;

        const request = pool.request()
            .input('offset', sql.Int, offset)
            .input('limit', sql.Int, parseInt(limit));

        params.forEach(param => {
            request.input(param.name, param.type, param.value);
        });

        const [result, countResult] = await Promise.all([
            request.query(query),
            request.query(countQuery)
        ]);

        res.json({
            success: true,
            data: result.recordset,
            total: countResult.recordset[0].total,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total: countResult.recordset[0].total,
                totalPages: Math.ceil(countResult.recordset[0].total / limit)
            }
        });
    } catch (error) {
        console.error('Error fetching branches:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في جلب الفروع',
            error: error.message
        });
    }
});

// GET /api/branches - Get all branches with pagination
app.get('/api/branches', async (req, res) => {
    try {
        const pool = await getConnection();
        const { page = 1, limit = 10, search = '', institutionID } = req.query;

        const offset = (page - 1) * limit;

        let whereClause = 'WHERE b.is_deleted = 0';
        const params = [];

        if (search) {
            whereClause += ` AND (b.branch_name LIKE @search OR b.branch_code LIKE @search)`;
            params.push({ name: 'search', type: sql.NVarChar, value: `%${search}%` });
        }

        if (institutionID) {
            whereClause += ' AND b.institution_id = @institutionID';
            params.push({ name: 'institutionID', type: sql.Int, value: parseInt(institutionID) });
        }

        const query = `
            SELECT b.id, b.branch_name, b.branch_code, b.institution_id, b.address, b.phone1, b.created_at
            FROM Branches b
            ${whereClause}
            ORDER BY b.branch_name
            OFFSET @offset ROWS FETCH NEXT @limit ROWS ONLY
        `;

        const countQuery = `
            SELECT COUNT(*) as total
            FROM Branches b
            ${whereClause}
        `;

        const request = pool.request()
            .input('offset', sql.Int, offset)
            .input('limit', sql.Int, parseInt(limit));

        params.forEach(param => {
            request.input(param.name, param.type, param.value);
        });

        const [result, countResult] = await Promise.all([
            request.query(query),
            request.query(countQuery)
        ]);

        res.json({
            success: true,
            data: result.recordset,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total: countResult.recordset[0].total,
                pages: Math.ceil(countResult.recordset[0].total / limit)
            }
        });
    } catch (error) {
        console.error('Error fetching branches:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في جلب الفروع',
            error: error.message
        });
    }
});

// POST /api/branches - Create new branch
app.post('/api/branches', async (req, res) => {
    try {
        const pool = await getConnection();
        const {
            branch_code,
            branch_name,
            branch_address,
            branch_phone,
            address,
            phone1,
            phone2,
            institution_id,
            description
        } = req.body;

        if (!branch_name || !institution_id) {
            return res.status(400).json({
                success: false,
                message: 'اسم الفرع والمؤسسة مطلوبان'
            });
        }

        // Check if branch name already exists in the same institution
        const checkQuery = `
            SELECT COUNT(*) as count
            FROM Branches
            WHERE branch_name = @branch_name AND institution_id = @institution_id AND is_deleted = 0
        `;

        const checkResult = await pool.request()
            .input('branch_name', sql.NVarChar, branch_name)
            .input('institution_id', sql.Int, institution_id)
            .query(checkQuery);

        if (checkResult.recordset[0].count > 0) {
            return res.status(400).json({
                success: false,
                message: 'اسم الفرع موجود بالفعل في هذه المؤسسة'
            });
        }

        const result = await pool.request()
            .input('branch_code', sql.NVarChar, branch_code)
            .input('branch_name', sql.NVarChar, branch_name)
            .input('address', sql.NVarChar, branch_address || address || null)
            .input('phone1', sql.NVarChar, branch_phone || phone1 || null)
            .input('phone2', sql.NVarChar, phone2 || null)
            .input('institution_id', sql.Int, institution_id)
            .query(`
                INSERT INTO Branches (branch_code, branch_name, address, phone1, phone2, institution_id, created_at, is_active, is_deleted)
                OUTPUT INSERTED.id, INSERTED.branch_code, INSERTED.branch_name
                VALUES (@branch_code, @branch_name, @address, @phone1, @phone2, @institution_id, GETDATE(), 1, 0)
            `);

        res.json({
            success: true,
            message: 'تم إضافة الفرع بنجاح',
            data: result.recordset[0]
        });
    } catch (error) {
        console.error('Error creating branch:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في إضافة الفرع',
            error: error.message
        });
    }
});

// PUT /api/branches/:id - Update branch
app.put('/api/branches/:id', async (req, res) => {
    try {
        const pool = await getConnection();
        const { id } = req.params;
        const {
            branch_name,
            branch_address,
            branch_phone,
            address,
            phone1,
            phone2,
            institution_id,
            is_active,
            description
        } = req.body;

        if (!branch_name || !institution_id) {
            return res.status(400).json({
                success: false,
                message: 'اسم الفرع والمؤسسة مطلوبان'
            });
        }

        // Check if branch name already exists in the same institution (excluding current branch)
        const checkQuery = `
            SELECT COUNT(*) as count
            FROM Branches
            WHERE branch_name = @branch_name AND institution_id = @institution_id
            AND id != @id AND is_deleted = 0
        `;

        const checkResult = await pool.request()
            .input('branch_name', sql.NVarChar, branch_name)
            .input('institution_id', sql.Int, institution_id)
            .input('id', sql.Int, id)
            .query(checkQuery);

        if (checkResult.recordset[0].count > 0) {
            return res.status(400).json({
                success: false,
                message: 'اسم الفرع موجود بالفعل في هذه المؤسسة'
            });
        }

        const result = await pool.request()
            .input('id', sql.Int, id)
            .input('branch_name', sql.NVarChar, branch_name)
            .input('address', sql.NVarChar, branch_address || address || null)
            .input('phone1', sql.NVarChar, branch_phone || phone1 || null)
            .input('phone2', sql.NVarChar, phone2 || null)
            .input('institution_id', sql.Int, institution_id)
            .input('is_active', sql.Bit, is_active !== undefined ? is_active : 1)
            .query(`
                UPDATE Branches
                SET branch_name = @branch_name,
                    address = @address,
                    phone1 = @phone1,
                    phone2 = @phone2,
                    institution_id = @institution_id,
                    is_active = @is_active,
                    updated_at = GETDATE()
                WHERE id = @id AND is_deleted = 0
            `);

        if (result.rowsAffected[0] === 0) {
            return res.status(404).json({
                success: false,
                message: 'الفرع غير موجود'
            });
        }

        res.json({
            success: true,
            message: 'تم تحديث الفرع بنجاح'
        });
    } catch (error) {
        console.error('Error updating branch:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في تحديث الفرع',
            error: error.message
        });
    }
});

// DELETE /api/branches/:id - Delete branch
app.delete('/api/branches/:id', async (req, res) => {
    try {
        const pool = await getConnection();
        const { id } = req.params;

        const result = await pool.request()
            .input('id', sql.Int, id)
            .query(`
                UPDATE Branches
                SET is_deleted = 1, updated_at = GETDATE()
                WHERE id = @id AND is_deleted = 0
            `);

        if (result.rowsAffected[0] === 0) {
            return res.status(404).json({
                success: false,
                message: 'الفرع غير موجود'
            });
        }

        res.json({
            success: true,
            message: 'تم حذف الفرع بنجاح'
        });
    } catch (error) {
        console.error('Error deleting branch:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في حذف الفرع',
            error: error.message
        });
    }
});

// GET /api/grades - Get all grades from database




// GET /api/grades - Get all grades with pagination and filters
app.get('/api/grades', async (req, res) => {
    try {
        const pool = await getConnection();
        const { page = 1, limit = 10, search = '', centerID, branchID } = req.query;

        const offset = (page - 1) * limit;

        let whereClause = 'WHERE g.is_deleted = 0';
        let searchCondition = '';

        if (search) {
            searchCondition = ` AND (g.grade_name LIKE '%${search}%' OR g.grade_code LIKE '%${search}%')`;
        }

        if (centerID && centerID !== '0') {
            whereClause += ` AND g.institution_id = ${centerID}`;
        }

        if (branchID && branchID !== '0') {
            whereClause += ` AND g.branch_id = ${branchID}`;
        }

        const query = `
            SELECT
                g.id,
                g.grade_code,
                g.grade_name as name,
                FORMAT(g.created_at, 'yyyy-MM-dd') as created_at,
                i.institution_name,
                b.branch_name
            FROM Grades g
            LEFT JOIN Institutions i ON g.institution_id = i.id
            LEFT JOIN Branches b ON g.branch_id = b.id
            ${whereClause}${searchCondition}
            ORDER BY g.created_at DESC
            OFFSET ${offset} ROWS
            FETCH NEXT ${limit} ROWS ONLY
        `;

        const countQuery = `
            SELECT COUNT(*) as total
            FROM Grades g
            ${whereClause}${searchCondition}
        `;

        const [result, countResult] = await Promise.all([
            pool.request().query(query),
            pool.request().query(countQuery)
        ]);

        res.json({
            success: true,
            data: result.recordset,
            total: countResult.recordset[0].total,
            page: parseInt(page),
            limit: parseInt(limit)
        });
    } catch (error) {
        console.error('Error fetching grades:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في جلب الصفوف',
            error: error.message
        });
    }
});

// POST /api/grades - Create new grade
app.post('/api/grades', async (req, res) => {
    try {
        const pool = await getConnection();
        const { grade_code, grade_name, institution_id, branch_id } = req.body;

        if (!grade_name || !institution_id) {
            return res.status(400).json({
                success: false,
                message: 'اسم الصف والمؤسسة مطلوبان'
            });
        }

        const result = await pool.request()
            .input('grade_code', sql.NVarChar, grade_code)
            .input('grade_name', sql.NVarChar, grade_name)
            .input('institution_id', sql.Int, institution_id)
            .input('branch_id', sql.Int, branch_id || null)
            .query(`
                INSERT INTO Grades (grade_code, grade_name, institution_id, branch_id, created_at, is_active, is_deleted)
                OUTPUT INSERTED.id, INSERTED.grade_code, INSERTED.grade_name
                VALUES (@grade_code, @grade_name, @institution_id, @branch_id, GETDATE(), 1, 0)
            `);

        res.json({
            success: true,
            message: 'تم إضافة الصف بنجاح',
            data: result.recordset[0]
        });
    } catch (error) {
        console.error('Error creating grade:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في إضافة الصف',
            error: error.message
        });
    }
});

// DELETE /api/grades/:id - Delete a grade
app.delete('/api/grades/:id', async (req, res) => {
    try {
        const pool = await getConnection();
        const { id } = req.params;

        // Check if grade exists
        const checkQuery = `
            SELECT id FROM Grades
            WHERE id = @id AND is_deleted = 0
        `;

        const checkResult = await pool.request()
            .input('id', sql.Int, parseInt(id))
            .query(checkQuery);

        if (checkResult.recordset.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'الصف غير موجود'
            });
        }

        // Soft delete the grade
        const deleteQuery = `
            UPDATE Grades
            SET is_deleted = 1, updated_at = GETDATE()
            WHERE id = @id
        `;

        await pool.request()
            .input('id', sql.Int, parseInt(id))
            .query(deleteQuery);

        res.json({
            success: true,
            message: 'تم حذف الصف بنجاح'
        });
    } catch (error) {
        console.error('Error deleting grade:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في حذف الصف',
            error: error.message
        });
    }
});

// DELETE /api/branches/:id - Delete a branch
app.delete('/api/branches/:id', async (req, res) => {
    try {
        const pool = await getConnection();
        const { id } = req.params;

        // Check if branch exists
        const checkQuery = `
            SELECT id FROM Branches
            WHERE id = @id AND is_deleted = 0
        `;

        const checkResult = await pool.request()
            .input('id', sql.Int, parseInt(id))
            .query(checkQuery);

        if (checkResult.recordset.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'الفرع غير موجود'
            });
        }

        // Soft delete the branch
        const deleteQuery = `
            UPDATE Branches
            SET is_deleted = 1, updated_at = GETDATE()
            WHERE id = @id
        `;

        await pool.request()
            .input('id', sql.Int, parseInt(id))
            .query(deleteQuery);

        res.json({
            success: true,
            message: 'تم حذف الفرع بنجاح'
        });
    } catch (error) {
        console.error('Error deleting branch:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في حذف الفرع',
            error: error.message
        });
    }
});

// GET /api/branches/next-code - Get next branch code for institution
app.get('/api/branches/next-code', async (req, res) => {
    try {
        const pool = await getConnection();
        const { institutionID } = req.query;

        if (!institutionID) {
            return res.status(400).json({
                success: false,
                message: 'معرف المؤسسة مطلوب'
            });
        }

        const result = await pool.request()
            .input('institutionID', sql.Int, institutionID)
            .query(`
                SELECT TOP 1 branch_code
                FROM Branches
                WHERE institution_id = @institutionID AND branch_code LIKE 'BR%'
                ORDER BY CAST(SUBSTRING(branch_code, 3, LEN(branch_code)) AS INT) DESC
            `);

        let nextCode = 'BR001';
        if (result.recordset.length > 0) {
            const lastCode = result.recordset[0].branch_code;
            const lastNumber = parseInt(lastCode.substring(2));
            const nextNumber = lastNumber + 1;
            nextCode = `BR${nextNumber.toString().padStart(3, '0')}`;
        }

        res.json({
            success: true,
            data: { nextCode }
        });
    } catch (error) {
        console.error('Error getting next branch code:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في الحصول على الكود التالي',
            error: error.message
        });
    }
});

// GET /api/branches/simple - Get all branches for dropdowns
app.get('/api/branches/simple', async (req, res) => {
    try {
        const pool = await getConnection();

        const result = await pool.request()
            .query(`
                SELECT id, branch_name as name
                FROM Branches
                WHERE is_deleted = 0
                ORDER BY branch_name
            `);

        res.json({
            success: true,
            data: result.recordset
        });
    } catch (error) {
        console.error('Error fetching branches:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في جلب الفروع',
            error: error.message
        });
    }
});

// GET /api/branches/:id - Get single branch details
app.get('/api/branches/:id', async (req, res) => {
    try {
        const pool = await getConnection();
        const { id } = req.params;

        const result = await pool.request()
            .input('id', sql.Int, parseInt(id))
            .query(`
                SELECT
                    b.id,
                    b.branch_name,
                    b.branch_code,
                    b.institution_id,
                    b.address,
                    b.phone1,
                    b.phone2,
                    b.created_at,
                    i.institution_name
                FROM Branches b
                LEFT JOIN Institutions i ON b.institution_id = i.id
                WHERE b.id = @id AND b.is_deleted = 0
            `);

        if (result.recordset.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'الفرع غير موجود'
            });
        }

        res.json({
            success: true,
            data: result.recordset[0]
        });
    } catch (error) {
        console.error('Error fetching branch:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في جلب بيانات الفرع',
            error: error.message
        });
    }
});

// GET /api/branches - Get all branches from database with pagination
app.get('/api/branches', async (req, res) => {
    try {
        const pool = await getConnection();
        const { page = 1, limit = 10, search = '', institutionID } = req.query;

        const offset = (page - 1) * limit;

        let whereClause = 'WHERE b.is_active = 1 AND b.is_deleted = 0';
        let searchCondition = '';

        if (search) {
            searchCondition = ` AND (b.branch_name LIKE '%${search}%' OR b.branch_code LIKE '%${search}%' OR b.address LIKE '%${search}%')`;
        }

        if (institutionID && institutionID !== '0') {
            whereClause += ` AND b.institution_id = ${institutionID}`;
        }

        // Get branches with proper formatting and pagination
        const query = `
            SELECT
                b.id,
                b.branch_name as name,
                b.branch_code,
                b.address,
                b.phone1,
                b.phone2,
                b.institution_id,
                FORMAT(b.created_at, 'yyyy-MM-dd') as created_at,
                i.institution_name
            FROM Branches b
            LEFT JOIN Institutions i ON b.institution_id = i.id
            ${whereClause}${searchCondition}
            ORDER BY b.created_at DESC
            OFFSET ${offset} ROWS
            FETCH NEXT ${limit} ROWS ONLY
        `;

        const countQuery = `
            SELECT COUNT(*) as total
            FROM Branches b
            ${whereClause}${searchCondition}
        `;

        const [result, countResult] = await Promise.all([
            pool.request().query(query),
            pool.request().query(countQuery)
        ]);

        res.json({
            success: true,
            data: result.recordset,
            total: countResult.recordset[0].total,
            page: parseInt(page),
            limit: parseInt(limit)
        });
    } catch (error) {
        console.error('Error fetching branches:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في جلب الفروع',
            error: error.message
        });
    }
});



// GET /api/teachers - Get all teachers with pagination and filters
app.get('/api/teachers', async (req, res) => {
    try {
        const pool = await getConnection();
        const { page = 1, limit = 10, search = '', centerID, branchID } = req.query;

        const offset = (page - 1) * limit;

        let whereClause = 'WHERE t.is_deleted = 0';
        let joinClause = '';
        let searchCondition = '';

        if (search) {
            searchCondition = ` AND (t.teacher_name LIKE '%${search}%' OR t.teacher_code LIKE '%${search}%')`;
        }

        if (centerID && centerID !== '0') {
            whereClause += ` AND t.institution_id = ${centerID}`;
        } else {
            // If no centerID provided, return empty result for security
            return res.json({
                success: true,
                data: [],
                total: 0,
                page: parseInt(page),
                limit: parseInt(limit)
            });
        }

        if (branchID && branchID !== '0') {
            joinClause = ` INNER JOIN TeacherBranches tb ON t.id = tb.teacher_id`;
            whereClause += ` AND tb.branch_id = ${branchID} AND tb.is_active = 1`;
        }

        const query = `
            SELECT TOP ${limit} * FROM (
                SELECT
                    t.id,
                    t.teacher_code,
                    t.teacher_name,
                    t.phone1 as teacher_phone,
                    t.specialization,
                    FORMAT(t.created_at, 'yyyy-MM-dd') as created_at,
                    i.institution_name,
                    STUFF((SELECT ', ' + s2.subject_name
                           FROM TeacherSubjects ts2
                           LEFT JOIN Subjects s2 ON ts2.subject_id = s2.id AND s2.is_deleted = 0
                           WHERE ts2.teacher_id = t.id AND ts2.is_active = 1
                           FOR XML PATH('')), 1, 2, '') as subjects,
                    ROW_NUMBER() OVER (ORDER BY t.created_at DESC) as rn
                FROM Teachers t
                LEFT JOIN Institutions i ON t.institution_id = i.id
                ${joinClause}
                ${whereClause}${searchCondition}
            ) ranked
            WHERE rn > ${offset}
            ORDER BY rn
        `;

        const countQuery = `
            SELECT COUNT(DISTINCT t.id) as total
            FROM Teachers t
            ${joinClause}
            ${whereClause}${searchCondition}
        `;

        const [result, countResult] = await Promise.all([
            pool.request().query(query),
            pool.request().query(countQuery)
        ]);

        res.json({
            success: true,
            data: result.recordset,
            total: countResult.recordset[0].total,
            page: parseInt(page),
            limit: parseInt(limit)
        });
    } catch (error) {
        console.error('Error fetching teachers:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في جلب المدرسين',
            error: error.message
        });
    }
});

// GET /api/teachers/next-code - Get next teacher code for institution
app.get('/api/teachers/next-code', async (req, res) => {
    try {
        const pool = await getConnection();
        const { institutionID } = req.query;

        if (!institutionID) {
            return res.status(400).json({
                success: false,
                message: 'معرف المؤسسة مطلوب'
            });
        }

        const result = await pool.request()
            .input('institutionID', sql.Int, institutionID)
            .query(`
                SELECT TOP 1 teacher_code
                FROM Teachers
                WHERE institution_id = @institutionID AND teacher_code LIKE 'TCH%'
                ORDER BY CAST(SUBSTRING(teacher_code, 4, LEN(teacher_code)) AS INT) DESC
            `);

        let nextCode = 'TCH001';
        if (result.recordset.length > 0) {
            const lastCode = result.recordset[0].teacher_code;
            const lastNumber = parseInt(lastCode.substring(3));
            const nextNumber = lastNumber + 1;
            nextCode = 'TCH' + nextNumber.toString().padStart(3, '0');
        }

        res.json({
            success: true,
            data: { nextCode }
        });
    } catch (error) {
        console.error('Error generating next teacher code:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في توليد كود المدرس',
            error: error.message
        });
    }
});

// GET /api/teachers/:id - Get single teacher details
app.get('/api/teachers/:id', async (req, res) => {
    try {
        const pool = await getConnection();
        const { id } = req.params;

        // Get teacher basic info
        const teacherQuery = `
            SELECT
                t.id, t.teacher_code, t.teacher_name,
                ISNULL(t.phone1, '') as teacher_phone,
                ISNULL(t.address, '') as address,
                ISNULL(t.specialization, '') as specialization,
                t.institution_id,
                FORMAT(t.created_at, 'yyyy-MM-dd') as created_at,
                ISNULL(i.institution_name, 'غير محدد') as institution_name
            FROM Teachers t
            LEFT JOIN Institutions i ON t.institution_id = i.id
            WHERE t.id = @id AND t.is_deleted = 0
        `;

        // Get teacher subjects
        const subjectsQuery = `
            SELECT s.id, s.subject_name
            FROM TeacherSubjects ts
            INNER JOIN Subjects s ON ts.subject_id = s.id AND s.is_deleted = 0
            WHERE ts.teacher_id = @id AND ts.is_active = 1
        `;

        // Get teacher grades
        const gradesQuery = `
            SELECT g.id, g.grade_name
            FROM TeacherGrades tg
            INNER JOIN Grades g ON tg.grade_id = g.id AND g.is_deleted = 0
            WHERE tg.teacher_id = @id AND tg.is_active = 1
        `;

        const [teacherResult, subjectsResult, gradesResult] = await Promise.all([
            pool.request().input('id', sql.Int, parseInt(id)).query(teacherQuery),
            pool.request().input('id', sql.Int, parseInt(id)).query(subjectsQuery),
            pool.request().input('id', sql.Int, parseInt(id)).query(gradesQuery)
        ]);

        if (teacherResult.recordset.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'المدرس غير موجود'
            });
        }

        const teacher = teacherResult.recordset[0];
        const subjects = subjectsResult.recordset || [];
        const grades = gradesResult.recordset || [];

        // Add subjects and grades data
        teacher.subjects = subjects.map(s => s.subject_name).join(', ');
        teacher.subject_ids = subjects.map(s => s.id);
        teacher.grades = grades.map(g => g.grade_name).join(', ');
        teacher.grade_ids = grades.map(g => g.id);

        res.json({
            success: true,
            data: teacher
        });
    } catch (error) {
        console.error('Error fetching teacher details:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في جلب تفاصيل المدرس',
            error: error.message
        });
    }
});

// PUT /api/teachers/:id - Update teacher
app.put('/api/teachers/:id', async (req, res) => {
    try {
        const pool = await getConnection();
        const { id } = req.params;
        const { teacher_name, teacher_phone, address, specialization } = req.body;

        const query = `
            UPDATE Teachers
            SET teacher_name = @teacher_name,
                teacher_phone = @teacher_phone,
                address = @address,
                specialization = @specialization,
                updated_at = GETDATE()
            WHERE id = @id AND is_deleted = 0
        `;

        const result = await pool.request()
            .input('id', sql.Int, parseInt(id))
            .input('teacher_name', sql.NVarChar, teacher_name)
            .input('teacher_phone', sql.NVarChar, teacher_phone || '')
            .input('address', sql.NVarChar, address || '')
            .input('specialization', sql.NVarChar, specialization || '')
            .query(query);

        if (result.rowsAffected[0] === 0) {
            return res.status(404).json({
                success: false,
                message: 'المدرس غير موجود'
            });
        }

        res.json({
            success: true,
            message: 'تم تحديث بيانات المدرس بنجاح'
        });
    } catch (error) {
        console.error('Error updating teacher:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في تحديث المدرس',
            error: error.message
        });
    }
});

// PUT /api/students/:id - Update student
app.put('/api/students/:id', async (req, res) => {
    try {
        const pool = await getConnection();
        const { id } = req.params;
        const {
            student_name,
            student_phone,
            father_phone,
            mother_phone,
            gender,
            branch_id,
            grade_id,
            is_exempt_from_fees,
            is_exempt_from_books,
            is_active
        } = req.body;

        const query = `
            UPDATE Students
            SET student_name = @student_name,
                student_phone = @student_phone,
                father_phone = @father_phone,
                mother_phone = @mother_phone,
                gender = @gender,
                branch_id = @branch_id,
                grade_id = @grade_id,
                is_exempt_from_fees = @is_exempt_from_fees,
                is_exempt_from_books = @is_exempt_from_books,
                is_active = @is_active,
                updated_at = GETDATE()
            WHERE id = @id AND is_deleted = 0
        `;

        const result = await pool.request()
            .input('id', sql.Int, parseInt(id))
            .input('student_name', sql.NVarChar, student_name)
            .input('student_phone', sql.NVarChar, student_phone || '')
            .input('father_phone', sql.NVarChar, father_phone || '')
            .input('mother_phone', sql.NVarChar, mother_phone || '')
            .input('gender', sql.NVarChar, gender)
            .input('branch_id', sql.Int, branch_id || null)
            .input('grade_id', sql.Int, grade_id || null)
            .input('is_exempt_from_fees', sql.Bit, is_exempt_from_fees || false)
            .input('is_exempt_from_books', sql.Bit, is_exempt_from_books || false)
            .input('is_active', sql.Bit, is_active !== undefined ? is_active : true)
            .query(query);

        if (result.rowsAffected[0] === 0) {
            return res.status(404).json({
                success: false,
                message: 'الطالب غير موجود'
            });
        }

        res.json({
            success: true,
            message: 'تم تحديث بيانات الطالب بنجاح'
        });
    } catch (error) {
        console.error('Error updating student:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في تحديث الطالب',
            error: error.message
        });
    }
});



// POST /api/teachers - Create new teacher
app.post('/api/teachers', async (req, res) => {
    try {
        const pool = await getConnection();

        const teacherData = req.body;

        // Validate required fields
        if (!teacherData.teacher_name || !teacherData.institution_id) {
            return res.status(400).json({
                success: false,
                message: 'اسم المدرس والمؤسسة مطلوبان'
            });
        }

        // Validate teacher name length
        if (teacherData.teacher_name.length < 2) {
            return res.status(400).json({
                success: false,
                message: 'اسم المدرس يجب أن يكون أكثر من حرفين'
            });
        }

        if (teacherData.teacher_name.length > 255) {
            return res.status(400).json({
                success: false,
                message: 'اسم المدرس يجب أن يكون أقل من 255 حرف'
            });
        }

        // Check for duplicate teacher name in the same institution
        const nameCheckResult = await pool.request()
            .input('teacher_name', sql.NVarChar, teacherData.teacher_name)
            .input('institution_id', sql.Int, teacherData.institution_id)
            .query(`
                SELECT teacher_name, teacher_code
                FROM Teachers
                WHERE teacher_name = @teacher_name
                AND institution_id = @institution_id
                AND is_deleted = 0
            `);

        if (nameCheckResult.recordset.length > 0) {
            const existingTeacher = nameCheckResult.recordset[0];
            return res.status(400).json({
                success: false,
                message: `المدرس "${existingTeacher.teacher_name}" مسجل مسبقاً بالكود: ${existingTeacher.teacher_code}`
            });
        }

        // Check for duplicate phone number if provided
        if (teacherData.teacher_phone) {
            const phoneCheckResult = await pool.request()
                .input('teacher_phone', sql.NVarChar, teacherData.teacher_phone)
                .input('institution_id', sql.Int, teacherData.institution_id)
                .query(`
                    SELECT teacher_name, teacher_code
                    FROM Teachers
                    WHERE phone1 = @teacher_phone
                    AND institution_id = @institution_id
                    AND is_deleted = 0
                `);

            if (phoneCheckResult.recordset.length > 0) {
                const existingTeacher = phoneCheckResult.recordset[0];
                return res.status(400).json({
                    success: false,
                    message: `رقم الهاتف مسجل مسبقاً للمدرس "${existingTeacher.teacher_name}" بالكود: ${existingTeacher.teacher_code}`
                });
            }

            // Validate phone number format
            const phoneRegex = /^[0-9+\-\s()]*$/;
            if (!phoneRegex.test(teacherData.teacher_phone)) {
                return res.status(400).json({
                    success: false,
                    message: 'رقم الهاتف غير صحيح'
                });
            }
        }

        // Validate that branches belong to the institution if branches are provided
        if (teacherData.branch_ids && teacherData.branch_ids.length > 0) {
            for (const branchId of teacherData.branch_ids) {
                const branchCheckResult = await pool.request()
                    .input('branch_id', sql.Int, branchId)
                    .input('institution_id', sql.Int, teacherData.institution_id)
                    .query(`
                        SELECT id
                        FROM Branches
                        WHERE id = @branch_id
                        AND institution_id = @institution_id
                        AND is_active = 1 AND is_deleted = 0
                    `);

                if (branchCheckResult.recordset.length === 0) {
                    return res.status(400).json({
                        success: false,
                        message: 'أحد الفروع المختارة غير صحيح أو لا ينتمي للمؤسسة'
                    });
                }
            }
        }

        // Generate teacher code automatically
        const codeResult = await pool.request()
            .input('institution_id', sql.Int, teacherData.institution_id)
            .query(`
                SELECT TOP 1 teacher_code
                FROM Teachers
                WHERE institution_id = @institution_id AND teacher_code LIKE 'TCH%'
                ORDER BY CAST(SUBSTRING(teacher_code, 4, LEN(teacher_code)) AS INT) DESC
            `);

        let nextCode = 'TCH001';
        if (codeResult.recordset.length > 0) {
            const lastCode = codeResult.recordset[0].teacher_code;
            const lastNumber = parseInt(lastCode.substring(3));
            nextCode = `TCH${String(lastNumber + 1).padStart(3, '0')}`;
        }

        // Update teacher data with generated code
        teacherData.teacher_code = nextCode;

        const query = `
            INSERT INTO Teachers (
                teacher_code, teacher_name, phone1, address, specialization,
                institution_id, branch_id, created_at, updated_at, is_active, is_deleted
            ) VALUES (
                @teacher_code, @teacher_name, @teacher_phone, @address, @specialization,
                @institution_id, @branch_id, GETDATE(), GETDATE(), 1, 0
            );
            SELECT SCOPE_IDENTITY() as TeacherID;
        `;

        const request = pool.request();
        request.input('teacher_code', sql.NVarChar, teacherData.teacher_code);
        request.input('teacher_name', sql.NVarChar, teacherData.teacher_name);
        request.input('teacher_phone', sql.NVarChar, teacherData.teacher_phone || null);
        request.input('address', sql.NVarChar, teacherData.address || null);
        request.input('specialization', sql.NVarChar, teacherData.specialization || null);
        request.input('institution_id', sql.Int, teacherData.institution_id);
        request.input('branch_id', sql.Int, teacherData.branch_id || null);

        const result = await request.query(query);
        const teacherID = result.recordset[0].TeacherID;

        // Insert teacher-branch relationships if branches are selected
        if (teacherData.branch_ids && teacherData.branch_ids.length > 0) {
            for (const branchId of teacherData.branch_ids) {
                await pool.request()
                    .input('teacher_id', sql.Int, teacherID)
                    .input('branch_id', sql.Int, branchId)
                    .query(`
                        INSERT INTO TeacherBranches (teacher_id, branch_id, created_at, is_active)
                        VALUES (@teacher_id, @branch_id, GETDATE(), 1)
                    `);
            }
        }

        // Insert teacher-subject relationships if subjects are selected
        if (teacherData.subject_ids && teacherData.subject_ids.length > 0) {
            for (const subjectId of teacherData.subject_ids) {
                await pool.request()
                    .input('teacher_id', sql.Int, teacherID)
                    .input('subject_id', sql.Int, subjectId)
                    .query(`
                        INSERT INTO TeacherSubjects (teacher_id, subject_id, created_at, is_active)
                        VALUES (@teacher_id, @subject_id, GETDATE(), 1)
                    `);
            }
        }

        // Insert teacher-grade relationships if grades are selected
        if (teacherData.grade_ids && teacherData.grade_ids.length > 0) {
            for (const gradeId of teacherData.grade_ids) {
                await pool.request()
                    .input('teacher_id', sql.Int, teacherID)
                    .input('grade_id', sql.Int, gradeId)
                    .query(`
                        INSERT INTO TeacherGrades (teacher_id, grade_id, created_at, is_active)
                        VALUES (@teacher_id, @grade_id, GETDATE(), 1)
                    `);
            }
        }

        res.status(201).json({
            success: true,
            message: 'Teacher created successfully',
            data: { id: teacherID, ...teacherData }
        });
    } catch (error) {
        console.error('Error creating teacher:', error);
        res.status(500).json({
            success: false,
            message: 'Error creating teacher',
            error: error.message
        });
    }
});

// DELETE /api/teachers/:id - Delete teacher
app.delete('/api/teachers/:id', async (req, res) => {
    try {
        const pool = await getConnection();
        const { id } = req.params;

        const result = await pool.request()
            .input('id', sql.Int, id)
            .query(`
                UPDATE Teachers
                SET is_deleted = 1, updated_at = GETDATE()
                WHERE id = @id AND is_deleted = 0
            `);

        if (result.rowsAffected[0] === 0) {
            return res.status(404).json({
                success: false,
                message: 'المدرس غير موجود'
            });
        }

        res.json({
            success: true,
            message: 'تم حذف المدرس بنجاح'
        });
    } catch (error) {
        console.error('Error deleting teacher:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في حذف المدرس',
            error: error.message
        });
    }
});

// GET /api/dashboard/stats - Get dashboard statistics
app.get('/api/dashboard/stats', async (req, res) => {
    try {
        const pool = await getConnection();
        const { institutionID } = req.query;

        let whereClause = 'WHERE is_deleted = 0';
        const params = [];

        if (institutionID) {
            whereClause += ' AND institution_id = @institutionID';
            params.push({ name: 'institutionID', type: sql.Int, value: parseInt(institutionID) });
        }

        const queries = {
            students: `SELECT COUNT(*) as count FROM Students ${whereClause}`,
            institutions: 'SELECT COUNT(*) as count FROM Institutions WHERE is_deleted = 0',
            branches: `SELECT COUNT(*) as count FROM Branches ${whereClause}`,
            teachers: `SELECT COUNT(*) as count FROM Teachers ${whereClause}`
        };

        const request1 = pool.request();
        const request2 = pool.request();
        const request3 = pool.request();
        const request4 = pool.request();

        params.forEach(param => {
            request1.input(param.name, param.type, param.value);
            request3.input(param.name, param.type, param.value);
            request4.input(param.name, param.type, param.value);
        });

        const results = await Promise.all([
            request1.query(queries.students),
            request2.query(queries.institutions),
            request3.query(queries.branches),
            request4.query(queries.teachers)
        ]);

        res.json({
            success: true,
            data: {
                students: results[0].recordset[0].count,
                institutions: results[1].recordset[0].count,
                branches: results[2].recordset[0].count,
                teachers: results[3].recordset[0].count
            }
        });
    } catch (error) {
        console.error('Error fetching dashboard stats:', error);
        res.status(500).json({
            success: false,
            message: 'Error fetching dashboard stats',
            error: error.message
        });
    }
});

// GET /api/students/next-code - Get next student code (must be before /:id route)
app.get('/api/students/next-code', async (req, res) => {
    try {
        const pool = await getConnection();

        const { institutionID } = req.query;

        if (!institutionID) {
            return res.status(400).json({
                success: false,
                message: 'معرف المؤسسة مطلوب'
            });
        }

        const result = await pool.request()
            .input('institutionID', sql.Int, institutionID)
            .query(`
                SELECT TOP 1 student_code
                FROM Students
                WHERE institution_id = @institutionID AND student_code LIKE 'STU%'
                ORDER BY CAST(SUBSTRING(student_code, 4, LEN(student_code)) AS INT) DESC
            `);

        let nextCode = 'STU001';
        if (result.recordset.length > 0) {
            const lastCode = result.recordset[0].student_code;
            const lastNumber = parseInt(lastCode.substring(3));
            nextCode = `STU${String(lastNumber + 1).padStart(3, '0')}`;
        }

        res.json({
            success: true,
            data: { nextCode }
        });
    } catch (error) {
        console.error('Error getting next student code:', error);
        res.status(500).json({
            success: false,
            message: 'Error getting next student code',
            error: error.message
        });
    }
});

// DELETE /api/students/:id - Delete student
app.delete('/api/students/:id', async (req, res) => {
    try {
        const pool = await getConnection();
        const { id } = req.params;

        const result = await pool.request()
            .input('id', sql.Int, id)
            .query(`
                UPDATE Students
                SET is_deleted = 1, updated_at = GETDATE()
                WHERE id = @id AND is_deleted = 0
            `);

        if (result.rowsAffected[0] === 0) {
            return res.status(404).json({
                success: false,
                message: 'الطالب غير موجود'
            });
        }

        res.json({
            success: true,
            message: 'تم حذف الطالب بنجاح'
        });
    } catch (error) {
        console.error('Error deleting student:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في حذف الطالب',
            error: error.message
        });
    }
});

// GET /api/students/:id - Get single student details
app.get('/api/students/:id', async (req, res) => {
    try {
        const pool = await getConnection();
        const { id } = req.params;

        // Get basic student information
        const studentQuery = `
            SELECT
                s.id, s.student_code, s.student_name,
                ISNULL(s.student_phone, '') as student_phone,
                ISNULL(s.father_phone, '') as father_phone,
                ISNULL(s.mother_phone, '') as mother_phone,
                ISNULL(s.gender, 'male') as gender,
                s.institution_id,
                s.branch_id,
                s.grade_id,
                s.group_id,
                ISNULL(s.is_exempt_from_fees, 0) as is_exempt_from_fees,
                ISNULL(s.is_exempt_from_books, 0) as is_exempt_from_books,
                s.created_at,
                s.updated_at,
                ISNULL(i.institution_name, 'غير محدد') as institution_name,
                ISNULL(b.branch_name, 'غير محدد') as branch_name,
                ISNULL(g.grade_name, 'غير محدد') as grade_name
            FROM Students s
            LEFT JOIN Institutions i ON s.institution_id = i.id
            LEFT JOIN Branches b ON s.branch_id = b.id
            LEFT JOIN Grades g ON s.grade_id = g.id
            WHERE s.id = @id AND s.is_deleted = 0
        `;

        const studentResult = await pool.request()
            .input('id', sql.Int, id)
            .query(studentQuery);

        if (studentResult.recordset.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'الطالب غير موجود'
            });
        }

        const student = studentResult.recordset[0];

        // Get student's groups
        const groupsQuery = `
            SELECT DISTINCT
                g.id, g.group_name,
                t.teacher_name, t.teacher_code,
                s.subject_name
            FROM StudentGroups sg
            INNER JOIN Groups g ON sg.group_id = g.id
            LEFT JOIN Teachers t ON g.teacher_id = t.id
            LEFT JOIN Subjects s ON g.subject_id = s.id
            WHERE sg.student_id = @id
            AND g.is_active = 1 AND g.is_deleted = 0
        `;

        const groupsResult = await pool.request()
            .input('id', sql.Int, id)
            .query(groupsQuery);

        // Get student's teachers
        const teachersQuery = `
            SELECT DISTINCT
                t.id, t.teacher_name, t.teacher_code, t.specialization
            FROM StudentTeachers st
            INNER JOIN Teachers t ON st.teacher_id = t.id
            WHERE st.student_id = @id
            AND st.is_active = 1
            AND t.is_active = 1 AND t.is_deleted = 0
        `;

        const teachersResult = await pool.request()
            .input('id', sql.Int, id)
            .query(teachersQuery);

        // Combine all data
        const studentDetails = {
            ...student,
            groups: groupsResult.recordset,
            teachers: teachersResult.recordset
        };

        res.json({
            success: true,
            data: studentDetails
        });
    } catch (error) {
        console.error('Error fetching student details:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في جلب تفاصيل الطالب',
            error: error.message
        });
    }
});

/**
 * @swagger
 * /api/auth/login:
 *   post:
 *     summary: تسجيل دخول المستخدم
 *     description: مصادقة المستخدم وإرجاع معلومات المستخدم مع رمز الوصول
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/LoginRequest'
 *           example:
 *             username: "admin"
 *             password: "password123"
 *     responses:
 *       200:
 *         description: تم تسجيل الدخول بنجاح
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "تم تسجيل الدخول بنجاح"
 *                 data:
 *                   type: object
 *                   properties:
 *                     user:
 *                       $ref: '#/components/schemas/User'
 *                     token:
 *                       type: string
 *                       example: "mock-jwt-token-1234567890"
 *       400:
 *         description: بيانات غير صحيحة
 *       401:
 *         description: اسم المستخدم أو كلمة المرور غير صحيحة
 *       500:
 *         description: خطأ في الخادم
 */
// POST /api/auth/login - User authentication
app.post('/api/auth/login', async (req, res) => {
    try {
        const pool = await getConnection();
        const { username, password, institution_id } = req.body;

        // Check if Users table exists, if not create it
        try {
            await pool.request().query(`
                IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Users' AND xtype='U')
                CREATE TABLE Users (
                    id INT IDENTITY(1,1) PRIMARY KEY,
                    username NVARCHAR(50) UNIQUE NOT NULL,
                    password NVARCHAR(255) NOT NULL,
                    role NVARCHAR(20) DEFAULT 'user',
                    isGlobalAdmin BIT DEFAULT 0,
                    institution_id INT NULL,
                    created_at DATETIME DEFAULT GETDATE(),
                    is_active BIT DEFAULT 1,
                    FOREIGN KEY (institution_id) REFERENCES Institutions(id)
                )
            `);

            // Insert default users if table is empty
            const userCount = await pool.request().query('SELECT COUNT(*) as count FROM Users');
            if (userCount.recordset[0].count === 0) {
                await pool.request().query(`
                    INSERT INTO Users (username, password, role, isGlobalAdmin, institution_id) VALUES
                    ('admin', 'admin', 'admin', 1, NULL),
                    ('user', 'user', 'user', 0, 1)
                `);
            }
        } catch (createError) {
            console.log('Users table already exists or creation failed:', createError.message);
        }

        // Authenticate user from database
        const userQuery = `
            SELECT u.*, i.institution_name
            FROM Users u
            LEFT JOIN Institutions i ON u.institution_id = i.id
            WHERE u.username = @username AND u.password = @password AND u.is_active = 1
        `;

        const userResult = await pool.request()
            .input('username', sql.NVarChar, username)
            .input('password', sql.NVarChar, password)
            .query(userQuery);

        if (userResult.recordset.length === 0) {
            return res.status(401).json({
                success: false,
                message: 'اسم المستخدم أو كلمة المرور غير صحيحة'
            });
        }

        const user = userResult.recordset[0];

        // If user is not global admin and institution_id is provided, update it
        if (!user.isGlobalAdmin && institution_id && institution_id !== user.institution_id) {
            const institutionQuery = 'SELECT institution_name FROM Institutions WHERE id = @institutionID';
            const institutionResult = await pool.request()
                .input('institutionID', sql.Int, institution_id)
                .query(institutionQuery);

            if (institutionResult.recordset.length > 0) {
                user.institution_id = institution_id;
                user.institution_name = institutionResult.recordset[0].institution_name;
            }
        }

        res.json({
            success: true,
            user: {
                id: user.id,
                username: user.username,
                role: user.role,
                isGlobalAdmin: !!user.isGlobalAdmin,
                institution_id: user.institution_id,
                institution_name: user.institution_name || 'جميع المؤسسات'
            },
            token: 'dummy-token-' + user.id,
            message: 'تم تسجيل الدخول بنجاح'
        });
    } catch (error) {
        console.error('Error during login:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في تسجيل الدخول',
            error: error.message
        });
    }
});

// ==================== ATTENDANCE APIs ====================

// GET /api/attendance - Get attendance records with filters
app.get('/api/attendance', async (req, res) => {
    try {
        const pool = await getConnection();
        const { page = 1, limit = 10, studentId, groupId, teacherId, date, status } = req.query;

        const offset = (page - 1) * limit;
        let whereClause = 'WHERE 1=1';
        const params = [];

        if (studentId) {
            whereClause += ' AND a.student_id = @studentId';
            params.push({ name: 'studentId', type: sql.Int, value: parseInt(studentId) });
        }

        if (groupId) {
            whereClause += ' AND a.group_id = @groupId';
            params.push({ name: 'groupId', type: sql.Int, value: parseInt(groupId) });
        }

        if (teacherId) {
            whereClause += ' AND a.teacher_id = @teacherId';
            params.push({ name: 'teacherId', type: sql.Int, value: parseInt(teacherId) });
        }

        if (date) {
            whereClause += ' AND a.attendance_date = @date';
            params.push({ name: 'date', type: sql.Date, value: date });
        }

        if (status) {
            whereClause += ' AND a.status = @status';
            params.push({ name: 'status', type: sql.NVarChar, value: status });
        }

        const query = `
            SELECT
                a.id,
                a.attendance_date,
                'present' as status,
                '' as notes,
                s.student_name,
                s.student_code,
                '' as group_name,
                '' as teacher_name,
                '' as subject_name
            FROM Attendance a
            LEFT JOIN Students s ON a.student_id = s.id
            ${whereClause}
            ORDER BY a.attendance_date DESC
            OFFSET @offset ROWS FETCH NEXT @limit ROWS ONLY
        `;

        const countQuery = `
            SELECT COUNT(*) as total
            FROM Attendance a
            ${whereClause}
        `;

        const request = pool.request()
            .input('offset', sql.Int, offset)
            .input('limit', sql.Int, parseInt(limit));

        params.forEach(param => {
            request.input(param.name, param.type, param.value);
        });

        const [result, countResult] = await Promise.all([
            request.query(query),
            request.query(countQuery)
        ]);

        res.json({
            success: true,
            data: result.recordset,
            pagination: {
                total: countResult.recordset[0].total,
                page: parseInt(page),
                limit: parseInt(limit),
                totalPages: Math.ceil(countResult.recordset[0].total / limit)
            }
        });
    } catch (error) {
        console.error('Error fetching attendance:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في جلب سجلات الحضور',
            error: error.message
        });
    }
});

// POST /api/attendance - Create new attendance record
app.post('/api/attendance', async (req, res) => {
    try {
        const pool = await getConnection();
        const { student_id, group_id, attendance_date, status = 'present' } = req.body;

        if (!student_id || !attendance_date) {
            return res.status(400).json({
                success: false,
                message: 'معرف الطالب وتاريخ الحضور مطلوبان'
            });
        }

        // Check if attendance already exists for this student and date
        const existingCheck = await pool.request()
            .input('student_id', sql.Int, student_id)
            .input('attendance_date', sql.Date, attendance_date)
            .query(`
                SELECT id FROM Attendance
                WHERE student_id = @student_id AND attendance_date = @attendance_date
            `);

        if (existingCheck.recordset.length > 0) {
            return res.status(400).json({
                success: false,
                message: 'تم تسجيل حضور هذا الطالب مسبقاً لهذا التاريخ'
            });
        }

        const result = await pool.request()
            .input('student_id', sql.Int, student_id)
            .input('group_id', sql.Int, group_id || null)
            .input('attendance_date', sql.Date, attendance_date)
            .input('institution_id', sql.Int, 1) // Default institution
            .input('is_present', sql.Bit, 1) // Default present
            .query(`
                INSERT INTO Attendance (student_id, group_id, attendance_date, institution_id, is_present)
                OUTPUT INSERTED.id
                VALUES (@student_id, @group_id, @attendance_date, @institution_id, @is_present)
            `);

        res.json({
            success: true,
            message: 'تم تسجيل الحضور بنجاح',
            data: { id: result.recordset[0].id }
        });
    } catch (error) {
        console.error('Error creating attendance:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في تسجيل الحضور',
            error: error.message
        });
    }
});

// ==================== BOOKLETS APIs ====================

// GET /api/booklets - Get all booklets with pagination and filters
app.get('/api/booklets', async (req, res) => {
    try {
        const pool = await getConnection();
        const { page = 1, limit = 10, search = '', institutionID, gradeId, subjectId, teacherId } = req.query;

        const offset = (page - 1) * limit;
        let whereClause = 'WHERE b.is_deleted = 0';
        const params = [];

        if (institutionID) {
            whereClause += ' AND b.institution_id = @institutionID';
            params.push({ name: 'institutionID', type: sql.Int, value: parseInt(institutionID) });
        }

        if (search) {
            whereClause += ' AND (b.booklet_name LIKE @search OR b.booklet_code LIKE @search)';
            params.push({ name: 'search', type: sql.NVarChar, value: `%${search}%` });
        }

        if (gradeId) {
            whereClause += ' AND b.grade_id = @gradeId';
            params.push({ name: 'gradeId', type: sql.Int, value: parseInt(gradeId) });
        }

        if (subjectId) {
            whereClause += ' AND b.subject_id = @subjectId';
            params.push({ name: 'subjectId', type: sql.Int, value: parseInt(subjectId) });
        }

        if (teacherId) {
            whereClause += ' AND b.teacher_id = @teacherId';
            params.push({ name: 'teacherId', type: sql.Int, value: parseInt(teacherId) });
        }

        const query = `
            SELECT TOP ${limit} * FROM (
                SELECT
                    b.id,
                    b.booklet_code,
                    b.booklet_name,
                    b.unit_price,
                    b.total_quantity,
                    b.available_quantity,
                    b.description,
                    s.subject_name,
                    g.grade_name,
                    t.teacher_name,
                    i.institution_name,
                    FORMAT(b.created_at, 'yyyy-MM-dd') as created_at,
                    ROW_NUMBER() OVER (ORDER BY b.created_at DESC) as rn
                FROM Booklets b
                LEFT JOIN Subjects s ON b.subject_id = s.id
                LEFT JOIN Grades g ON b.grade_id = g.id
                LEFT JOIN Teachers t ON b.teacher_id = t.id
                LEFT JOIN Institutions i ON b.institution_id = i.id
                ${whereClause}
            ) ranked
            WHERE rn > ${offset}
            ORDER BY rn
        `;

        const countQuery = `
            SELECT COUNT(*) as total
            FROM Booklets b
            ${whereClause}
        `;

        const request = pool.request();
        params.forEach(param => {
            request.input(param.name, param.type, param.value);
        });

        const [result, countResult] = await Promise.all([
            request.query(query),
            request.query(countQuery)
        ]);

        res.json({
            success: true,
            data: result.recordset,
            pagination: {
                total: countResult.recordset[0].total,
                page: parseInt(page),
                limit: parseInt(limit),
                totalPages: Math.ceil(countResult.recordset[0].total / limit)
            }
        });
    } catch (error) {
        console.error('Error fetching booklets:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في جلب الملازم',
            error: error.message
        });
    }
});

// POST /api/booklets - Create new booklet
app.post('/api/booklets', async (req, res) => {
    try {
        const pool = await getConnection();
        const { booklet_name, subject_id, grade_id, teacher_id, institution_id, unit_price, total_quantity, description } = req.body;

        if (!booklet_name || !subject_id || !grade_id || !teacher_id || !institution_id || !unit_price || !total_quantity) {
            return res.status(400).json({
                success: false,
                message: 'جميع البيانات الأساسية مطلوبة'
            });
        }

        // Generate booklet code
        const codeResult = await pool.request()
            .input('institution_id', sql.Int, institution_id)
            .query(`
                SELECT TOP 1 booklet_code
                FROM Booklets
                WHERE institution_id = @institution_id AND booklet_code LIKE 'BK%'
                ORDER BY CAST(SUBSTRING(booklet_code, 3, LEN(booklet_code)) AS INT) DESC
            `);

        let nextCode = 'BK001';
        if (codeResult.recordset.length > 0) {
            const lastCode = codeResult.recordset[0].booklet_code;
            const lastNumber = parseInt(lastCode.substring(2));
            nextCode = `BK${String(lastNumber + 1).padStart(3, '0')}`;
        }

        const result = await pool.request()
            .input('booklet_code', sql.NVarChar, nextCode)
            .input('booklet_name', sql.NVarChar, booklet_name)
            .input('subject_id', sql.Int, subject_id)
            .input('grade_id', sql.Int, grade_id)
            .input('teacher_id', sql.Int, teacher_id)
            .input('institution_id', sql.Int, institution_id)
            .input('unit_price', sql.Decimal, unit_price)
            .input('total_quantity', sql.Int, total_quantity)
            .input('available_quantity', sql.Int, total_quantity)
            .input('description', sql.NVarChar, description || null)
            .query(`
                INSERT INTO Booklets (booklet_code, booklet_name, subject_id, grade_id, teacher_id, institution_id, unit_price, total_quantity, available_quantity, description)
                OUTPUT INSERTED.id, INSERTED.booklet_code, INSERTED.booklet_name
                VALUES (@booklet_code, @booklet_name, @subject_id, @grade_id, @teacher_id, @institution_id, @unit_price, @total_quantity, @available_quantity, @description)
            `);

        res.json({
            success: true,
            message: 'تم إضافة الملزمة بنجاح',
            data: result.recordset[0]
        });
    } catch (error) {
        console.error('Error creating booklet:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في إضافة الملزمة',
            error: error.message
        });
    }
});

// ==================== BOOKLET DELIVERIES APIs ====================

// GET /api/booklet-deliveries - Get booklet deliveries with pagination and filters
app.get('/api/booklet-deliveries', async (req, res) => {
    try {
        const pool = await getConnection();
        const { page = 1, limit = 10, studentId, bookletId, supervisorId, paymentStatus } = req.query;

        const offset = (page - 1) * limit;
        let whereClause = 'WHERE bd.is_deleted = 0';
        const params = [];

        if (studentId) {
            whereClause += ' AND bd.student_id = @studentId';
            params.push({ name: 'studentId', type: sql.Int, value: parseInt(studentId) });
        }

        if (bookletId) {
            whereClause += ' AND bd.booklet_id = @bookletId';
            params.push({ name: 'bookletId', type: sql.Int, value: parseInt(bookletId) });
        }

        if (supervisorId) {
            whereClause += ' AND bd.supervisor_id = @supervisorId';
            params.push({ name: 'supervisorId', type: sql.Int, value: parseInt(supervisorId) });
        }

        if (paymentStatus) {
            whereClause += ' AND bd.payment_status = @paymentStatus';
            params.push({ name: 'paymentStatus', type: sql.NVarChar, value: paymentStatus });
        }

        const query = `
            SELECT TOP ${limit} * FROM (
                SELECT
                    bd.id,
                    bd.delivery_code,
                    bd.quantity,
                    bd.unit_price,
                    bd.total_amount,
                    bd.paid_amount,
                    bd.remaining_amount,
                    bd.payment_status,
                    bd.delivery_date,
                    bd.notes,
                    s.student_name,
                    s.student_code,
                    b.booklet_name,
                    b.booklet_code,
                    t.teacher_name as supervisor_name,
                    ROW_NUMBER() OVER (ORDER BY bd.delivery_date DESC) as rn
                FROM BookletDeliveries bd
                INNER JOIN Students s ON bd.student_id = s.id
                INNER JOIN Booklets b ON bd.booklet_id = b.id
                INNER JOIN Teachers t ON bd.supervisor_id = t.id
                ${whereClause}
            ) ranked
            WHERE rn > ${offset}
            ORDER BY rn
        `;

        const countQuery = `
            SELECT COUNT(*) as total
            FROM BookletDeliveries bd
            ${whereClause}
        `;

        const request = pool.request();
        params.forEach(param => {
            request.input(param.name, param.type, param.value);
        });

        const [result, countResult] = await Promise.all([
            request.query(query),
            request.query(countQuery)
        ]);

        res.json({
            success: true,
            data: result.recordset,
            pagination: {
                total: countResult.recordset[0].total,
                page: parseInt(page),
                limit: parseInt(limit),
                totalPages: Math.ceil(countResult.recordset[0].total / limit)
            }
        });
    } catch (error) {
        console.error('Error fetching booklet deliveries:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في جلب سجلات استلام الملازم',
            error: error.message
        });
    }
});

// POST /api/booklet-deliveries - Create new booklet delivery
app.post('/api/booklet-deliveries', async (req, res) => {
    try {
        const pool = await getConnection();
        const { booklet_id, student_id, supervisor_id, quantity = 1, paid_amount = 0, notes } = req.body;

        if (!booklet_id || !student_id || !supervisor_id) {
            return res.status(400).json({
                success: false,
                message: 'الملزمة والطالب والمشرف مطلوبون'
            });
        }

        // Get booklet details
        const bookletResult = await pool.request()
            .input('booklet_id', sql.Int, booklet_id)
            .query(`
                SELECT unit_price, available_quantity, booklet_name
                FROM Booklets
                WHERE id = @booklet_id AND is_deleted = 0
            `);

        if (bookletResult.recordset.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'الملزمة غير موجودة'
            });
        }

        const booklet = bookletResult.recordset[0];

        if (booklet.available_quantity < quantity) {
            return res.status(400).json({
                success: false,
                message: `الكمية المتاحة غير كافية. المتاح: ${booklet.available_quantity}`
            });
        }

        const totalAmount = booklet.unit_price * quantity;
        const remainingAmount = totalAmount - paid_amount;

        // Generate delivery code
        const codeResult = await pool.request().query(`
            SELECT TOP 1 delivery_code
            FROM BookletDeliveries
            WHERE delivery_code LIKE 'DEL%'
            ORDER BY CAST(SUBSTRING(delivery_code, 4, LEN(delivery_code)) AS INT) DESC
        `);

        let nextCode = 'DEL001';
        if (codeResult.recordset.length > 0) {
            const lastCode = codeResult.recordset[0].delivery_code;
            const lastNumber = parseInt(lastCode.substring(3));
            nextCode = `DEL${String(lastNumber + 1).padStart(3, '0')}`;
        }

        // Create delivery record
        const result = await pool.request()
            .input('delivery_code', sql.NVarChar, nextCode)
            .input('booklet_id', sql.Int, booklet_id)
            .input('student_id', sql.Int, student_id)
            .input('supervisor_id', sql.Int, supervisor_id)
            .input('quantity', sql.Int, quantity)
            .input('unit_price', sql.Decimal, booklet.unit_price)
            .input('total_amount', sql.Decimal, totalAmount)
            .input('paid_amount', sql.Decimal, paid_amount)
            .input('remaining_amount', sql.Decimal, remainingAmount)
            .input('payment_status', sql.NVarChar, remainingAmount > 0 ? 'partial' : 'paid')
            .input('notes', sql.NVarChar, notes || null)
            .query(`
                INSERT INTO BookletDeliveries (delivery_code, booklet_id, student_id, supervisor_id, quantity, unit_price, total_amount, paid_amount, remaining_amount, payment_status, notes)
                OUTPUT INSERTED.id, INSERTED.delivery_code
                VALUES (@delivery_code, @booklet_id, @student_id, @supervisor_id, @quantity, @unit_price, @total_amount, @paid_amount, @remaining_amount, @payment_status, @notes)
            `);

        // Update booklet available quantity
        await pool.request()
            .input('booklet_id', sql.Int, booklet_id)
            .input('quantity', sql.Int, quantity)
            .query(`
                UPDATE Booklets
                SET available_quantity = available_quantity - @quantity
                WHERE id = @booklet_id
            `);

        res.json({
            success: true,
            message: 'تم تسجيل استلام الملزمة بنجاح',
            data: result.recordset[0]
        });
    } catch (error) {
        console.error('Error creating booklet delivery:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في تسجيل استلام الملزمة',
            error: error.message
        });
    }
});

// POST /api/groups/:groupId/students - Add student to group
app.post('/api/groups/:groupId/students', async (req, res) => {
    try {
        const pool = await getConnection();
        const { groupId } = req.params;
        const { student_id } = req.body;

        if (!student_id) {
            return res.status(400).json({
                success: false,
                message: 'معرف الطالب مطلوب'
            });
        }

        // Check if student exists
        const studentCheck = await pool.request()
            .input('student_id', sql.Int, student_id)
            .query('SELECT id FROM Students WHERE id = @student_id AND is_deleted = 0');

        if (studentCheck.recordset.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'الطالب غير موجود'
            });
        }

        // Check if group exists
        const groupCheck = await pool.request()
            .input('group_id', sql.Int, groupId)
            .query('SELECT id FROM Groups WHERE id = @group_id AND is_deleted = 0');

        if (groupCheck.recordset.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'المجموعة غير موجودة'
            });
        }

        // Update student's group_id
        await pool.request()
            .input('student_id', sql.Int, student_id)
            .input('group_id', sql.Int, groupId)
            .query('UPDATE Students SET group_id = @group_id WHERE id = @student_id');

        res.json({
            success: true,
            message: 'تم إضافة الطالب للمجموعة بنجاح'
        });

    } catch (error) {
        console.error('Error adding student to group:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في إضافة الطالب للمجموعة',
            error: error.message
        });
    }
});

// DELETE /api/groups/:groupId/students/:studentId - Remove student from group
app.delete('/api/groups/:groupId/students/:studentId', async (req, res) => {
    try {
        const pool = await getConnection();
        const { studentId } = req.params;

        // Remove student from group
        await pool.request()
            .input('student_id', sql.Int, studentId)
            .query('UPDATE Students SET group_id = NULL WHERE id = @student_id');

        res.json({
            success: true,
            message: 'تم إزالة الطالب من المجموعة بنجاح'
        });

    } catch (error) {
        console.error('Error removing student from group:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في إزالة الطالب من المجموعة',
            error: error.message
        });
    }
});

// Error handling middleware
app.use((err, req, res, next) => {
    console.error(err.stack);
    res.status(500).json({
        success: false,
        message: 'Internal Server Error'
    });
});

// Get student by barcode/code with groups
app.get('/api/students/:code', async (req, res) => {
  try {
    const { code } = req.params;
    const { centerID, include_groups } = req.query;

    console.log(`🔍 Searching for student with code: ${code}, centerID: ${centerID}`);

    // Search for student by student_code
    const studentQuery = `
      SELECT s.*,
             i.institution_name,
             b.branch_name,
             g.grade_name
      FROM Students s
      LEFT JOIN Institutions i ON s.institution_id = i.id
      LEFT JOIN Branches b ON s.branch_id = b.id
      LEFT JOIN Grades g ON s.grade_id = g.id
      WHERE s.student_code = @code AND s.institution_id = @centerID
    `;

    const pool = await getConnection();
    const studentResult = await pool.request()
      .input('code', sql.NVarChar, code)
      .input('centerID', sql.Int, centerID)
      .query(studentQuery);

    if (studentResult.recordset.length === 0) {
      return res.json({
        success: false,
        message: 'Student not found',
        data: null
      });
    }

    const student = studentResult.recordset[0];

    // If include_groups is requested, get student's groups
    if (include_groups === 'true') {
      const groupsQuery = `
        SELECT gr.id, gr.group_name, gr.subject_name,
               t.teacher_name
        FROM Groups gr
        LEFT JOIN Teachers t ON gr.teacher_id = t.id
        WHERE gr.id = @groupID AND gr.institution_id = @centerID
      `;

      let groups = [];
      if (student.group_id) {
        const groupResult = await pool.request()
          .input('groupID', sql.Int, student.group_id)
          .input('centerID', sql.Int, centerID)
          .query(groupsQuery);
        groups = groupResult.recordset;
      }

      student.groups = groups;
    }

    console.log(`✅ Found student: ${student.student_name} with ${student.groups?.length || 0} groups`);

    res.json({
      success: true,
      data: student
    });

  } catch (error) {
    console.error('❌ Error searching student:', error);
    res.status(500).json({
      success: false,
      message: 'Error searching student',
      error: error.message
    });
  }
});

// Save attendance record
app.post('/api/attendance', async (req, res) => {
  try {
    const { student_id, group_id, attendance_date, is_present, institution_id } = req.body;

    console.log(`📝 Saving attendance: Student ${student_id}, Group ${group_id}, Date ${attendance_date}`);

    const query = `
      INSERT INTO Attendance (student_id, group_id, attendance_date, is_present, institution_id, created_at)
      VALUES (@student_id, @group_id, @attendance_date, @is_present, @institution_id, GETDATE())
    `;

    const pool = await getConnection();
    await pool.request()
      .input('student_id', sql.Int, student_id)
      .input('group_id', sql.Int, group_id)
      .input('attendance_date', sql.Date, attendance_date)
      .input('is_present', sql.Bit, is_present ? 1 : 0)
      .input('institution_id', sql.Int, institution_id)
      .query(query);

    console.log(`✅ Attendance saved successfully`);

    res.json({
      success: true,
      message: 'Attendance saved successfully'
    });

  } catch (error) {
    console.error('❌ Error saving attendance:', error);
    res.status(500).json({
      success: false,
      message: 'Error saving attendance',
      error: error.message
    });
  }
});

// Save booklet delivery
app.post('/api/booklet-deliveries', async (req, res) => {
  try {
    const { student_id, booklet_id, delivery_date, amount_paid, institution_id } = req.body;

    console.log(`📚 Saving booklet delivery: Student ${student_id}, Booklet ${booklet_id}, Amount ${amount_paid}`);

    const query = `
      INSERT INTO BookletDeliveries (student_id, booklet_id, delivery_date, amount_paid, institution_id, created_at)
      VALUES (@student_id, @booklet_id, @delivery_date, @amount_paid, @institution_id, GETDATE())
    `;

    const pool = await getConnection();
    await pool.request()
      .input('student_id', sql.Int, student_id)
      .input('booklet_id', sql.Int, booklet_id)
      .input('delivery_date', sql.Date, delivery_date)
      .input('amount_paid', sql.Decimal(10, 2), amount_paid)
      .input('institution_id', sql.Int, institution_id)
      .query(query);

    console.log(`✅ Booklet delivery saved successfully`);

    res.json({
      success: true,
      message: 'Booklet delivery saved successfully'
    });

  } catch (error) {
    console.error('❌ Error saving booklet delivery:', error);
    res.status(500).json({
      success: false,
      message: 'Error saving booklet delivery',
      error: error.message
    });
  }
});

// Get booklets
app.get('/api/booklets', async (req, res) => {
  try {
    const { centerID } = req.query;

    console.log(`📚 Getting booklets for center: ${centerID}`);

    const query = `
      SELECT id, booklet_name, unit_price
      FROM Booklets
      WHERE institution_id = @centerID AND is_active = 1
      ORDER BY booklet_name
    `;

    const pool = await getConnection();
    const result = await pool.request()
      .input('centerID', sql.Int, centerID)
      .query(query);

    console.log(`✅ Found ${result.recordset.length} booklets`);

    res.json({
      success: true,
      data: result.recordset
    });

  } catch (error) {
    console.error('❌ Error getting booklets:', error);
    res.status(500).json({
      success: false,
      message: 'Error getting booklets',
      error: error.message
    });
  }
});

// Save WhatsApp message
app.post('/api/whatsapp-messages', async (req, res) => {
  try {
    const { group_id, message_text, recipient_type, institution_id } = req.body;

    console.log(`💬 Saving WhatsApp message for group: ${group_id}`);

    const query = `
      INSERT INTO WhatsAppMessages (group_id, message_text, recipient_type, institution_id, status, created_at)
      OUTPUT INSERTED.id
      VALUES (@group_id, @message_text, @recipient_type, @institution_id, 'pending', GETDATE())
    `;

    const pool = await getConnection();
    const result = await pool.request()
      .input('group_id', sql.Int, group_id)
      .input('message_text', sql.NVarChar(sql.MAX), message_text)
      .input('recipient_type', sql.NVarChar(20), recipient_type)
      .input('institution_id', sql.Int, institution_id)
      .query(query);

    console.log(`✅ WhatsApp message saved with ID: ${result.recordset[0].id}`);

    res.json({
      success: true,
      data: { id: result.recordset[0].id },
      message: 'WhatsApp message saved successfully'
    });

  } catch (error) {
    console.error('❌ Error saving WhatsApp message:', error);
    res.status(500).json({
      success: false,
      message: 'Error saving WhatsApp message',
      error: error.message
    });
  }
});

// Send WhatsApp message
app.post('/api/whatsapp/send', async (req, res) => {
  try {
    const { phone_number, message, student_id, message_id } = req.body;

    console.log(`📱 Sending WhatsApp to: ${phone_number}`);

    // Here you would integrate with actual WhatsApp API
    // For now, we'll simulate the sending

    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 500));

    // Simulate 90% success rate
    const isSuccess = Math.random() > 0.1;

    if (isSuccess) {
      // Save delivery record
      const query = `
        INSERT INTO WhatsAppDeliveries (message_id, student_id, phone_number, status, sent_at)
        VALUES (@message_id, @student_id, @phone_number, 'sent', GETDATE())
      `;

      const pool = await getConnection();
      await pool.request()
        .input('message_id', sql.Int, message_id)
        .input('student_id', sql.Int, student_id)
        .input('phone_number', sql.NVarChar(20), phone_number)
        .query(query);

      console.log(`✅ WhatsApp sent successfully to: ${phone_number}`);

      res.json({
        success: true,
        message: 'WhatsApp message sent successfully'
      });
    } else {
      // Save failed delivery record
      const query = `
        INSERT INTO WhatsAppDeliveries (message_id, student_id, phone_number, status, error_message, sent_at)
        VALUES (@message_id, @student_id, @phone_number, 'failed', 'Simulated failure', GETDATE())
      `;

      const pool = await getConnection();
      await pool.request()
        .input('message_id', sql.Int, message_id)
        .input('student_id', sql.Int, student_id)
        .input('phone_number', sql.NVarChar(20), phone_number)
        .query(query);

      throw new Error('Simulated WhatsApp sending failure');
    }

  } catch (error) {
    console.error('❌ Error sending WhatsApp:', error);
    res.status(500).json({
      success: false,
      message: 'Error sending WhatsApp message',
      error: error.message
    });
  }
});

// ==================== EXAMS SYSTEM ENDPOINTS ====================

/**
 * @swagger
 * /api/exams:
 *   get:
 *     summary: Get all exams
 *     tags: [Exams]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Number of items per page
 *       - in: query
 *         name: teacher_id
 *         schema:
 *           type: integer
 *         description: Filter by teacher ID
 *       - in: query
 *         name: group_id
 *         schema:
 *           type: integer
 *         description: Filter by group ID
 *     responses:
 *       200:
 *         description: List of exams
 */
app.get('/api/exams', async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const offset = (page - 1) * limit;
        const teacherId = req.query.teacher_id;
        const groupId = req.query.group_id;

        const pool = await getConnection();

        let whereClause = 'WHERE e.is_deleted = 0';
        const params = [];

        if (teacherId) {
            whereClause += ' AND e.teacher_id = @teacher_id';
            params.push({ name: 'teacher_id', type: sql.Int, value: parseInt(teacherId) });
        }

        if (groupId) {
            whereClause += ' AND e.group_id = @group_id';
            params.push({ name: 'group_id', type: sql.Int, value: parseInt(groupId) });
        }

        // Get total count
        const countQuery = `
            SELECT COUNT(*) as total
            FROM Exams e
            ${whereClause}
        `;

        let countRequest = pool.request();
        params.forEach(param => {
            countRequest.input(param.name, param.type, param.value);
        });

        const countResult = await countRequest.query(countQuery);
        const total = countResult.recordset[0].total;

        // Get exams
        const query = `
            SELECT *
            FROM ExamsView ev
            WHERE ev.id IN (
                SELECT e.id
                FROM Exams e
                ${whereClause}
            )
            ORDER BY ev.created_at DESC
            OFFSET @offset ROWS
            FETCH NEXT @limit ROWS ONLY
        `;

        let request = pool.request();
        params.forEach(param => {
            request.input(param.name, param.type, param.value);
        });
        request.input('offset', sql.Int, offset);
        request.input('limit', sql.Int, limit);

        const result = await request.query(query);

        res.json({
            success: true,
            data: result.recordset,
            pagination: {
                page,
                limit,
                total,
                pages: Math.ceil(total / limit)
            }
        });
    } catch (error) {
        console.error('Error fetching exams:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في جلب الامتحانات',
            error: error.message
        });
    }
});

/**
 * @swagger
 * /api/exams:
 *   post:
 *     summary: Create a new exam
 *     tags: [Exams]
 */
app.post('/api/exams', async (req, res) => {
    try {
        const {
            exam_name,
            teacher_id,
            group_id,
            total_marks,
            exam_date,
            exam_duration_minutes,
            description
        } = req.body;

        if (!exam_name || !teacher_id || !group_id || !total_marks) {
            return res.status(400).json({
                success: false,
                message: 'البيانات المطلوبة مفقودة'
            });
        }

        const pool = await getConnection();

        // Get group details
        const groupQuery = `
            SELECT g.grade_id, g.branch_id, gr.institution_id
            FROM Groups g
            INNER JOIN Grades gr ON g.grade_id = gr.id
            WHERE g.id = @group_id AND g.is_deleted = 0
        `;

        const groupResult = await pool.request()
            .input('group_id', sql.Int, group_id)
            .query(groupQuery);

        if (groupResult.recordset.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'المجموعة غير موجودة'
            });
        }

        const { grade_id, branch_id, institution_id } = groupResult.recordset[0];

        // Generate exam code
        const codeResult = await pool.request()
            .input('InstitutionId', sql.Int, institution_id)
            .output('ExamCode', sql.NVarChar(20))
            .execute('GenerateExamCode');

        const exam_code = codeResult.output.ExamCode;

        // Insert exam
        const insertQuery = `
            INSERT INTO Exams (
                exam_code, exam_name, teacher_id, group_id, grade_id,
                branch_id, institution_id, total_marks, exam_date,
                exam_duration_minutes, description
            )
            OUTPUT INSERTED.id
            VALUES (
                @exam_code, @exam_name, @teacher_id, @group_id, @grade_id,
                @branch_id, @institution_id, @total_marks, @exam_date,
                @exam_duration_minutes, @description
            )
        `;

        const result = await pool.request()
            .input('exam_code', sql.NVarChar(20), exam_code)
            .input('exam_name', sql.NVarChar(255), exam_name)
            .input('teacher_id', sql.Int, teacher_id)
            .input('group_id', sql.Int, group_id)
            .input('grade_id', sql.Int, grade_id)
            .input('branch_id', sql.Int, branch_id)
            .input('institution_id', sql.Int, institution_id)
            .input('total_marks', sql.Decimal(5, 2), total_marks)
            .input('exam_date', sql.Date, exam_date || null)
            .input('exam_duration_minutes', sql.Int, exam_duration_minutes || 120)
            .input('description', sql.NVarChar(sql.MAX), description || null)
            .query(insertQuery);

        const examId = result.recordset[0].id;

        res.status(201).json({
            success: true,
            message: 'تم إنشاء الامتحان بنجاح',
            data: {
                id: examId,
                exam_code: exam_code
            }
        });
    } catch (error) {
        console.error('Error creating exam:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في إنشاء الامتحان',
            error: error.message
        });
    }
});

// Get exam results
app.get('/api/exam-results/:exam_id', async (req, res) => {
    try {
        const { exam_id } = req.params;
        const pool = await getConnection();

        const query = `
            SELECT *
            FROM ExamResultsView
            WHERE exam_id = @exam_id
            ORDER BY student_name
        `;

        const result = await pool.request()
            .input('exam_id', sql.Int, exam_id)
            .query(query);

        res.json({
            success: true,
            data: result.recordset
        });
    } catch (error) {
        console.error('Error fetching exam results:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في جلب نتائج الامتحان',
            error: error.message
        });
    }
});

// Add or update exam result
app.post('/api/exam-results', async (req, res) => {
    try {
        const { exam_id, student_code, marks_obtained, notes } = req.body;

        if (!exam_id || !student_code || marks_obtained === undefined) {
            return res.status(400).json({
                success: false,
                message: 'البيانات المطلوبة مفقودة'
            });
        }

        const pool = await getConnection();

        // Get student ID from code
        const studentQuery = `
            SELECT id FROM Students
            WHERE student_code = @student_code AND is_deleted = 0
        `;

        const studentResult = await pool.request()
            .input('student_code', sql.NVarChar(20), student_code)
            .query(studentQuery);

        if (studentResult.recordset.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'الطالب غير موجود'
            });
        }

        const student_id = studentResult.recordset[0].id;

        // Check if result already exists
        const existingQuery = `
            SELECT id FROM ExamResults
            WHERE exam_id = @exam_id AND student_id = @student_id
        `;

        const existingResult = await pool.request()
            .input('exam_id', sql.Int, exam_id)
            .input('student_id', sql.Int, student_id)
            .query(existingQuery);

        let query;
        if (existingResult.recordset.length > 0) {
            // Update existing result
            query = `
                UPDATE ExamResults
                SET marks_obtained = @marks_obtained,
                    notes = @notes,
                    corrected_at = GETDATE()
                WHERE exam_id = @exam_id AND student_id = @student_id
            `;
        } else {
            // Insert new result
            query = `
                INSERT INTO ExamResults (exam_id, student_id, student_code, marks_obtained, notes)
                VALUES (@exam_id, @student_id, @student_code, @marks_obtained, @notes)
            `;
        }

        await pool.request()
            .input('exam_id', sql.Int, exam_id)
            .input('student_id', sql.Int, student_id)
            .input('student_code', sql.NVarChar(20), student_code)
            .input('marks_obtained', sql.Decimal(5, 2), marks_obtained)
            .input('notes', sql.NVarChar(sql.MAX), notes || null)
            .query(query);

        res.json({
            success: true,
            message: 'تم حفظ النتيجة بنجاح'
        });
    } catch (error) {
        console.error('Error saving exam result:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في حفظ النتيجة',
            error: error.message
        });
    }
});

// Drop and recreate exams tables
app.post('/api/setup/recreate-exams-tables', async (req, res) => {
    try {
        const pool = await getConnection();

        // Drop existing tables and views
        await pool.request().query(`
            IF EXISTS (SELECT * FROM sys.views WHERE name = 'ExamResultsView')
                DROP VIEW ExamResultsView
        `);

        await pool.request().query(`
            IF EXISTS (SELECT * FROM sys.views WHERE name = 'ExamsView')
                DROP VIEW ExamsView
        `);

        await pool.request().query(`
            IF EXISTS (SELECT * FROM sysobjects WHERE name='ExamResults' AND xtype='U')
                DROP TABLE ExamResults
        `);

        await pool.request().query(`
            IF EXISTS (SELECT * FROM sysobjects WHERE name='Exams' AND xtype='U')
                DROP TABLE Exams
        `);

        // Create Exams table with all required columns
        await pool.request().query(`
            CREATE TABLE Exams (
                id INT IDENTITY(1,1) PRIMARY KEY,
                exam_code NVARCHAR(20) UNIQUE NOT NULL,
                exam_name NVARCHAR(255) NOT NULL,
                teacher_id INT NOT NULL,
                group_id INT NOT NULL,
                grade_id INT NOT NULL,
                branch_id INT NOT NULL,
                institution_id INT NOT NULL,
                total_marks DECIMAL(5,2) NOT NULL DEFAULT 100.00,
                exam_date DATE NULL,
                exam_duration_minutes INT DEFAULT 120,
                description NVARCHAR(MAX) NULL,
                is_active BIT DEFAULT 1,
                is_deleted BIT DEFAULT 0,
                created_at DATETIME2 DEFAULT GETDATE(),
                updated_at DATETIME2 DEFAULT GETDATE(),
                created_by INT NULL
            )
        `);

        // Create ExamResults table
        await pool.request().query(`
            CREATE TABLE ExamResults (
                id INT IDENTITY(1,1) PRIMARY KEY,
                exam_id INT NOT NULL,
                student_id INT NOT NULL,
                student_code NVARCHAR(20) NOT NULL,
                marks_obtained DECIMAL(5,2) NOT NULL DEFAULT 0.00,
                percentage DECIMAL(5,2) NULL,
                is_passed BIT NULL,
                corrected_at DATETIME2 DEFAULT GETDATE(),
                corrected_by INT NULL,
                notes NVARCHAR(MAX) NULL,
                is_deleted BIT DEFAULT 0,

                CONSTRAINT FK_ExamResults_Exam FOREIGN KEY (exam_id) REFERENCES Exams(id),
                CONSTRAINT FK_ExamResults_Student FOREIGN KEY (student_id) REFERENCES Students(id),
                CONSTRAINT UK_ExamResults_Student_Exam UNIQUE (exam_id, student_id)
            )
        `);

        // Create indexes for better performance
        await pool.request().query(`
            CREATE INDEX IX_Exams_Teacher_Group ON Exams(teacher_id, group_id);
            CREATE INDEX IX_Exams_Branch_Grade ON Exams(branch_id, grade_id);
            CREATE INDEX IX_Exams_Date ON Exams(exam_date);
            CREATE INDEX IX_ExamResults_Exam ON ExamResults(exam_id);
            CREATE INDEX IX_ExamResults_Student ON ExamResults(student_id);
        `);

        res.json({
            success: true,
            message: 'تم إنشاء جداول الامتحانات بنجاح'
        });
    } catch (error) {
        console.error('Error recreating exams tables:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في إنشاء جداول الامتحانات',
            error: error.message
        });
    }
});

// Create exams tables (legacy endpoint)
app.post('/api/setup/create-exams-tables', async (req, res) => {
    // Redirect to recreate endpoint
    return res.redirect(307, '/api/setup/recreate-exams-tables');
});

// Create exams views
app.post('/api/setup/create-exams-views', async (req, res) => {
    try {
        const pool = await getConnection();

        // Create ExamsView with proper column references
        await pool.request().query(`
            CREATE OR ALTER VIEW ExamsView AS
            SELECT
                e.id,
                e.exam_code,
                e.exam_name,
                e.total_marks,
                e.exam_date,
                e.exam_duration_minutes,
                ISNULL(e.description, '') as description,
                e.is_active,
                e.created_at,

                ISNULL(t.teacher_name, '') as teacher_name,
                ISNULL(t.teacher_code, '') as teacher_code,
                ISNULL(g.group_name, '') as group_name,
                ISNULL(gr.grade_name, '') as grade_name,
                ISNULL(b.branch_name, '') as branch_name,
                ISNULL(i.institution_name, '') as institution_name,

                (SELECT COUNT(*) FROM ExamResults er WHERE er.exam_id = e.id AND er.is_deleted = 0) as total_students,
                (SELECT COUNT(*) FROM ExamResults er WHERE er.exam_id = e.id AND er.is_passed = 1 AND er.is_deleted = 0) as passed_students,
                (SELECT ISNULL(AVG(er.marks_obtained), 0) FROM ExamResults er WHERE er.exam_id = e.id AND er.is_deleted = 0) as average_marks

            FROM Exams e
            LEFT JOIN Teachers t ON e.teacher_id = t.id
            LEFT JOIN Groups g ON e.group_id = g.id
            LEFT JOIN Grades gr ON e.grade_id = gr.id
            LEFT JOIN Branches b ON e.branch_id = b.id
            LEFT JOIN Institutions i ON e.institution_id = i.id
            WHERE e.is_deleted = 0
        `);

        // Create ExamResultsView with computed percentage and pass status
        await pool.request().query(`
            CREATE OR ALTER VIEW ExamResultsView AS
            SELECT
                er.id,
                er.exam_id,
                er.student_id,
                er.marks_obtained,
                CASE
                    WHEN e.total_marks > 0 THEN ROUND((er.marks_obtained * 100.0) / e.total_marks, 2)
                    ELSE 0
                END as percentage,
                CASE
                    WHEN e.total_marks > 0 AND (er.marks_obtained * 100.0) / e.total_marks >= 50 THEN 1
                    ELSE 0
                END as is_passed,
                er.corrected_at,
                ISNULL(er.notes, '') as notes,

                e.exam_code,
                e.exam_name,
                e.total_marks,
                e.exam_date,

                s.student_code,
                s.student_name,
                ISNULL(s.student_phone, '') as student_phone,

                ISNULL(t.teacher_name, '') as teacher_name,
                ISNULL(t.teacher_code, '') as teacher_code,
                ISNULL(g.group_name, '') as group_name,
                ISNULL(gr.grade_name, '') as grade_name,
                ISNULL(b.branch_name, '') as branch_name,
                ISNULL(i.institution_name, '') as institution_name

            FROM ExamResults er
            INNER JOIN Exams e ON er.exam_id = e.id
            INNER JOIN Students s ON er.student_id = s.id
            LEFT JOIN Teachers t ON e.teacher_id = t.id
            LEFT JOIN Groups g ON e.group_id = g.id
            LEFT JOIN Grades gr ON e.grade_id = gr.id
            LEFT JOIN Branches b ON e.branch_id = b.id
            LEFT JOIN Institutions i ON e.institution_id = i.id
            WHERE er.is_deleted = 0 AND e.is_deleted = 0
        `);

        res.json({
            success: true,
            message: 'تم إنشاء views الامتحانات بنجاح'
        });
    } catch (error) {
        console.error('Error creating exams views:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في إنشاء views الامتحانات',
            error: error.message
        });
    }
});

// Create stored procedures for exams
app.post('/api/setup/create-exams-procedures', async (req, res) => {
    try {
        const pool = await getConnection();

        // Create procedure to generate exam code
        await pool.request().query(`
            CREATE OR ALTER PROCEDURE GenerateExamCode
                @InstitutionId INT,
                @ExamCode NVARCHAR(20) OUTPUT
            AS
            BEGIN
                DECLARE @Counter INT;
                DECLARE @CodePrefix NVARCHAR(10) = 'EXM';

                -- Get the next number
                SELECT @Counter = ISNULL(MAX(CAST(SUBSTRING(exam_code, 4, LEN(exam_code) - 3) AS INT)), 0) + 1
                FROM Exams
                WHERE institution_id = @InstitutionId
                AND exam_code LIKE @CodePrefix + '%'
                AND ISNUMERIC(SUBSTRING(exam_code, 4, LEN(exam_code) - 3)) = 1;

                -- Generate the new code
                SET @ExamCode = @CodePrefix + RIGHT('000' + CAST(@Counter AS NVARCHAR), 3);

                -- Ensure uniqueness
                WHILE EXISTS (SELECT 1 FROM Exams WHERE exam_code = @ExamCode AND institution_id = @InstitutionId)
                BEGIN
                    SET @Counter = @Counter + 1;
                    SET @ExamCode = @CodePrefix + RIGHT('000' + CAST(@Counter AS NVARCHAR), 3);
                END
            END
        `);

        // Create procedure to update exam result percentages
        await pool.request().query(`
            CREATE OR ALTER PROCEDURE UpdateExamResultPercentages
                @ExamId INT
            AS
            BEGIN
                UPDATE ExamResults
                SET
                    percentage = CASE
                        WHEN e.total_marks > 0 THEN ROUND((ExamResults.marks_obtained * 100.0) / e.total_marks, 2)
                        ELSE 0
                    END,
                    is_passed = CASE
                        WHEN e.total_marks > 0 AND (ExamResults.marks_obtained * 100.0) / e.total_marks >= 50 THEN 1
                        ELSE 0
                    END
                FROM ExamResults
                INNER JOIN Exams e ON ExamResults.exam_id = e.id
                WHERE ExamResults.exam_id = @ExamId
                AND ExamResults.is_deleted = 0
            END
        `);

        res.json({
            success: true,
            message: 'تم إنشاء stored procedures الامتحانات بنجاح'
        });
    } catch (error) {
        console.error('Error creating exams procedures:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في إنشاء stored procedures الامتحانات',
            error: error.message
        });
    }
});

// Create WhatsApp messages table
app.post('/api/setup/create-whatsapp-table', async (req, res) => {
    try {
        const pool = await getConnection();

        // Create WhatsAppMessages table
        await pool.request().query(`
            IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='WhatsAppMessages' AND xtype='U')
            BEGIN
                CREATE TABLE WhatsAppMessages (
                    id INT IDENTITY(1,1) PRIMARY KEY,
                    phone_number NVARCHAR(20) NOT NULL,
                    message NVARCHAR(MAX) NOT NULL,
                    group_id INT NULL,
                    student_id INT NULL,
                    exam_id INT NULL,
                    status NVARCHAR(20) DEFAULT 'pending',
                    error_message NVARCHAR(MAX) NULL,
                    sent_at DATETIME2 DEFAULT GETDATE(),
                    created_by INT NULL,
                    institution_id INT NULL,

                    CONSTRAINT FK_WhatsAppMessages_Group FOREIGN KEY (group_id) REFERENCES Groups(id),
                    CONSTRAINT FK_WhatsAppMessages_Student FOREIGN KEY (student_id) REFERENCES Students(id),
                    CONSTRAINT FK_WhatsAppMessages_Exam FOREIGN KEY (exam_id) REFERENCES Exams(id)
                )
            END
        `);

        // Create indexes
        await pool.request().query(`
            IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_WhatsAppMessages_Group')
                CREATE INDEX IX_WhatsAppMessages_Group ON WhatsAppMessages(group_id);

            IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_WhatsAppMessages_Student')
                CREATE INDEX IX_WhatsAppMessages_Student ON WhatsAppMessages(student_id);

            IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_WhatsAppMessages_Status')
                CREATE INDEX IX_WhatsAppMessages_Status ON WhatsAppMessages(status);
        `);

        res.json({
            success: true,
            message: 'تم إنشاء جدول رسائل الواتساب بنجاح'
        });
    } catch (error) {
        console.error('Error creating WhatsApp table:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في إنشاء جدول رسائل الواتساب',
            error: error.message
        });
    }
});

// GET /api/exams/:id/students - Get students for exam correction
app.get('/api/exams/:id/students', async (req, res) => {
    try {
        const pool = await getConnection();
        const { id } = req.params;

        // Get exam details first
        const examResult = await pool.request()
            .input('id', sql.Int, id)
            .query(`
                SELECT e.*, g.group_name
                FROM Exams e
                LEFT JOIN Groups g ON e.group_id = g.id
                WHERE e.id = @id AND e.is_deleted = 0
            `);

        if (examResult.recordset.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'الامتحان غير موجود'
            });
        }

        const exam = examResult.recordset[0];

        // Get students in the group with their exam results if any
        const studentsResult = await pool.request()
            .input('group_id', sql.Int, exam.group_id)
            .input('exam_id', sql.Int, id)
            .query(`
                SELECT
                    s.id,
                    s.student_code,
                    s.student_name,
                    s.student_phone,
                    er.marks_obtained,
                    er.percentage,
                    er.is_passed,
                    er.notes,
                    CASE WHEN er.id IS NOT NULL THEN 1 ELSE 0 END as corrected
                FROM Students s
                INNER JOIN StudentGroups sg ON s.id = sg.student_id
                LEFT JOIN ExamResults er ON s.id = er.student_id AND er.exam_id = @exam_id AND er.is_deleted = 0
                WHERE sg.group_id = @group_id
                AND s.is_deleted = 0
                ORDER BY s.student_name
            `);

        res.json({
            success: true,
            data: studentsResult.recordset
        });
    } catch (error) {
        console.error('Error fetching exam students:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في جلب طلاب الامتحان',
            error: error.message
        });
    }
});

// GET /api/groups/:id/students - Get students in a group for WhatsApp messaging
app.get('/api/groups/:id/students', async (req, res) => {
    try {
        const pool = await getConnection();
        const { id } = req.params;

        const studentsResult = await pool.request()
            .input('group_id', sql.Int, id)
            .query(`
                SELECT
                    s.id,
                    s.student_code,
                    s.student_name,
                    s.student_phone,
                    s.father_phone,
                    s.mother_phone
                FROM Students s
                INNER JOIN StudentGroups sg ON s.id = sg.student_id
                WHERE sg.group_id = @group_id
                AND s.is_deleted = 0
                ORDER BY s.student_name
            `);

        res.json({
            success: true,
            data: studentsResult.recordset
        });
    } catch (error) {
        console.error('Error fetching group students:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في جلب طلاب المجموعة',
            error: error.message
        });
    }
});

// ==================== FINANCIAL SYSTEM ENDPOINTS ====================

// GET /api/booklets - Get all booklets
app.get('/api/booklets', async (req, res) => {
    try {
        const pool = await getConnection();
        const { page = 1, limit = 10, institution_id, teacher_id, subject_id } = req.query;
        const offset = (page - 1) * limit;

        let whereClause = 'WHERE b.is_active = 1';
        if (institution_id) whereClause += ` AND b.institution_id = ${institution_id}`;
        if (teacher_id) whereClause += ` AND b.teacher_id = ${teacher_id}`;
        if (subject_id) whereClause += ` AND b.subject_id = ${subject_id}`;

        const query = `
            SELECT
                b.id, b.booklet_code, b.booklet_name, b.unit_price as price, b.description,
                b.institution_id, b.teacher_id, b.subject_id, b.grade_id,
                i.institution_name, t.teacher_name, s.subject_name, g.grade_name,
                b.created_at
            FROM Booklets b
            LEFT JOIN Institutions i ON b.institution_id = i.id
            LEFT JOIN Teachers t ON b.teacher_id = t.id
            LEFT JOIN Subjects s ON b.subject_id = s.id
            LEFT JOIN Grades g ON b.grade_id = g.id
            ${whereClause}
            ORDER BY b.created_at DESC
            OFFSET ${offset} ROWS FETCH NEXT ${limit} ROWS ONLY
        `;

        const countQuery = `
            SELECT COUNT(*) as total
            FROM Booklets b
            ${whereClause}
        `;

        const [result, countResult] = await Promise.all([
            pool.request().query(query),
            pool.request().query(countQuery)
        ]);

        res.json({
            success: true,
            data: result.recordset,
            total: countResult.recordset[0].total,
            page: parseInt(page),
            limit: parseInt(limit)
        });
    } catch (error) {
        console.error('Error fetching booklets:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في جلب الملازم',
            error: error.message
        });
    }
});

// POST /api/booklets - Create new booklet
app.post('/api/booklets', async (req, res) => {
    try {
        const pool = await getConnection();
        const { booklet_name, price, institution_id, teacher_id, subject_id, grade_id, description } = req.body;

        if (!booklet_name || !price || !institution_id) {
            return res.status(400).json({
                success: false,
                message: 'اسم الملزمة والسعر والمؤسسة مطلوبة'
            });
        }

        // Generate booklet code
        const codeResult = await pool.request()
            .input('institution_id', sql.Int, institution_id)
            .query(`
                SELECT ISNULL(MAX(CAST(SUBSTRING(booklet_code, 3, LEN(booklet_code)) AS INT)), 0) + 1 as next_number
                FROM Booklets
                WHERE institution_id = @institution_id
                AND booklet_code LIKE 'BK%'
            `);

        const nextNumber = codeResult.recordset[0].next_number;
        const bookletCode = `BK${String(nextNumber).padStart(3, '0')}`;

        const result = await pool.request()
            .input('booklet_code', sql.NVarChar, bookletCode)
            .input('booklet_name', sql.NVarChar, booklet_name)
            .input('unit_price', sql.Decimal(10,2), price)
            .input('institution_id', sql.Int, institution_id)
            .input('teacher_id', sql.Int, teacher_id || null)
            .input('subject_id', sql.Int, subject_id || null)
            .input('grade_id', sql.Int, grade_id || null)
            .input('description', sql.NVarChar, description || '')
            .query(`
                INSERT INTO Booklets (
                    booklet_code, booklet_name, unit_price, institution_id,
                    teacher_id, subject_id, grade_id, description
                )
                OUTPUT INSERTED.id, INSERTED.booklet_code
                VALUES (
                    @booklet_code, @booklet_name, @unit_price, @institution_id,
                    @teacher_id, @subject_id, @grade_id, @description
                )
            `);

        res.json({
            success: true,
            message: 'تم إنشاء الملزمة بنجاح',
            data: {
                id: result.recordset[0].id,
                booklet_code: result.recordset[0].booklet_code
            }
        });
    } catch (error) {
        console.error('Error creating booklet:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في إنشاء الملزمة',
            error: error.message
        });
    }
});

// GET /api/groups/:id/booklets - Get booklets for a specific group
app.get('/api/groups/:id/booklets', async (req, res) => {
    try {
        const pool = await getConnection();
        const { id } = req.params;

        // Get group info first
        const groupResult = await pool.request()
            .input('group_id', sql.Int, parseInt(id))
            .query(`
                SELECT g.id, g.teacher_id, g.subject_id, g.grade_id, g.institution_id
                FROM Groups g
                WHERE g.id = @group_id AND g.is_active = 1
            `);

        if (groupResult.recordset.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'المجموعة غير موجودة'
            });
        }

        const group = groupResult.recordset[0];

        // Get booklets for this group (by teacher, subject, or grade)
        const query = `
            SELECT
                b.id, b.booklet_code, b.booklet_name, b.unit_price as price, b.description,
                b.teacher_id, b.subject_id, b.grade_id,
                t.teacher_name, s.subject_name, g.grade_name
            FROM Booklets b
            LEFT JOIN Teachers t ON b.teacher_id = t.id
            LEFT JOIN Subjects s ON b.subject_id = s.id
            LEFT JOIN Grades g ON b.grade_id = g.id
            WHERE b.is_active = 1
            AND b.institution_id = @institution_id
            AND (
                b.teacher_id = @teacher_id
                OR b.subject_id = @subject_id
                OR b.grade_id = @grade_id
                OR (b.teacher_id IS NULL AND b.subject_id IS NULL AND b.grade_id IS NULL)
            )
            ORDER BY b.booklet_name
        `;

        const result = await pool.request()
            .input('institution_id', sql.Int, group.institution_id)
            .input('teacher_id', sql.Int, group.teacher_id)
            .input('subject_id', sql.Int, group.subject_id)
            .input('grade_id', sql.Int, group.grade_id)
            .query(query);

        res.json({
            success: true,
            data: result.recordset
        });
    } catch (error) {
        console.error('Error fetching group booklets:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في جلب ملازم المجموعة',
            error: error.message
        });
    }
});

// POST /api/booklet-distributions - Distribute booklet to student
app.post('/api/booklet-distributions', async (req, res) => {
    try {
        const pool = await getConnection();
        const {
            student_id,
            booklet_id,
            custody_id,
            amount_paid = 0,
            is_exempt = false,
            exemption_reason = '',
            attendance_record_id,
            distributed_by
        } = req.body;

        if (!student_id || !booklet_id || !custody_id) {
            return res.status(400).json({
                success: false,
                message: 'بيانات الطالب والملزمة والعهدة مطلوبة'
            });
        }

        // Get booklet price
        const bookletResult = await pool.request()
            .input('booklet_id', sql.Int, booklet_id)
            .query('SELECT unit_price as price FROM Booklets WHERE id = @booklet_id');

        if (bookletResult.recordset.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'الملزمة غير موجودة'
            });
        }

        const bookletPrice = bookletResult.recordset[0].price;

        // Get student and booklet info for journal entry
        const studentResult = await pool.request()
            .input('student_id', sql.Int, student_id)
            .query('SELECT student_name FROM Students WHERE id = @student_id');

        const bookletResult2 = await pool.request()
            .input('booklet_id', sql.Int, booklet_id)
            .query('SELECT booklet_name FROM Booklets WHERE id = @booklet_id');

        const studentName = studentResult.recordset[0]?.student_name || 'غير معروف';
        const bookletName = bookletResult2.recordset[0]?.booklet_name || 'غير معروف';

        // Insert distribution record
        const result = await pool.request()
            .input('student_id', sql.Int, student_id)
            .input('booklet_id', sql.Int, booklet_id)
            .input('custody_id', sql.Int, custody_id)
            .input('unit_price', sql.Decimal(10,2), bookletPrice)
            .input('amount_paid', sql.Decimal(10,2), amount_paid)
            .input('is_exempt', sql.Bit, is_exempt)
            .input('exemption_reason', sql.NVarChar, exemption_reason)
            .input('attendance_record_id', sql.Int, attendance_record_id || null)
            .input('distributed_by', sql.Int, distributed_by || null)
            .query(`
                INSERT INTO BookletDistributions (
                    student_id, booklet_id, custody_id, unit_price,
                    amount_paid, is_exempt, exemption_reason,
                    attendance_record_id, distributed_by
                )
                OUTPUT INSERTED.id
                VALUES (
                    @student_id, @booklet_id, @custody_id, @unit_price,
                    @amount_paid, @is_exempt, @exemption_reason,
                    @attendance_record_id, @distributed_by
                )
            `);

        const distributionId = result.recordset[0].id;

        // Create accounting entry
        try {
            await pool.request()
                .input('distribution_id', sql.Int, distributionId)
                .input('student_name', sql.NVarChar, studentName)
                .input('booklet_name', sql.NVarChar, bookletName)
                .input('amount', sql.Decimal(15,2), bookletPrice)
                .input('is_exempt', sql.Bit, is_exempt)
                .input('exemption_reason', sql.NVarChar, exemption_reason)
                .execute('sp_create_booklet_distribution_entry');
        } catch (entryError) {
            console.error('Error creating accounting entry:', entryError);
            // Continue without failing the distribution
        }

        res.json({
            success: true,
            message: 'تم توزيع الملزمة بنجاح',
            data: {
                distribution_id: distributionId
            }
        });
    } catch (error) {
        console.error('Error distributing booklet:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في توزيع الملزمة',
            error: error.message
        });
    }
});

// POST /api/booklet-custodies - Create booklet custody (for testing)
app.post('/api/booklet-custodies', async (req, res) => {
    try {
        const pool = await getConnection();
        const { teacher_id, booklet_id, quantity, unit_price, delivery_date, notes } = req.body;

        if (!teacher_id || !booklet_id || !quantity || !unit_price) {
            return res.status(400).json({
                success: false,
                message: 'جميع البيانات مطلوبة'
            });
        }

        // Generate custody number
        const codeResult = await pool.request()
            .query(`
                SELECT ISNULL(MAX(CAST(SUBSTRING(custody_number, 4, LEN(custody_number)) AS INT)), 0) + 1 as next_number
                FROM BookletCustodies
                WHERE custody_number LIKE 'CUS%'
            `);

        const nextNumber = codeResult.recordset[0].next_number;
        const custodyNumber = `CUS${String(nextNumber).padStart(3, '0')}`;

        const totalAmount = quantity * unit_price;

        const result = await pool.request()
            .input('custody_number', sql.NVarChar, custodyNumber)
            .input('teacher_id', sql.Int, teacher_id)
            .input('booklet_id', sql.Int, booklet_id)
            .input('quantity', sql.Int, quantity)
            .input('unit_price', sql.Decimal(10,2), unit_price)
            .input('total_amount', sql.Decimal(10,2), totalAmount)
            .input('delivery_date', sql.Date, delivery_date || new Date())
            .input('notes', sql.NVarChar, notes || '')
            .query(`
                INSERT INTO BookletCustodies (
                    custody_number, teacher_id, booklet_id, quantity,
                    unit_price, total_amount, delivery_date, notes
                )
                OUTPUT INSERTED.id, INSERTED.custody_number
                VALUES (
                    @custody_number, @teacher_id, @booklet_id, @quantity,
                    @unit_price, @total_amount, @delivery_date, @notes
                )
            `);

        res.json({
            success: true,
            message: 'تم إنشاء العهدة بنجاح',
            data: {
                id: result.recordset[0].id,
                custody_number: result.recordset[0].custody_number
            }
        });
    } catch (error) {
        console.error('Error creating custody:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في إنشاء العهدة',
            error: error.message
        });
    }
});

// ==================== ACCOUNTING SYSTEM ENDPOINTS ====================

// Function to get next counter number
async function getNextCounterNumber(pool, counterType, branchId = 1) {
    const year = new Date().getFullYear();
    const month = new Date().getMonth() + 1;

    const result = await pool.request()
        .input('counter_type', sql.NVarChar, counterType)
        .input('branch_id', sql.Int, branchId)
        .input('year_number', sql.Int, year)
        .input('month_number', sql.Int, month)
        .query(`
            MERGE COUNTERS AS target
            USING (SELECT @counter_type as counter_type, @branch_id as branch_id,
                          @year_number as year_number, @month_number as month_number) AS source
            ON target.counter_type = source.counter_type
               AND target.branch_id = source.branch_id
               AND target.year_number = source.year_number
               AND target.month_number = source.month_number
            WHEN MATCHED THEN
                UPDATE SET last_number = last_number + 1, updated_at = GETDATE()
            WHEN NOT MATCHED THEN
                INSERT (counter_type, branch_id, year_number, month_number, last_number, prefix)
                VALUES (@counter_type, @branch_id, @year_number, @month_number, 1, @counter_type);

            SELECT last_number, prefix FROM COUNTERS
            WHERE counter_type = @counter_type AND branch_id = @branch_id
                  AND year_number = @year_number AND month_number = @month_number;
        `);

    const counter = result.recordset[0];
    const formattedNumber = `${counter.prefix}${year}${String(month).padStart(2, '0')}${String(counter.last_number).padStart(4, '0')}`;

    return formattedNumber;
}

// POST /api/journal-entries - Create journal entry
app.post('/api/journal-entries', async (req, res) => {
    try {
        const pool = await getConnection();
        const { description, entries, reference_type, reference_id } = req.body;

        if (!description || !entries || !Array.isArray(entries) || entries.length === 0) {
            return res.status(400).json({
                success: false,
                message: 'الوصف والقيود مطلوبة'
            });
        }

        // Validate entries balance
        const totalDebit = entries.reduce((sum, entry) => sum + (entry.debit_amount || 0), 0);
        const totalCredit = entries.reduce((sum, entry) => sum + (entry.credit_amount || 0), 0);

        if (Math.abs(totalDebit - totalCredit) > 0.01) {
            return res.status(400).json({
                success: false,
                message: 'إجمالي المدين يجب أن يساوي إجمالي الدائن'
            });
        }

        // Get voucher number
        const voucherNumber = await getNextCounterNumber(pool, 'VOUCHER');

        // Create journal entry
        const journalResult = await pool.request()
            .input('voucher_number', sql.NVarChar, voucherNumber)
            .input('voucher_date', sql.Date, new Date())
            .input('description', sql.NVarChar, description)
            .input('reference_type', sql.NVarChar, reference_type || null)
            .input('reference_id', sql.Int, reference_id || null)
            .input('total_debit', sql.Decimal(15,2), totalDebit)
            .input('total_credit', sql.Decimal(15,2), totalCredit)
            .input('branch_id', sql.Int, 1)
            .input('created_by', sql.Int, 1) // TODO: Get from session
            .query(`
                INSERT INTO JOURNAL_ENTRIES (
                    voucher_number, voucher_date, description, reference_type, reference_id,
                    total_debit, total_credit, branch_id, created_by
                )
                OUTPUT INSERTED.id, INSERTED.voucher_number
                VALUES (
                    @voucher_number, @voucher_date, @description, @reference_type, @reference_id,
                    @total_debit, @total_credit, @branch_id, @created_by
                )
            `);

        const journalEntryId = journalResult.recordset[0].id;

        // Create journal entry details
        for (const entry of entries) {
            await pool.request()
                .input('journal_entry_id', sql.Int, journalEntryId)
                .input('account_id', sql.Int, entry.account_id)
                .input('debit_amount', sql.Decimal(15,2), entry.debit_amount || 0)
                .input('credit_amount', sql.Decimal(15,2), entry.credit_amount || 0)
                .input('description', sql.NVarChar, entry.description || '')
                .query(`
                    INSERT INTO JOURNAL_ENTRY_DETAILS (
                        journal_entry_id, account_id, debit_amount, credit_amount, description
                    )
                    VALUES (
                        @journal_entry_id, @account_id, @debit_amount, @credit_amount, @description
                    )
                `);
        }

        res.json({
            success: true,
            message: 'تم إنشاء القيد بنجاح',
            data: {
                id: journalEntryId,
                voucher_number: journalResult.recordset[0].voucher_number
            }
        });
    } catch (error) {
        console.error('Error creating journal entry:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في إنشاء القيد',
            error: error.message
        });
    }
});

// GET /api/journal-entries - Get journal entries
app.get('/api/journal-entries', async (req, res) => {
    try {
        const pool = await getConnection();
        const { page = 1, limit = 10, date_from, date_to } = req.query;
        const offset = (page - 1) * limit;

        let whereClause = 'WHERE 1=1';
        if (date_from) whereClause += ` AND voucher_date >= '${date_from}'`;
        if (date_to) whereClause += ` AND voucher_date <= '${date_to}'`;

        const query = `
            SELECT
                id, voucher_number, voucher_date, description,
                total_debit, total_credit, created_at
            FROM JOURNAL_ENTRIES
            ${whereClause}
            ORDER BY voucher_date DESC, created_at DESC
            OFFSET ${offset} ROWS FETCH NEXT ${limit} ROWS ONLY
        `;

        const countQuery = `
            SELECT COUNT(*) as total
            FROM JOURNAL_ENTRIES
            ${whereClause}
        `;

        const [result, countResult] = await Promise.all([
            pool.request().query(query),
            pool.request().query(countQuery)
        ]);

        res.json({
            success: true,
            data: result.recordset,
            total: countResult.recordset[0].total,
            page: parseInt(page),
            limit: parseInt(limit)
        });
    } catch (error) {
        console.error('Error fetching journal entries:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في جلب القيود اليومية',
            error: error.message
        });
    }
});

// GET /api/chart-of-accounts - Get chart of accounts
app.get('/api/chart-of-accounts', async (req, res) => {
    try {
        const pool = await getConnection();
        const { account_type, is_final } = req.query;

        let whereClause = 'WHERE is_active = 1';
        if (account_type) whereClause += ` AND account_type = '${account_type}'`;
        if (is_final !== undefined) whereClause += ` AND is_final = ${is_final === 'true' ? 1 : 0}`;

        const query = `
            SELECT id, account_code, account_name, account_type, level_number, is_final
            FROM CHART_OF_ACCOUNTS
            ${whereClause}
            ORDER BY account_code
        `;

        const result = await pool.request().query(query);

        res.json({
            success: true,
            data: result.recordset
        });
    } catch (error) {
        console.error('Error fetching chart of accounts:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في جلب دليل الحسابات',
            error: error.message
        });
    }
});

// POST /api/cash-transactions - Create cash transaction (receipt/payment)
app.post('/api/cash-transactions', async (req, res) => {
    try {
        const pool = await getConnection();
        const {
            transaction_type,
            amount,
            description,
            received_from,
            paid_to,
            account_entries,
            reference_type,
            reference_id
        } = req.body;

        if (!transaction_type || !amount || !description || !account_entries) {
            return res.status(400).json({
                success: false,
                message: 'نوع المعاملة والمبلغ والوصف والحسابات مطلوبة'
            });
        }

        // Get transaction number
        const transactionNumber = await getNextCounterNumber(pool, transaction_type.toUpperCase());

        // Create journal entry first
        const journalEntries = [];

        if (transaction_type === 'RECEIPT') {
            // Receipt: Debit Cash, Credit other account
            journalEntries.push({
                account_id: 3, // Cash account (1110)
                debit_amount: amount,
                credit_amount: 0,
                description: `استلام نقدي من ${received_from}`
            });
        } else {
            // Payment: Credit Cash, Debit other account
            journalEntries.push({
                account_id: 3, // Cash account (1110)
                debit_amount: 0,
                credit_amount: amount,
                description: `صرف نقدي إلى ${paid_to}`
            });
        }

        // Add other account entries
        account_entries.forEach(entry => {
            journalEntries.push({
                account_id: entry.account_id,
                debit_amount: transaction_type === 'RECEIPT' ? 0 : entry.amount,
                credit_amount: transaction_type === 'RECEIPT' ? entry.amount : 0,
                description: entry.description || description
            });
        });

        // Create journal entry using stored procedure
        try {
            await pool.request()
                .input('description', sql.NVarChar, description)
                .input('entries', sql.NVarChar, JSON.stringify(journalEntries))
                .input('reference_type', sql.NVarChar, reference_type || 'CASH_TRANSACTION')
                .input('reference_id', sql.Int, reference_id || null)
                .output('journal_id', sql.Int)
                .execute('sp_create_journal_entry');
        } catch (journalError) {
            console.error('Error creating journal entry:', journalError);
            // Continue without failing the cash transaction
        }

        // Create cash transaction
        const result = await pool.request()
            .input('transaction_number', sql.NVarChar, transactionNumber)
            .input('transaction_date', sql.Date, new Date())
            .input('transaction_type', sql.NVarChar, transaction_type)
            .input('amount', sql.Decimal(15,2), amount)
            .input('description', sql.NVarChar, description)
            .input('received_from', sql.NVarChar, received_from || null)
            .input('paid_to', sql.NVarChar, paid_to || null)
            .input('reference_type', sql.NVarChar, reference_type || null)
            .input('reference_id', sql.Int, reference_id || null)
            .input('branch_id', sql.Int, 1)
            .input('created_by', sql.Int, 1)
            .query(`
                INSERT INTO CASH_TRANSACTIONS (
                    transaction_number, transaction_date, transaction_type, amount,
                    description, received_from, paid_to, reference_type, reference_id,
                    branch_id, created_by
                )
                OUTPUT INSERTED.id, INSERTED.transaction_number
                VALUES (
                    @transaction_number, @transaction_date, @transaction_type, @amount,
                    @description, @received_from, @paid_to, @reference_type, @reference_id,
                    @branch_id, @created_by
                )
            `);

        res.json({
            success: true,
            message: `تم إنشاء ${transaction_type === 'RECEIPT' ? 'سند الاستلام' : 'سند الصرف'} بنجاح`,
            data: {
                id: result.recordset[0].id,
                transaction_number: result.recordset[0].transaction_number
            }
        });
    } catch (error) {
        console.error('Error creating cash transaction:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في إنشاء المعاملة النقدية',
            error: error.message
        });
    }
});

// GET /api/cash-transactions - Get cash transactions
app.get('/api/cash-transactions', async (req, res) => {
    try {
        const pool = await getConnection();
        const { page = 1, limit = 10, transaction_type, date_from, date_to } = req.query;
        const offset = (page - 1) * limit;

        let whereClause = 'WHERE 1=1';
        if (transaction_type) whereClause += ` AND transaction_type = '${transaction_type}'`;
        if (date_from) whereClause += ` AND transaction_date >= '${date_from}'`;
        if (date_to) whereClause += ` AND transaction_date <= '${date_to}'`;

        const query = `
            SELECT
                id, transaction_number, transaction_date, transaction_type,
                amount, description, received_from, paid_to, created_at
            FROM CASH_TRANSACTIONS
            ${whereClause}
            ORDER BY transaction_date DESC, created_at DESC
            OFFSET ${offset} ROWS FETCH NEXT ${limit} ROWS ONLY
        `;

        const countQuery = `
            SELECT COUNT(*) as total
            FROM CASH_TRANSACTIONS
            ${whereClause}
        `;

        const [result, countResult] = await Promise.all([
            pool.request().query(query),
            pool.request().query(countQuery)
        ]);

        res.json({
            success: true,
            data: result.recordset,
            total: countResult.recordset[0].total,
            page: parseInt(page),
            limit: parseInt(limit)
        });
    } catch (error) {
        console.error('Error fetching cash transactions:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في جلب المعاملات النقدية',
            error: error.message
        });
    }
});

// GET /api/financial/summary - Get financial summary
app.get('/api/financial/summary', async (req, res) => {
    try {
        const pool = await getConnection();
        const { date_from, date_to } = req.query;

        let whereClause = '';
        if (date_from && date_to) {
            whereClause = `WHERE je.voucher_date BETWEEN '${date_from}' AND '${date_to}'`;
        }

        const query = `
            WITH AccountTotals AS (
                SELECT
                    coa.account_type,
                    SUM(jed.debit_amount) as total_debit,
                    SUM(jed.credit_amount) as total_credit
                FROM JOURNAL_ENTRY_DETAILS jed
                INNER JOIN JOURNAL_ENTRIES je ON jed.journal_entry_id = je.id
                INNER JOIN CHART_OF_ACCOUNTS coa ON jed.account_id = coa.id
                ${whereClause}
                GROUP BY coa.account_type
            )
            SELECT
                ISNULL(SUM(CASE WHEN account_type = 'ASSET' THEN total_debit - total_credit END), 0) as total_assets,
                ISNULL(SUM(CASE WHEN account_type = 'LIABILITY' THEN total_credit - total_debit END), 0) as total_liabilities,
                ISNULL(SUM(CASE WHEN account_type = 'EQUITY' THEN total_credit - total_debit END), 0) as total_equity,
                ISNULL(SUM(CASE WHEN account_type = 'REVENUE' THEN total_credit - total_debit END), 0) as total_revenue,
                ISNULL(SUM(CASE WHEN account_type = 'EXPENSE' THEN total_debit - total_credit END), 0) as total_expenses,
                ISNULL(SUM(CASE WHEN account_type = 'REVENUE' THEN total_credit - total_debit END), 0) -
                ISNULL(SUM(CASE WHEN account_type = 'EXPENSE' THEN total_debit - total_credit END), 0) as net_income
            FROM AccountTotals
        `;

        const result = await pool.request().query(query);

        res.json({
            success: true,
            data: result.recordset[0] || {
                total_assets: 0,
                total_liabilities: 0,
                total_equity: 0,
                total_revenue: 0,
                total_expenses: 0,
                net_income: 0
            }
        });
    } catch (error) {
        console.error('Error fetching financial summary:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في جلب الملخص المالي',
            error: error.message
        });
    }
});

// GET /api/financial/account-balances - Get account balances
app.get('/api/financial/account-balances', async (req, res) => {
    try {
        const pool = await getConnection();
        const { date_from, date_to } = req.query;

        let whereClause = '';
        if (date_from && date_to) {
            whereClause = `WHERE je.voucher_date BETWEEN '${date_from}' AND '${date_to}'`;
        }

        const query = `
            SELECT
                coa.account_code,
                coa.account_name,
                coa.account_type,
                ISNULL(SUM(jed.debit_amount), 0) as debit_balance,
                ISNULL(SUM(jed.credit_amount), 0) as credit_balance,
                ISNULL(SUM(jed.debit_amount), 0) - ISNULL(SUM(jed.credit_amount), 0) as net_balance
            FROM CHART_OF_ACCOUNTS coa
            LEFT JOIN JOURNAL_ENTRY_DETAILS jed ON coa.id = jed.account_id
            LEFT JOIN JOURNAL_ENTRIES je ON jed.journal_entry_id = je.id
            ${whereClause}
            WHERE coa.is_final = 1
            GROUP BY coa.account_code, coa.account_name, coa.account_type
            HAVING ISNULL(SUM(jed.debit_amount), 0) > 0 OR ISNULL(SUM(jed.credit_amount), 0) > 0
            ORDER BY coa.account_code
        `;

        const result = await pool.request().query(query);

        res.json({
            success: true,
            data: result.recordset
        });
    } catch (error) {
        console.error('Error fetching account balances:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في جلب أرصدة الحسابات',
            error: error.message
        });
    }
});

// ==================== WHATSAPP API ENDPOINTS ====================

// Initialize WhatsApp client
app.post('/api/whatsapp/initialize', async (req, res) => {
    try {
        console.log('📱 Initializing WhatsApp client...');
        const result = await whatsappService.initialize();

        res.json({
            success: true,
            message: 'WhatsApp initialization started',
            data: result
        });
    } catch (error) {
        console.error('Error initializing WhatsApp:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في تشغيل الواتساب',
            error: error.message
        });
    }
});

// Get WhatsApp status
app.get('/api/whatsapp/status', (req, res) => {
    try {
        const status = whatsappService.getStatus();

        res.json({
            success: true,
            data: status
        });
    } catch (error) {
        console.error('Error getting WhatsApp status:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في الحصول على حالة الواتساب',
            error: error.message
        });
    }
});

// Disconnect WhatsApp
app.post('/api/whatsapp/disconnect', async (req, res) => {
    try {
        console.log('📱 Disconnecting WhatsApp client...');
        const result = await whatsappService.disconnect();

        res.json({
            success: true,
            message: 'تم قطع الاتصال بالواتساب',
            data: result
        });
    } catch (error) {
        console.error('Error disconnecting WhatsApp:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في قطع الاتصال',
            error: error.message
        });
    }
});

// Restart WhatsApp client
app.post('/api/whatsapp/restart', async (req, res) => {
    try {
        console.log('📱 Restarting WhatsApp client...');

        // First disconnect
        await whatsappService.disconnect();

        // Wait a moment
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Then initialize again
        const result = await whatsappService.initialize();

        res.json({
            success: true,
            message: 'تم إعادة تشغيل الواتساب بنجاح',
            data: result
        });

    } catch (error) {
        console.error('Error restarting WhatsApp:', error);
        res.status(500).json({
            success: false,
            message: error.message || 'خطأ في إعادة تشغيل الواتساب',
            error: error.message
        });
    }
});

// Send single WhatsApp message
app.post('/api/whatsapp/send-real', async (req, res) => {
    try {
        const { phone_number, message, student_id, group_id, message_id } = req.body;

        if (!phone_number || !message) {
            return res.status(400).json({
                success: false,
                message: 'رقم الهاتف والرسالة مطلوبان'
            });
        }

        console.log(`📱 Sending real WhatsApp message to: ${phone_number}`);

        const result = await whatsappService.sendMessage(phone_number, message);

        res.json({
            success: true,
            message: 'تم إرسال الرسالة بنجاح',
            data: result
        });

    } catch (error) {
        console.error('Error sending WhatsApp message:', error);
        res.status(500).json({
            success: false,
            message: error.message || 'خطأ في إرسال الرسالة',
            error: error.message
        });
    }
});

// Send bulk WhatsApp messages
app.post('/api/whatsapp/send-bulk', async (req, res) => {
    try {
        const { recipients, message, group_id } = req.body;

        if (!recipients || !Array.isArray(recipients) || recipients.length === 0) {
            return res.status(400).json({
                success: false,
                message: 'قائمة المستقبلين مطلوبة'
            });
        }

        if (!message || !message.trim()) {
            return res.status(400).json({
                success: false,
                message: 'نص الرسالة مطلوب'
            });
        }

        console.log(`📱 Sending bulk WhatsApp messages to ${recipients.length} recipients`);

        // Format recipients for whatsapp service
        const messagesToSend = recipients.map(recipient => ({
            phoneNumber: recipient.phone,
            message: message,
            studentName: recipient.name
        }));

        if (messagesToSend.length === 0) {
            return res.status(400).json({
                success: false,
                message: 'لا توجد رسائل للإرسال'
            });
        }

        console.log(`📱 Sending ${messagesToSend.length} bulk WhatsApp messages...`);

        const results = await whatsappService.sendBulkMessages(messagesToSend);

        const successCount = results.filter(r => r.status === 'sent').length;
        const failedCount = results.filter(r => r.status === 'failed').length;

        res.json({
            success: true,
            message: `تم إرسال ${successCount} رسالة بنجاح، فشل ${failedCount} رسالة`,
            data: {
                results,
                summary: {
                    total: messagesToSend.length,
                    success: successCount,
                    failed: failedCount
                }
            }
        });

    } catch (error) {
        console.error('Error sending bulk WhatsApp messages:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في إرسال الرسائل',
            error: error.message
        });
    }
});

// Get WhatsApp message history
app.get('/api/whatsapp/history', async (req, res) => {
    try {
        const filters = {
            student_id: req.query.student_id ? parseInt(req.query.student_id) : null,
            group_id: req.query.group_id ? parseInt(req.query.group_id) : null,
            status: req.query.status,
            date_from: req.query.date_from ? new Date(req.query.date_from) : null,
            date_to: req.query.date_to ? new Date(req.query.date_to) : null,
            offset: req.query.offset ? parseInt(req.query.offset) : 0,
            limit: req.query.limit ? parseInt(req.query.limit) : 50
        };

        const history = await whatsappService.getMessageHistory(filters);

        res.json({
            success: true,
            data: history
        });

    } catch (error) {
        console.error('Error getting WhatsApp history:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في جلب تاريخ الرسائل',
            error: error.message
        });
    }
});

// ==================== WHATSAPP MESSAGES MANAGEMENT ====================

// Create WhatsApp message template
app.post('/api/whatsapp-messages', async (req, res) => {
    try {
        const pool = await getConnection();
        const { group_id, message_text, recipient_type, institution_id } = req.body;

        if (!group_id || !message_text || !recipient_type || !institution_id) {
            return res.status(400).json({
                success: false,
                message: 'جميع الحقول مطلوبة'
            });
        }

        const result = await pool.request()
            .input('group_id', sql.Int, group_id)
            .input('message_text', sql.NVarChar, message_text)
            .input('recipient_type', sql.NVarChar, recipient_type)
            .input('institution_id', sql.Int, institution_id)
            .input('status', sql.NVarChar, 'pending')
            .input('created_at', sql.DateTime, new Date())
            .query(`
                INSERT INTO WhatsAppMessages (
                    group_id, message_text, recipient_type, institution_id,
                    status, sent_count, failed_count, created_at
                )
                OUTPUT INSERTED.id, INSERTED.group_id, INSERTED.message_text, INSERTED.status
                VALUES (
                    @group_id, @message_text, @recipient_type, @institution_id,
                    @status, 0, 0, @created_at
                )
            `);

        res.json({
            success: true,
            message: 'تم حفظ الرسالة بنجاح',
            data: result.recordset[0]
        });

    } catch (error) {
        console.error('Error creating WhatsApp message:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في حفظ الرسالة',
            error: error.message
        });
    }
});

// Get WhatsApp messages with pagination
app.get('/api/whatsapp-messages', async (req, res) => {
    try {
        const pool = await getConnection();
        const {
            page = 1,
            limit = 10,
            group_id,
            status,
            institution_id,
            date_from,
            date_to
        } = req.query;

        const offset = (page - 1) * limit;
        let whereClause = 'WHERE 1=1';
        const params = [];

        if (institution_id) {
            whereClause += ' AND wm.institution_id = @institution_id';
            params.push({ name: 'institution_id', type: sql.Int, value: parseInt(institution_id) });
        }

        if (group_id) {
            whereClause += ' AND wm.group_id = @group_id';
            params.push({ name: 'group_id', type: sql.Int, value: parseInt(group_id) });
        }

        if (status) {
            whereClause += ' AND wm.status = @status';
            params.push({ name: 'status', type: sql.NVarChar, value: status });
        }

        if (date_from) {
            whereClause += ' AND wm.created_at >= @date_from';
            params.push({ name: 'date_from', type: sql.DateTime, value: new Date(date_from) });
        }

        if (date_to) {
            whereClause += ' AND wm.created_at <= @date_to';
            params.push({ name: 'date_to', type: sql.DateTime, value: new Date(date_to + ' 23:59:59') });
        }

        const query = `
            SELECT
                wm.*,
                g.group_name,
                g.subject_id,
                s.subject_name,
                t.teacher_name,
                FORMAT(wm.created_at, 'yyyy-MM-dd HH:mm') as formatted_date
            FROM WhatsAppMessages wm
            LEFT JOIN Groups g ON wm.group_id = g.id
            LEFT JOIN Subjects s ON g.subject_id = s.id
            LEFT JOIN Teachers t ON g.teacher_id = t.id
            ${whereClause}
            ORDER BY wm.created_at DESC
            OFFSET @offset ROWS FETCH NEXT @limit ROWS ONLY
        `;

        const countQuery = `
            SELECT COUNT(*) as total
            FROM WhatsAppMessages wm
            ${whereClause}
        `;

        const request = pool.request()
            .input('offset', sql.Int, offset)
            .input('limit', sql.Int, parseInt(limit));

        params.forEach(param => {
            request.input(param.name, param.type, param.value);
        });

        const [result, countResult] = await Promise.all([
            request.query(query),
            request.query(countQuery)
        ]);

        res.json({
            success: true,
            data: result.recordset,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total: countResult.recordset[0].total,
                pages: Math.ceil(countResult.recordset[0].total / limit)
            }
        });

    } catch (error) {
        console.error('Error fetching WhatsApp messages:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في جلب الرسائل',
            error: error.message
        });
    }
});

// Update message status and counts
app.put('/api/whatsapp-messages/:id/status', async (req, res) => {
    try {
        const pool = await getConnection();
        const { id } = req.params;
        const { status, sent_count, failed_count } = req.body;

        const result = await pool.request()
            .input('id', sql.Int, id)
            .input('status', sql.NVarChar, status)
            .input('sent_count', sql.Int, sent_count || 0)
            .input('failed_count', sql.Int, failed_count || 0)
            .input('updated_at', sql.DateTime, new Date())
            .query(`
                UPDATE WhatsAppMessages
                SET status = @status,
                    sent_count = @sent_count,
                    failed_count = @failed_count,
                    updated_at = @updated_at
                WHERE id = @id
            `);

        if (result.rowsAffected[0] === 0) {
            return res.status(404).json({
                success: false,
                message: 'الرسالة غير موجودة'
            });
        }

        res.json({
            success: true,
            message: 'تم تحديث حالة الرسالة بنجاح'
        });

    } catch (error) {
        console.error('Error updating message status:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في تحديث حالة الرسالة',
            error: error.message
        });
    }
});

// Server will be started at the end of the file

// ==================== ATTENDANCE SHEETS ENDPOINTS ====================

// POST /api/attendance-sheets - Create new attendance sheet
app.post('/api/attendance-sheets', async (req, res) => {
    try {
        const pool = await getConnection();
        const { institution_id, teacher_id, group_id, sheet_date, notes } = req.body;

        if (!institution_id || !teacher_id || !group_id || !sheet_date) {
            return res.status(400).json({
                success: false,
                message: 'جميع البيانات مطلوبة'
            });
        }

        // Generate sheet number
        const sheetNumberResult = await pool.request()
            .input('institution_id', sql.Int, institution_id)
            .query(`
                SELECT ISNULL(MAX(CAST(SUBSTRING(sheet_number, 4, LEN(sheet_number)) AS INT)), 0) + 1 as next_number
                FROM AttendanceSheets
                WHERE institution_id = @institution_id
                AND sheet_number LIKE 'SHT%'
            `);

        const nextNumber = sheetNumberResult.recordset[0].next_number;
        const sheetNumber = `SHT${String(nextNumber).padStart(4, '0')}`;

        const result = await pool.request()
            .input('sheet_number', sql.NVarChar, sheetNumber)
            .input('institution_id', sql.Int, institution_id)
            .input('teacher_id', sql.Int, teacher_id)
            .input('group_id', sql.Int, group_id)
            .input('sheet_date', sql.Date, sheet_date)
            .input('notes', sql.NVarChar, notes || '')
            .query(`
                INSERT INTO AttendanceSheets (
                    sheet_number, institution_id, teacher_id, group_id,
                    sheet_date, notes, created_at, is_active
                )
                OUTPUT INSERTED.id, INSERTED.sheet_number
                VALUES (
                    @sheet_number, @institution_id, @teacher_id, @group_id,
                    @sheet_date, @notes, GETDATE(), 1
                )
            `);

        res.json({
            success: true,
            message: 'تم إنشاء كشف الحضور بنجاح',
            data: result.recordset[0]
        });
    } catch (error) {
        console.error('Error creating attendance sheet:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في إنشاء كشف الحضور',
            error: error.message
        });
    }
});

// GET /api/attendance-sheets - Get attendance sheets with filters
app.get('/api/attendance-sheets', async (req, res) => {
    try {
        const pool = await getConnection();
        const { page = 1, limit = 10, institution_id, teacher_id, date_from, date_to } = req.query;
        const offset = (page - 1) * limit;

        let whereClause = 'WHERE ash.is_active = 1';
        if (institution_id) whereClause += ` AND ash.institution_id = ${institution_id}`;
        if (teacher_id) whereClause += ` AND ash.teacher_id = ${teacher_id}`;
        if (date_from) whereClause += ` AND ash.sheet_date >= '${date_from}'`;
        if (date_to) whereClause += ` AND ash.sheet_date <= '${date_to}'`;

        const query = `
            SELECT
                ash.id, ash.sheet_number, ash.sheet_date, ash.notes,
                ash.created_at, ash.institution_id, ash.teacher_id, ash.group_id,
                i.institution_name,
                t.teacher_name,
                g.group_name,
                COUNT(ar.id) as total_records,
                SUM(CASE WHEN ar.status = 'present' THEN 1 ELSE 0 END) as present_count,
                SUM(CASE WHEN ar.status = 'absent' THEN 1 ELSE 0 END) as absent_count
            FROM AttendanceSheets ash
            LEFT JOIN Institutions i ON ash.institution_id = i.id
            LEFT JOIN Teachers t ON ash.teacher_id = t.id
            LEFT JOIN Groups g ON ash.group_id = g.id
            LEFT JOIN AttendanceRecords ar ON ash.id = ar.sheet_id
            ${whereClause}
            GROUP BY ash.id, ash.sheet_number, ash.sheet_date, ash.notes, ash.created_at,
                     ash.institution_id, ash.teacher_id, ash.group_id,
                     i.institution_name, t.teacher_name, g.group_name
            ORDER BY ash.created_at DESC
            OFFSET ${offset} ROWS FETCH NEXT ${limit} ROWS ONLY
        `;

        const countQuery = `
            SELECT COUNT(*) as total
            FROM AttendanceSheets ash
            ${whereClause}
        `;

        const [result, countResult] = await Promise.all([
            pool.request().query(query),
            pool.request().query(countQuery)
        ]);

        res.json({
            success: true,
            data: result.recordset,
            total: countResult.recordset[0].total,
            page: parseInt(page),
            limit: parseInt(limit)
        });
    } catch (error) {
        console.error('Error fetching attendance sheets:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في جلب كشوف الحضور',
            error: error.message
        });
    }
});

// GET /api/attendance-sheets/:id - Get single attendance sheet with records
app.get('/api/attendance-sheets/:id', async (req, res) => {
    try {
        const pool = await getConnection();
        const { id } = req.params;

        // Get sheet info
        const sheetQuery = `
            SELECT
                ash.id, ash.sheet_number, ash.sheet_date, ash.notes,
                ash.institution_id, ash.teacher_id, ash.group_id,
                i.institution_name, t.teacher_name, g.group_name
            FROM AttendanceSheets ash
            LEFT JOIN Institutions i ON ash.institution_id = i.id
            LEFT JOIN Teachers t ON ash.teacher_id = t.id
            LEFT JOIN Groups g ON ash.group_id = g.id
            WHERE ash.id = @id AND ash.is_active = 1
        `;

        // Get attendance records
        const recordsQuery = `
            SELECT
                ar.id, ar.student_id, ar.status, ar.scan_time, ar.notes,
                ar.student_original_group_id, ar.sheet_group_id,
                s.student_name, s.student_code,
                og.group_name as original_group_name,
                sg.group_name as sheet_group_name,
                CASE
                    WHEN ar.student_original_group_id != ar.sheet_group_id THEN 1
                    ELSE 0
                END as is_different_group
            FROM AttendanceRecords ar
            INNER JOIN Students s ON ar.student_id = s.id
            LEFT JOIN Groups og ON ar.student_original_group_id = og.id
            LEFT JOIN Groups sg ON ar.sheet_group_id = sg.id
            WHERE ar.sheet_id = @id
            ORDER BY ar.scan_time DESC
        `;

        const [sheetResult, recordsResult] = await Promise.all([
            pool.request().input('id', sql.Int, parseInt(id)).query(sheetQuery),
            pool.request().input('id', sql.Int, parseInt(id)).query(recordsQuery)
        ]);

        if (sheetResult.recordset.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'كشف الحضور غير موجود'
            });
        }

        const sheet = sheetResult.recordset[0];
        sheet.records = recordsResult.recordset;

        res.json({
            success: true,
            data: sheet
        });
    } catch (error) {
        console.error('Error fetching attendance sheet:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في جلب كشف الحضور',
            error: error.message
        });
    }
});

// POST /api/attendance-sheets/:id/scan - Scan student for attendance
app.post('/api/attendance-sheets/:id/scan', async (req, res) => {
    try {
        const pool = await getConnection();
        const { id } = req.params;
        const { student_code } = req.body;

        if (!student_code) {
            return res.status(400).json({
                success: false,
                message: 'كود الطالب مطلوب'
            });
        }

        // Get sheet info
        const sheetResult = await pool.request()
            .input('sheet_id', sql.Int, parseInt(id))
            .query(`
                SELECT ash.id, ash.group_id as sheet_group_id, ash.sheet_number
                FROM AttendanceSheets ash
                WHERE ash.id = @sheet_id AND ash.is_active = 1
            `);

        if (sheetResult.recordset.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'كشف الحضور غير موجود'
            });
        }

        const sheet = sheetResult.recordset[0];

        // Get student info with their groups from StudentGroups table
        // First try exact match, then try partial match if no exact match found
        let studentResult = await pool.request()
            .input('student_code', sql.NVarChar, student_code)
            .query(`
                SELECT s.id, s.student_name, s.student_code,
                       sg.group_id as original_group_id,
                       g.group_name as original_group_name
                FROM Students s
                LEFT JOIN StudentGroups sg ON s.id = sg.student_id
                LEFT JOIN Groups g ON sg.group_id = g.id
                WHERE s.student_code = @student_code AND s.is_deleted = 0
                ORDER BY sg.created_at DESC
            `);

        // If no exact match found and input is numeric, try partial match
        if (studentResult.recordset.length === 0 && /^\d+$/.test(student_code)) {
            studentResult = await pool.request()
                .input('student_code_partial', sql.NVarChar, `%${student_code}`)
                .query(`
                    SELECT s.id, s.student_name, s.student_code,
                           sg.group_id as original_group_id,
                           g.group_name as original_group_name
                    FROM Students s
                    LEFT JOIN StudentGroups sg ON s.id = sg.student_id
                    LEFT JOIN Groups g ON sg.group_id = g.id
                    WHERE s.student_code LIKE @student_code_partial AND s.is_deleted = 0
                    ORDER BY sg.created_at DESC
                `);

            // If multiple matches found, return error
            if (studentResult.recordset.length > 1) {
                return res.status(400).json({
                    success: false,
                    message: `تم العثور على ${studentResult.recordset.length} طلاب بهذا الكود. يرجى إدخال الكود كاملاً`,
                    suggestions: studentResult.recordset.map(s => ({
                        code: s.student_code,
                        name: s.student_name
                    }))
                });
            }
        }

        if (studentResult.recordset.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'الطالب غير موجود'
            });
        }

        const student = studentResult.recordset[0];

        // Check if student already scanned
        const existingRecord = await pool.request()
            .input('sheet_id', sql.Int, parseInt(id))
            .input('student_id', sql.Int, student.id)
            .query(`
                SELECT id FROM AttendanceRecords
                WHERE sheet_id = @sheet_id AND student_id = @student_id
            `);

        if (existingRecord.recordset.length > 0) {
            return res.status(400).json({
                success: false,
                message: 'الطالب مسجل مسبقاً في هذا الكشف'
            });
        }

        // Insert attendance record
        const insertResult = await pool.request()
            .input('sheet_id', sql.Int, parseInt(id))
            .input('student_id', sql.Int, student.id)
            .input('student_original_group_id', sql.Int, student.original_group_id)
            .input('sheet_group_id', sql.Int, sheet.sheet_group_id)
            .input('scan_time', sql.DateTime, new Date())
            .query(`
                INSERT INTO AttendanceRecords (
                    sheet_id, student_id, student_original_group_id, sheet_group_id,
                    status, scan_time, created_at
                )
                OUTPUT INSERTED.id
                VALUES (
                    @sheet_id, @student_id, @student_original_group_id, @sheet_group_id,
                    'present', @scan_time, GETDATE()
                )
            `);

        // Return student info with group comparison
        const isDifferentGroup = student.original_group_id !== sheet.sheet_group_id;

        res.json({
            success: true,
            message: 'تم تسجيل حضور الطالب بنجاح',
            data: {
                record_id: insertResult.recordset[0].id,
                student: {
                    id: student.id,
                    name: student.student_name,
                    code: student.student_code,
                    original_group_id: student.original_group_id,
                    original_group_name: student.original_group_name
                },
                sheet_group_id: sheet.sheet_group_id,
                is_different_group: isDifferentGroup,
                scan_time: new Date()
            }
        });
    } catch (error) {
        console.error('Error scanning student:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في تسجيل حضور الطالب',
            error: error.message
        });
    }
});

// POST /api/attendance-sheets/:id/close - Close attendance sheet
app.post('/api/attendance-sheets/:id/close', async (req, res) => {
    try {
        const pool = await getConnection();
        const { id } = req.params;
        console.log(`🔒 Closing attendance sheet: ${id}`);

        // Get sheet info
        const sheetResult = await pool.request()
            .input('sheet_id', sql.Int, parseInt(id))
            .query(`
                SELECT ash.id, ash.group_id, ash.sheet_date, ash.status
                FROM AttendanceSheets ash
                WHERE ash.id = @sheet_id AND ash.is_active = 1
            `);

        if (sheetResult.recordset.length === 0) {
            return res.status(404).json({
                success: false,
                message: 'كشف الحضور غير موجود'
            });
        }

        const sheet = sheetResult.recordset[0];

        // Get all students in the group
        const studentsResult = await pool.request()
            .input('group_id', sql.Int, sheet.group_id)
            .query(`
                SELECT DISTINCT s.id, s.student_code, s.student_name
                FROM Students s
                INNER JOIN StudentGroups sg ON s.id = sg.student_id
                WHERE sg.group_id = @group_id AND s.is_deleted = 0
            `);

        // Get students who already have attendance records
        const presentStudentsResult = await pool.request()
            .input('sheet_id', sql.Int, parseInt(id))
            .query(`
                SELECT DISTINCT student_id
                FROM AttendanceRecords
                WHERE sheet_id = @sheet_id
            `);

        const presentStudentIds = presentStudentsResult.recordset.map(r => r.student_id);
        const allStudents = studentsResult.recordset;
        const absentStudents = allStudents.filter(s => !presentStudentIds.includes(s.id));

        // Add absent records for missing students
        let absentCount = 0;
        for (const student of absentStudents) {
            await pool.request()
                .input('sheet_id', sql.Int, parseInt(id))
                .input('student_id', sql.Int, student.id)
                .input('student_original_group_id', sql.Int, sheet.group_id)
                .input('sheet_group_id', sql.Int, sheet.group_id)
                .input('scan_time', sql.DateTime, new Date())
                .query(`
                    INSERT INTO AttendanceRecords (
                        sheet_id, student_id, student_original_group_id, sheet_group_id,
                        status, scan_time, created_at
                    )
                    VALUES (
                        @sheet_id, @student_id, @student_original_group_id, @sheet_group_id,
                        'absent', @scan_time, GETDATE()
                    )
                `);
            absentCount++;
        }

        // Update sheet status to closed
        await pool.request()
            .input('sheet_id', sql.Int, parseInt(id))
            .query(`
                UPDATE AttendanceSheets
                SET status = 'closed', updated_at = GETDATE()
                WHERE id = @sheet_id
            `);

        res.json({
            success: true,
            message: `تم إقفال الكشف بنجاح. تم تسجيل ${absentCount} طالب كغائب`,
            data: {
                total_students: allStudents.length,
                present_count: presentStudentIds.length,
                absent_count: absentCount
            }
        });
    } catch (error) {
        console.error('Error closing attendance sheet:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في إقفال كشف الحضور',
            error: error.message
        });
    }
});

// DELETE /api/attendance-records/:id - Delete attendance record
app.delete('/api/attendance-records/:id', async (req, res) => {
    try {
        const pool = await getConnection();
        const { id } = req.params;

        const result = await pool.request()
            .input('id', sql.Int, parseInt(id))
            .query(`
                DELETE FROM AttendanceRecords
                WHERE id = @id
            `);

        if (result.rowsAffected[0] === 0) {
            return res.status(404).json({
                success: false,
                message: 'سجل الحضور غير موجود'
            });
        }

        res.json({
            success: true,
            message: 'تم حذف سجل الحضور بنجاح'
        });
    } catch (error) {
        console.error('Error deleting attendance record:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في حذف سجل الحضور',
            error: error.message
        });
    }
});

// DELETE /api/attendance-sheets/:id - Delete attendance sheet
app.delete('/api/attendance-sheets/:id', async (req, res) => {
    try {
        const pool = await getConnection();
        const { id } = req.params;

        // Delete all records first
        await pool.request()
            .input('sheet_id', sql.Int, parseInt(id))
            .query(`DELETE FROM AttendanceRecords WHERE sheet_id = @sheet_id`);

        // Delete the sheet
        const result = await pool.request()
            .input('id', sql.Int, parseInt(id))
            .query(`
                UPDATE AttendanceSheets
                SET is_active = 0, updated_at = GETDATE()
                WHERE id = @id
            `);

        if (result.rowsAffected[0] === 0) {
            return res.status(404).json({
                success: false,
                message: 'كشف الحضور غير موجود'
            });
        }

        res.json({
            success: true,
            message: 'تم حذف كشف الحضور بنجاح'
        });
    } catch (error) {
        console.error('Error deleting attendance sheet:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في حذف كشف الحضور',
            error: error.message
        });
    }
});

// Mock data for attendance and subjects
const mockAttendanceSheets = [
    { id: 17, sheet_code: 'SHT0017', teacher_id: 48, group_id: 1, date: '2024-01-20', status: 'open' }
];

const mockAttendanceRecords = [
    { id: 1, sheet_id: 17, student_code: 'STU001', student_name: 'أحمد محمد علي', status: 'present', scan_time: '2024-01-20 10:30:00' },
    { id: 2, sheet_id: 17, student_code: 'STU002', student_name: 'فاطمة أحمد حسن', status: 'absent', scan_time: null }
];

const mockSubjects = [
    { id: 1, subject_name: 'رياضيات', subject_code: 'MATH001', teacher_id: 48, grade_id: 33 },
    { id: 2, subject_name: 'فيزياء', subject_code: 'PHY001', teacher_id: 49, grade_id: 34 }
];

// Get attendance sheet with fallback
app.get('/api/attendance/scanner/:id', async (req, res) => {
    try {
        const { id } = req.params;

        try {
            const pool = await getConnection();
            // Database code here...
            console.log('Using real database for attendance');
        } catch (dbError) {
            console.log('Database failed, using mock attendance data');

            const sheet = mockAttendanceSheets.find(s => s.id == id);
            if (!sheet) {
                return res.status(404).json({ success: false, message: 'كشف الحضور غير موجود' });
            }

            const records = mockAttendanceRecords.filter(r => r.sheet_id == id);
            return res.json({ success: true, data: { ...sheet, records } });
        }
    } catch (error) {
        console.error('Error fetching attendance sheet:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في جلب كشف الحضور',
            error: error.message
        });
    }
});

// Update attendance with fallback
app.post('/api/attendance/scan', async (req, res) => {
    try {
        const { sheet_id, student_code, status = 'present' } = req.body;

        try {
            const pool = await getConnection();
            // Database code here...
            console.log('Using real database for attendance scan');
        } catch (dbError) {
            console.log('Database failed, using mock attendance scan');

            const existingIndex = mockAttendanceRecords.findIndex(r => r.sheet_id == sheet_id && r.student_code == student_code);

            if (existingIndex >= 0) {
                mockAttendanceRecords[existingIndex].status = status;
                mockAttendanceRecords[existingIndex].scan_time = new Date().toISOString();
            } else {
                const student = mockStudents.find(s => s.student_code == student_code);
                if (student) {
                    mockAttendanceRecords.push({
                        id: mockAttendanceRecords.length + 1,
                        sheet_id: parseInt(sheet_id),
                        student_code,
                        student_name: student.student_name,
                        status,
                        scan_time: new Date().toISOString()
                    });
                }
            }

            console.log('✅ Attendance updated:', { sheet_id, student_code, status });
            return res.json({ success: true, message: 'تم تسجيل الحضور بنجاح' });
        }
    } catch (error) {
        console.error('Error updating attendance:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في تسجيل الحضور',
            error: error.message
        });
    }
});

// Close attendance sheet with fallback
app.post('/api/attendance/close/:id', async (req, res) => {
    try {
        const { id } = req.params;

        try {
            const pool = await getConnection();
            // Database code here...
            console.log('Using real database for attendance close');
        } catch (dbError) {
            console.log('Database failed, using mock attendance close');

            const sheetIndex = mockAttendanceSheets.findIndex(s => s.id == id);

            if (sheetIndex === -1) {
                return res.status(404).json({ success: false, message: 'كشف الحضور غير موجود' });
            }

            mockAttendanceSheets[sheetIndex].status = 'closed';
            console.log('✅ Attendance sheet closed:', id);
            return res.json({ success: true, message: 'تم إقفال كشف الحضور بنجاح' });
        }
    } catch (error) {
        console.error('Error closing attendance sheet:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في إقفال كشف الحضور',
            error: error.message
        });
    }
});

// Subjects API with fallback
app.get('/api/subjects', async (req, res) => {
    try {
        const { teacher_id, grade_id } = req.query;

        try {
            const pool = await getConnection();
            // Database code here...
            console.log('Using real database for subjects');
        } catch (dbError) {
            console.log('Database failed, using mock subjects data');

            let filtered = [...mockSubjects];

            if (teacher_id) filtered = filtered.filter(s => s.teacher_id == teacher_id);
            if (grade_id) filtered = filtered.filter(s => s.grade_id == grade_id);

            return res.json({ success: true, data: filtered });
        }
    } catch (error) {
        console.error('Error fetching subjects:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في جلب المواد',
            error: error.message
        });
    }
});

// Get subjects by teacher and grade for groups
app.get('/api/subjects/by-teacher-grade', async (req, res) => {
    try {
        const { teacher_id, grade_id } = req.query;
        console.log(`🔍 Getting subjects for teacher ${teacher_id} and grade ${grade_id}`);

        if (!teacher_id || !grade_id) {
            return res.status(400).json({
                success: false,
                message: 'معرف المدرس والصف مطلوبان'
            });
        }

        const pool = await getConnection();

        // Get subjects that the teacher teaches for the specific grade
        // Using Groups table to find subjects taught by teacher for specific grade
        const query = `
            SELECT DISTINCT s.id, s.subject_name, s.subject_code
            FROM Subjects s
            INNER JOIN Groups g ON s.id = g.subject_id
            WHERE g.teacher_id = @teacher_id
            AND g.grade_id = @grade_id
            AND s.is_deleted = 0
            AND g.is_deleted = 0
            ORDER BY s.subject_name
        `;

        const result = await pool.request()
            .input('teacher_id', sql.Int, teacher_id)
            .input('grade_id', sql.Int, grade_id)
            .query(query);

        console.log(`✅ Found ${result.recordset.length} subjects for teacher ${teacher_id} and grade ${grade_id}`);

        res.json({
            success: true,
            data: result.recordset
        });

    } catch (error) {
        console.error('Error fetching subjects by teacher and grade:', error);
        res.status(500).json({
            success: false,
            message: 'خطأ في جلب المواد',
            error: error.message
        });
    }
});

// 404 handler - Must be last
app.use('*', (req, res) => {
    res.status(404).json({
        success: false,
        message: 'Route not found'
    });
});

// Start server with proper error handling
try {
    const server = app.listen(PORT, '0.0.0.0', () => {
        console.log('🎉 ================================');
        console.log(`🚀 REAL SERVER RUNNING on http://0.0.0.0:${PORT}`);
        console.log(`📊 Health check: http://localhost:${PORT}/health`);
        console.log(`🔗 API Health: http://localhost:${PORT}/api/health`);
        console.log(`📝 API Base: http://localhost:${PORT}/api`);
        console.log(`✅ Database connection loaded`);
        console.log(`✅ WhatsApp service loaded successfully`);
        console.log(`🗄️ Connected to real database: terraedu01`);
        console.log(`📱 WhatsApp API endpoints available`);
        console.log(`🌐 Network: http://*************:${PORT}`);
        console.log(`🌍 Domain: http://terraaa.ddns.net:${PORT}`);
        console.log(`📋 Attendance APIs ready`);
        console.log(`📚 Subjects APIs ready`);
        console.log(`💰 Financial APIs ready`);
        console.log(`📄 Swagger UI: http://localhost:${PORT}/api-docs`);
        console.log(`🌐 Available on all network interfaces`);
        console.log('🎉 ================================');
    });

    // Handle server errors
    server.on('error', (error) => {
        if (error.code === 'EADDRINUSE') {
            console.error(`❌ Port ${PORT} is already in use!`);
            console.log(`💡 Try running: taskkill /f /im node.exe`);
            console.log(`💡 Or use a different port`);
            process.exit(1);
        } else {
            console.error('❌ Server error:', error);
            process.exit(1);
        }
    });
} catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
}

// Graceful shutdown
process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down server...');
    server.close(() => {
        console.log('✅ Server closed');
        process.exit(0);
    });
});

module.exports = app;
