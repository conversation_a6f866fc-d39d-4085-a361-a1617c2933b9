// اختبار مباشر مع الأرقام المحددة
const axios = require('axios');

const API_BASE = 'http://localhost:7080/api';

// الأرقام التي أعطيتها
const TEST_PHONES = [
    '01121381487', // رقم الطالب
    '01008198992', // رقم ولي الأمر 1
    '01017839220'  // رقم ولي الأمر 2
];

async function testDirectPhones() {
    console.log('🎯 اختبار مباشر مع الأرقام المحددة...\n');
    
    try {
        // 1. التحقق من حالة الواتساب
        console.log('📊 التحقق من حالة الواتساب...');
        const statusResponse = await axios.get(`${API_BASE}/whatsapp/status`);
        console.log(`حالة الواتساب: ${statusResponse.data.data.status}`);
        console.log(`جاهز: ${statusResponse.data.data.isReady}\n`);
        
        if (!statusResponse.data.data.isReady) {
            console.log('⚠️ الواتساب غير جاهز، محاولة التهيئة...');
            await axios.post(`${API_BASE}/whatsapp/initialize`);
            console.log('⏳ انتظار 5 ثوانٍ...');
            await new Promise(resolve => setTimeout(resolve, 5000));
        }
        
        // 2. اختبار إرسال لكل رقم منفرد
        console.log('📱 اختبار الإرسال المنفرد لكل رقم:\n');
        
        for (let i = 0; i < TEST_PHONES.length; i++) {
            const phone = TEST_PHONES[i];
            const phoneType = i === 0 ? 'الطالب' : `ولي الأمر ${i}`;
            
            console.log(`${i + 1}. اختبار إرسال لرقم ${phoneType}: ${phone}`);
            
            try {
                const response = await axios.post(`${API_BASE}/whatsapp/send-real`, {
                    phone_number: phone,
                    message: `🧪 رسالة اختبار لرقم ${phoneType}
📱 الرقم: ${phone}
⏰ الوقت: ${new Date().toLocaleString('ar-EG')}
✅ إذا وصلتك هذه الرسالة، فالنظام يعمل بنجاح!`
                });
                
                if (response.data.success) {
                    console.log(`   ✅ تم الإرسال بنجاح`);
                    console.log(`   📋 تفاصيل: ${response.data.data.chatId}`);
                } else {
                    console.log(`   ❌ فشل الإرسال: ${response.data.message}`);
                }
                
            } catch (error) {
                console.log(`   ❌ خطأ في الإرسال: ${error.response?.data?.error || error.message}`);
            }
            
            // انتظار بين الرسائل
            if (i < TEST_PHONES.length - 1) {
                console.log('   ⏳ انتظار 3 ثوانٍ...\n');
                await new Promise(resolve => setTimeout(resolve, 3000));
            }
        }
        
        // 3. اختبار الإرسال المجمع
        console.log('\n📤 اختبار الإرسال المجمع:');
        
        const bulkRecipients = [
            { 
                phone: TEST_PHONES[0], 
                name: 'أحمد محمد علي (الطالب)' 
            },
            { 
                student_phone: TEST_PHONES[0],
                father_phone: TEST_PHONES[1], 
                name: 'أحمد محمد علي (أب)' 
            },
            { 
                student_phone: TEST_PHONES[0],
                mother_phone: TEST_PHONES[2], 
                name: 'أحمد محمد علي (أم)' 
            }
        ];
        
        try {
            const bulkResponse = await axios.post(`${API_BASE}/whatsapp/send-bulk`, {
                recipients: bulkRecipients,
                message: `📢 رسالة مجمعة اختبارية
👥 تم إرسالها لجميع أرقام العائلة
⏰ الوقت: ${new Date().toLocaleString('ar-EG')}
🎯 اختبار نظام إدارة المراكز التعليمية`
            });
            
            console.log('نتيجة الإرسال المجمع:');
            console.log(`✅ نجح: ${bulkResponse.data.data.summary.success}`);
            console.log(`❌ فشل: ${bulkResponse.data.data.summary.failed}`);
            console.log(`📊 المجموع: ${bulkResponse.data.data.summary.total}`);
            
            // تفاصيل كل رسالة
            bulkResponse.data.data.results.forEach((result, index) => {
                const recipient = bulkRecipients[index];
                console.log(`\n${index + 1}. ${recipient.name}:`);
                console.log(`   حالة: ${result.success ? '✅ نجح' : '❌ فشل'}`);
                if (result.success) {
                    console.log(`   الرقم: ${result.to}`);
                    console.log(`   Chat ID: ${result.chatId}`);
                } else {
                    console.log(`   خطأ: ${result.error}`);
                }
            });
            
        } catch (error) {
            console.log(`❌ خطأ في الإرسال المجمع: ${error.response?.data?.error || error.message}`);
        }
        
        console.log('\n🎉 انتهى الاختبار!');
        console.log('📱 تحقق من الهواتف للتأكد من وصول الرسائل');
        
    } catch (error) {
        console.error('💥 خطأ عام في الاختبار:', error.message);
    }
}

// تشغيل الاختبار
testDirectPhones();
