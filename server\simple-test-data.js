// إنشاء بيانات اختبار مبسطة
const { getConnection } = require('./config/database');

async function createSimpleTestData() {
    let connection;
    
    try {
        connection = await getConnection();
        console.log('🔗 متصل بقاعدة البيانات...');

        // 1. إضافة طالب اختبار مباشرة
        console.log('👨‍🎓 إضافة طالب اختبار...');
        
        try {
            const studentResult = await connection.request()
                .input('student_name', 'أحمد محمد علي - اختبار واتساب')
                .input('student_phone', '01121381487')
                .input('father_name', 'محمد علي أحمد')
                .input('father_phone', '01008198992')
                .input('mother_name', 'فاطمة حسن')
                .input('mother_phone', '01017839220')
                .query(`
                    INSERT INTO students (student_name, student_phone, father_name, father_phone, mother_name, mother_phone)
                    OUTPUT INSERTED.id
                    VALUES (@student_name, @student_phone, @father_name, @father_phone, @mother_name, @mother_phone)
                `);
            
            const studentId = studentResult.recordset[0].id;
            console.log(`✅ تم إنشاء الطالب برقم: ${studentId}`);

            // 2. ربط الطالب بمجموعة موجودة (استخدام أول مجموعة متاحة)
            console.log('🔗 البحث عن مجموعة متاحة...');
            
            const groupResult = await connection.request().query(`
                SELECT TOP 1 id, group_name FROM groups ORDER BY id
            `);
            
            if (groupResult.recordset.length > 0) {
                const groupId = groupResult.recordset[0].id;
                const groupName = groupResult.recordset[0].group_name;
                
                console.log(`📚 تم العثور على المجموعة: ${groupName} (ID: ${groupId})`);
                
                // ربط الطالب بالمجموعة
                await connection.request()
                    .input('student_id', studentId)
                    .input('group_id', groupId)
                    .query(`
                        INSERT INTO student_groups (student_id, group_id)
                        VALUES (@student_id, @group_id)
                    `);
                
                console.log('✅ تم ربط الطالب بالمجموعة');
                
                // 3. التحقق من البيانات
                console.log('\n📊 التحقق من البيانات:');
                const verifyResult = await connection.request()
                    .input('group_id', groupId)
                    .query(`
                        SELECT 
                            s.id,
                            s.student_name,
                            s.student_phone,
                            s.father_name,
                            s.father_phone,
                            s.mother_name,
                            s.mother_phone,
                            g.group_name
                        FROM students s
                        INNER JOIN student_groups sg ON s.id = sg.student_id
                        INNER JOIN groups g ON sg.group_id = g.id
                        WHERE g.id = @group_id AND s.id = ${studentId}
                    `);

                if (verifyResult.recordset.length > 0) {
                    const student = verifyResult.recordset[0];
                    console.log(`
🎯 بيانات الطالب المُنشأ:
👨‍🎓 الاسم: ${student.student_name}
📱 رقم الطالب: ${student.student_phone}
👨 الأب: ${student.father_name} - ${student.father_phone}
👩 الأم: ${student.mother_name} - ${student.mother_phone}
📚 المجموعة: ${student.group_name}
                    `);
                    
                    console.log(`\n🎯 معرف المجموعة للاختبار: ${groupId}`);
                    console.log(`🎯 معرف الطالب للاختبار: ${studentId}`);
                    
                    return { groupId, studentId };
                } else {
                    console.log('❌ لم يتم العثور على البيانات بعد الإنشاء');
                }
                
            } else {
                console.log('❌ لم يتم العثور على أي مجموعة');
                
                // إنشاء مجموعة بسيطة
                console.log('📚 إنشاء مجموعة جديدة...');
                const newGroupResult = await connection.request()
                    .input('group_name', 'مجموعة اختبار الواتساب')
                    .query(`
                        INSERT INTO groups (group_name)
                        OUTPUT INSERTED.id
                        VALUES (@group_name)
                    `);
                
                const newGroupId = newGroupResult.recordset[0].id;
                console.log(`✅ تم إنشاء المجموعة برقم: ${newGroupId}`);
                
                // ربط الطالب بالمجموعة الجديدة
                await connection.request()
                    .input('student_id', studentId)
                    .input('group_id', newGroupId)
                    .query(`
                        INSERT INTO student_groups (student_id, group_id)
                        VALUES (@student_id, @group_id)
                    `);
                
                console.log('✅ تم ربط الطالب بالمجموعة الجديدة');
                
                return { groupId: newGroupId, studentId };
            }
            
        } catch (error) {
            console.error('❌ خطأ في إنشاء الطالب:', error.message);
            
            // محاولة استخدام أسماء أعمدة مختلفة
            console.log('🔄 محاولة بأسماء أعمدة مختلفة...');
            
            const studentResult2 = await connection.request()
                .input('name', 'أحمد محمد علي - اختبار واتساب')
                .input('phone', '01121381487')
                .query(`
                    INSERT INTO students (name, phone)
                    OUTPUT INSERTED.id
                    VALUES (@name, @phone)
                `);
            
            const studentId = studentResult2.recordset[0].id;
            console.log(`✅ تم إنشاء الطالب برقم: ${studentId} (بأسماء أعمدة مختلفة)`);
            
            return { groupId: 1, studentId }; // استخدام مجموعة افتراضية
        }

    } catch (error) {
        console.error('❌ خطأ عام:', error);
        throw error;
    } finally {
        if (connection) {
            await connection.close();
            console.log('🔒 تم إغلاق الاتصال بقاعدة البيانات');
        }
    }
}

// تشغيل إنشاء البيانات
createSimpleTestData()
    .then(result => {
        console.log('\n🎉 تم إنشاء بيانات الاختبار بنجاح!');
        if (result) {
            console.log(`📋 استخدم معرف المجموعة: ${result.groupId} للاختبار`);
            console.log(`👨‍🎓 معرف الطالب: ${result.studentId}`);
        }
    })
    .catch(error => {
        console.error('💥 فشل في إنشاء بيانات الاختبار:', error.message);
    });
