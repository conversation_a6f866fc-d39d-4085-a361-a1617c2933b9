{"ast": null, "code": "var _jsxFileName = \"I:\\\\\\u0628\\u0631\\u0645\\u062C\\u0629\\\\\\u062A\\u0639\\u0644\\u064A\\u0645\\u064A ali2 2\\\\\\u062A\\u0639\\u0644\\u064A\\u0645\\u064A 2\\\\\\u0627\\u062F\\u0627\\u0631\\u0629 \\u0645\\u0631\\u0627\\u0643\\u0632 \\u062A\\u0639\\u0644\\u064A\\u0645\\u064A\\u0629\\\\edu 2\\\\client\\\\src\\\\components\\\\ui\\\\card.tsx\";\nimport React from 'react';\nimport { Card as Mui<PERSON>ard, Card<PERSON>ontent as <PERSON><PERSON><PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>eader } from '@mui/material';\nimport { Typography } from '@mui/material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const Card = ({\n  children,\n  className\n}) => {\n  return /*#__PURE__*/_jsxDEV(MuiCard, {\n    className: className,\n    sx: {\n      mb: 2\n    },\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 27,\n    columnNumber: 9\n  }, this);\n};\n_c = Card;\nexport const CardContent = ({\n  children,\n  className\n}) => {\n  return /*#__PURE__*/_jsxDEV(MuiCardContent, {\n    className: className,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 35,\n    columnNumber: 9\n  }, this);\n};\n_c2 = CardContent;\nexport const CardHeader = ({\n  children,\n  className\n}) => {\n  return /*#__PURE__*/_jsxDEV(MuiCardHeader, {\n    className: className,\n    title: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 43,\n    columnNumber: 9\n  }, this);\n};\n_c3 = CardHeader;\nexport const CardTitle = ({\n  children,\n  className\n}) => {\n  return /*#__PURE__*/_jsxDEV(Typography, {\n    variant: \"h6\",\n    component: \"h3\",\n    className: className,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 49,\n    columnNumber: 9\n  }, this);\n};\n_c4 = CardTitle;\nvar _c, _c2, _c3, _c4;\n$RefreshReg$(_c, \"Card\");\n$RefreshReg$(_c2, \"CardContent\");\n$RefreshReg$(_c3, \"CardHeader\");\n$RefreshReg$(_c4, \"CardTitle\");", "map": {"version": 3, "names": ["React", "Card", "MuiCard", "<PERSON><PERSON><PERSON><PERSON>", "MuiCardContent", "<PERSON><PERSON><PERSON><PERSON>", "MuiCardHeader", "Typography", "jsxDEV", "_jsxDEV", "children", "className", "sx", "mb", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "_c2", "title", "_c3", "CardTitle", "variant", "component", "_c4", "$RefreshReg$"], "sources": ["I:/برمجة/تعليمي ali2 2/تعليمي 2/ادارة مراكز تعليمية/edu 2/client/src/components/ui/card.tsx"], "sourcesContent": ["import React from 'react';\nimport { <PERSON> as <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>eader } from '@mui/material';\nimport { Typography } from '@mui/material';\n\ninterface CardProps {\n    children: React.ReactNode;\n    className?: string;\n}\n\ninterface CardContentProps {\n    children: React.ReactNode;\n    className?: string;\n}\n\ninterface CardHeaderProps {\n    children: React.ReactNode;\n    className?: string;\n}\n\ninterface CardTitleProps {\n    children: React.ReactNode;\n    className?: string;\n}\n\nexport const Card: React.FC<CardProps> = ({ children, className }) => {\n    return (\n        <MuiCard className={className} sx={{ mb: 2 }}>\n            {children}\n        </MuiCard>\n    );\n};\n\nexport const CardContent: React.FC<CardContentProps> = ({ children, className }) => {\n    return (\n        <MuiCardContent className={className}>\n            {children}\n        </MuiCardContent>\n    );\n};\n\nexport const CardHeader: React.FC<CardHeaderProps> = ({ children, className }) => {\n    return (\n        <MuiCardHeader className={className} title={children} />\n    );\n};\n\nexport const CardTitle: React.FC<CardTitleProps> = ({ children, className }) => {\n    return (\n        <Typography variant=\"h6\" component=\"h3\" className={className}>\n            {children}\n        </Typography>\n    );\n};\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,IAAIC,OAAO,EAAEC,WAAW,IAAIC,cAAc,EAAEC,UAAU,IAAIC,aAAa,QAAQ,eAAe;AAC3G,SAASC,UAAU,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAsB3C,OAAO,MAAMR,IAAyB,GAAGA,CAAC;EAAES,QAAQ;EAAEC;AAAU,CAAC,KAAK;EAClE,oBACIF,OAAA,CAACP,OAAO;IAACS,SAAS,EAAEA,SAAU;IAACC,EAAE,EAAE;MAAEC,EAAE,EAAE;IAAE,CAAE;IAAAH,QAAA,EACxCA;EAAQ;IAAAI,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAElB,CAAC;AAACC,EAAA,GANWjB,IAAyB;AAQtC,OAAO,MAAME,WAAuC,GAAGA,CAAC;EAAEO,QAAQ;EAAEC;AAAU,CAAC,KAAK;EAChF,oBACIF,OAAA,CAACL,cAAc;IAACO,SAAS,EAAEA,SAAU;IAAAD,QAAA,EAChCA;EAAQ;IAAAI,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEzB,CAAC;AAACE,GAAA,GANWhB,WAAuC;AAQpD,OAAO,MAAME,UAAqC,GAAGA,CAAC;EAAEK,QAAQ;EAAEC;AAAU,CAAC,KAAK;EAC9E,oBACIF,OAAA,CAACH,aAAa;IAACK,SAAS,EAAEA,SAAU;IAACS,KAAK,EAAEV;EAAS;IAAAI,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AAEhE,CAAC;AAACI,GAAA,GAJWhB,UAAqC;AAMlD,OAAO,MAAMiB,SAAmC,GAAGA,CAAC;EAAEZ,QAAQ;EAAEC;AAAU,CAAC,KAAK;EAC5E,oBACIF,OAAA,CAACF,UAAU;IAACgB,OAAO,EAAC,IAAI;IAACC,SAAS,EAAC,IAAI;IAACb,SAAS,EAAEA,SAAU;IAAAD,QAAA,EACxDA;EAAQ;IAAAI,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAErB,CAAC;AAACQ,GAAA,GANWH,SAAmC;AAAA,IAAAJ,EAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAI,GAAA;AAAAC,YAAA,CAAAR,EAAA;AAAAQ,YAAA,CAAAP,GAAA;AAAAO,YAAA,CAAAL,GAAA;AAAAK,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}