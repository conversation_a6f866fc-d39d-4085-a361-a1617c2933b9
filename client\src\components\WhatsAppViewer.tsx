import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { Separator } from './ui/separator';
import {
    WhatsApp as MessageCircle,
    Language as Globe,
    Refresh as RefreshCw,
    OpenInNew as ExternalLink,
    CheckCircle,
    Cancel as XCircle,
    Schedule as Clock,
    Warning as AlertCircle
} from '@mui/icons-material';
import axios from 'axios';

interface WhatsAppViewerProps {
    className?: string;
}

interface WhatsAppStatus {
    status: 'disconnected' | 'qr_received' | 'authenticated' | 'ready' | 'error';
    isReady: boolean;
    qrCode?: string;
    qrCodeDataURL?: string;
    sessionInfo?: {
        hasSession: boolean;
        clientId: string;
    };
    lastActivity?: string;
    messageQueueLength: number;
    isProcessingQueue: boolean;
    reconnectAttempts: number;
    error?: string;
}

const WhatsAppViewer: React.FC<WhatsAppViewerProps> = ({ className }) => {
    const [status, setStatus] = useState<WhatsAppStatus | null>(null);
    const [loading, setLoading] = useState(false);
    const [showWebView, setShowWebView] = useState(false);

    // Check WhatsApp status
    const checkStatus = async () => {
        try {
            setLoading(true);
            const response = await axios.get('/api/whatsapp/status');
            if (response.data.success) {
                setStatus(response.data.data);
            }
        } catch (error) {
            console.error('Error checking WhatsApp status:', error);
        } finally {
            setLoading(false);
        }
    };

    // Initialize WhatsApp
    const initializeWhatsApp = async () => {
        try {
            setLoading(true);
            const response = await axios.post('/api/whatsapp/initialize');
            if (response.data.success) {
                await checkStatus();
            }
        } catch (error) {
            console.error('Error initializing WhatsApp:', error);
        } finally {
            setLoading(false);
        }
    };

    // Disconnect WhatsApp
    const disconnectWhatsApp = async () => {
        try {
            setLoading(true);
            const response = await axios.post('/api/whatsapp/disconnect');
            if (response.data.success) {
                await checkStatus();
            }
        } catch (error) {
            console.error('Error disconnecting WhatsApp:', error);
        } finally {
            setLoading(false);
        }
    };

    // Get status badge
    const getStatusBadge = () => {
        if (!status) return <Badge variant="secondary">غير معروف</Badge>;

        switch (status.status) {
            case 'ready':
                return <Badge variant="default">متصل</Badge>;
            case 'authenticated':
                return <Badge variant="default">مصادق عليه</Badge>;
            case 'qr_received':
                return <Badge variant="default">انتظار المسح</Badge>;
            case 'disconnected':
                return <Badge variant="secondary">غير متصل</Badge>;
            case 'error':
                return <Badge variant="destructive">خطأ</Badge>;
            default:
                return <Badge variant="secondary">غير معروف</Badge>;
        }
    };

    // Auto-refresh status
    useEffect(() => {
        checkStatus();
        const interval = setInterval(checkStatus, 5000); // Check every 5 seconds
        return () => clearInterval(interval);
    }, []);

    return (
        <div className={className}>
            <Card>
                <CardHeader>
                    <CardTitle>
                        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                            <MessageCircle />
                            حالة الواتساب
                            {getStatusBadge()}
                        </div>
                    </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                    {/* Status Information */}
                    <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                            <span className="font-medium">الحالة:</span>
                            <span className="mr-2">{status?.status || 'غير معروف'}</span>
                        </div>
                        <div>
                            <span className="font-medium">جاهز:</span>
                            <span className="mr-2">{status?.isReady ? 'نعم' : 'لا'}</span>
                        </div>
                        <div>
                            <span className="font-medium">طوابير الرسائل:</span>
                            <span className="mr-2">{status?.messageQueueLength || 0}</span>
                        </div>
                        <div>
                            <span className="font-medium">محاولات الاتصال:</span>
                            <span className="mr-2">{status?.reconnectAttempts || 0}</span>
                        </div>
                    </div>

                    <Separator />

                    {/* Action Buttons */}
                    <div className="flex gap-2 flex-wrap">
                        <Button
                            onClick={initializeWhatsApp}
                            disabled={loading || status?.status === 'ready'}
                            size="sm"
                        >
                            تشغيل الواتساب
                        </Button>

                        <Button
                            onClick={disconnectWhatsApp}
                            disabled={loading || status?.status === 'disconnected'}
                            variant="outline"
                            size="sm"
                        >
                            قطع الاتصال
                        </Button>

                        <Button
                            onClick={checkStatus}
                            disabled={loading}
                            variant="outline"
                            size="sm"
                        >
                            تحديث
                        </Button>

                        <Button
                            onClick={() => setShowWebView(!showWebView)}
                            variant="outline"
                            size="sm"
                        >
                            {showWebView ? 'إخفاء' : 'عرض'} الواتساب
                        </Button>

                        <Button
                            onClick={() => window.open('https://web.whatsapp.com', '_blank')}
                            variant="outline"
                            size="sm"
                        >
                            فتح في نافذة جديدة
                        </Button>
                    </div>

                    {/* QR Code Display */}
                    {status?.qrCodeDataURL && (
                        <div className="text-center">
                            <p className="text-sm text-muted-foreground mb-2">امسح الكود بهاتفك:</p>
                            <img 
                                src={status.qrCodeDataURL} 
                                alt="WhatsApp QR Code" 
                                className="mx-auto border rounded"
                                style={{ maxWidth: '200px' }}
                            />
                        </div>
                    )}

                    {/* Error Display */}
                    {status?.error && (
                        <div className="p-3 bg-red-50 border border-red-200 rounded text-red-700 text-sm">
                            <strong>خطأ:</strong> {status.error}
                        </div>
                    )}

                    {/* WhatsApp Web Viewer */}
                    {showWebView && (
                        <div className="border rounded-lg overflow-hidden">
                            <div className="bg-gray-100 p-2 text-sm font-medium flex items-center justify-between">
                                <span>واتساب ويب</span>
                                <Button 
                                    onClick={() => setShowWebView(false)}
                                    variant="ghost"
                                    size="sm"
                                >
                                    ✕
                                </Button>
                            </div>
                            <iframe
                                src="https://web.whatsapp.com"
                                className="w-full"
                                style={{ height: '600px' }}
                                title="WhatsApp Web"
                            />
                        </div>
                    )}

                    {/* Last Activity */}
                    {status?.lastActivity && (
                        <div className="text-xs text-muted-foreground">
                            آخر نشاط: {new Date(status.lastActivity).toLocaleString('ar-EG')}
                        </div>
                    )}
                </CardContent>
            </Card>
        </div>
    );
};

export default WhatsAppViewer;
