{"ast": null, "code": "var _jsxFileName = \"I:\\\\\\u0628\\u0631\\u0645\\u062C\\u0629\\\\\\u062A\\u0639\\u0644\\u064A\\u0645\\u064A ali2 2\\\\\\u062A\\u0639\\u0644\\u064A\\u0645\\u064A 2\\\\\\u0627\\u062F\\u0627\\u0631\\u0629 \\u0645\\u0631\\u0627\\u0643\\u0632 \\u062A\\u0639\\u0644\\u064A\\u0645\\u064A\\u0629\\\\edu 2\\\\client\\\\src\\\\components\\\\ui\\\\button.tsx\";\nimport React from 'react';\nimport { Button as MuiButton } from '@mui/material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const Button = ({\n  children,\n  onClick,\n  disabled,\n  variant = 'default',\n  size = 'md',\n  className\n}) => {\n  const getVariant = () => {\n    switch (variant) {\n      case 'outline':\n        return 'outlined';\n      case 'ghost':\n        return 'text';\n      default:\n        return 'contained';\n    }\n  };\n  const getSize = () => {\n    switch (size) {\n      case 'sm':\n        return 'small';\n      case 'lg':\n        return 'large';\n      default:\n        return 'medium';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(MuiButton, {\n    variant: getVariant(),\n    size: getSize(),\n    onClick: onClick,\n    disabled: disabled,\n    className: className,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 44,\n    columnNumber: 9\n  }, this);\n};\n_c = Button;\nvar _c;\n$RefreshReg$(_c, \"Button\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON>", "MuiB<PERSON>on", "jsxDEV", "_jsxDEV", "children", "onClick", "disabled", "variant", "size", "className", "getVariant", "getSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["I:/برمجة/تعليمي ali2 2/تعليمي 2/ادارة مراكز تعليمية/edu 2/client/src/components/ui/button.tsx"], "sourcesContent": ["import React from 'react';\nimport { Button as Mu<PERSON>Button } from '@mui/material';\n\ninterface ButtonProps {\n    children: React.ReactNode;\n    onClick?: () => void;\n    disabled?: boolean;\n    variant?: 'default' | 'outline' | 'ghost';\n    size?: 'sm' | 'md' | 'lg';\n    className?: string;\n}\n\nexport const Button: React.FC<ButtonProps> = ({ \n    children, \n    onClick, \n    disabled, \n    variant = 'default', \n    size = 'md',\n    className \n}) => {\n    const getVariant = () => {\n        switch (variant) {\n            case 'outline':\n                return 'outlined';\n            case 'ghost':\n                return 'text';\n            default:\n                return 'contained';\n        }\n    };\n\n    const getSize = () => {\n        switch (size) {\n            case 'sm':\n                return 'small';\n            case 'lg':\n                return 'large';\n            default:\n                return 'medium';\n        }\n    };\n\n    return (\n        <MuiButton\n            variant={getVariant()}\n            size={getSize()}\n            onClick={onClick}\n            disabled={disabled}\n            className={className}\n        >\n            {children}\n        </MuiButton>\n    );\n};\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,IAAIC,SAAS,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAWpD,OAAO,MAAMH,MAA6B,GAAGA,CAAC;EAC1CI,QAAQ;EACRC,OAAO;EACPC,QAAQ;EACRC,OAAO,GAAG,SAAS;EACnBC,IAAI,GAAG,IAAI;EACXC;AACJ,CAAC,KAAK;EACF,MAAMC,UAAU,GAAGA,CAAA,KAAM;IACrB,QAAQH,OAAO;MACX,KAAK,SAAS;QACV,OAAO,UAAU;MACrB,KAAK,OAAO;QACR,OAAO,MAAM;MACjB;QACI,OAAO,WAAW;IAC1B;EACJ,CAAC;EAED,MAAMI,OAAO,GAAGA,CAAA,KAAM;IAClB,QAAQH,IAAI;MACR,KAAK,IAAI;QACL,OAAO,OAAO;MAClB,KAAK,IAAI;QACL,OAAO,OAAO;MAClB;QACI,OAAO,QAAQ;IACvB;EACJ,CAAC;EAED,oBACIL,OAAA,CAACF,SAAS;IACNM,OAAO,EAAEG,UAAU,CAAC,CAAE;IACtBF,IAAI,EAAEG,OAAO,CAAC,CAAE;IAChBN,OAAO,EAAEA,OAAQ;IACjBC,QAAQ,EAAEA,QAAS;IACnBG,SAAS,EAAEA,SAAU;IAAAL,QAAA,EAEpBA;EAAQ;IAAAQ,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEpB,CAAC;AAACC,EAAA,GAzCWhB,MAA6B;AAAA,IAAAgB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}