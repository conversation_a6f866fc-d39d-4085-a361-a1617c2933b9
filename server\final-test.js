// اختبار نهائي للإرسال المجمع
const axios = require('axios');

const API_BASE = 'http://localhost:7080/api';
const TEST_PHONE = '201121381487';

async function testBulkSend() {
    try {
        console.log('🧪 اختبار الإرسال المجمع النهائي...\n');
        
        const response = await axios.post(`${API_BASE}/whatsapp/send-bulk`, {
            recipients: [
                { 
                    phone: TEST_PHONE, 
                    name: 'اختبار مجمع 1' 
                },
                { 
                    student_phone: TEST_PHONE, 
                    name: 'اختبار مجمع 2' 
                }
            ],
            message: `🎯 اختبار الإرسال المجمع النهائي
الوقت: ${new Date().toLocaleString('ar-EG')}
✅ إذا وصلتك هذه الرسالة، فالنظام يعمل بشكل مثالي!`
        });
        
        console.log('✅ نتيجة الإرسال المجمع:');
        console.log(JSON.stringify(response.data, null, 2));
        
    } catch (error) {
        console.log('❌ خطأ في الإرسال المجمع:');
        console.log(error.response?.data || error.message);
    }
}

testBulkSend();
