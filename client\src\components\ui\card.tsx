import React from 'react';
import { <PERSON> as <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>eader } from '@mui/material';
import { Typography } from '@mui/material';

interface CardProps {
    children: React.ReactNode;
    className?: string;
}

interface CardContentProps {
    children: React.ReactNode;
    className?: string;
}

interface CardHeaderProps {
    children: React.ReactNode;
    className?: string;
}

interface CardTitleProps {
    children: React.ReactNode;
    className?: string;
}

export const Card: React.FC<CardProps> = ({ children, className }) => {
    return (
        <MuiCard className={className} sx={{ mb: 2 }}>
            {children}
        </MuiCard>
    );
};

export const CardContent: React.FC<CardContentProps> = ({ children, className }) => {
    return (
        <MuiCardContent className={className}>
            {children}
        </MuiCardContent>
    );
};

export const CardHeader: React.FC<CardHeaderProps> = ({ children, className }) => {
    return (
        <MuiCardHeader className={className} title={children} />
    );
};

export const CardTitle: React.FC<CardTitleProps> = ({ children, className }) => {
    return (
        <Typography variant="h6" component="h3" className={className}>
            {children}
        </Typography>
    );
};
