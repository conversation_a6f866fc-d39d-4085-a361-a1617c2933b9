import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  Chip,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Paper
} from '@mui/material';
import {
  WhatsApp as WhatsAppIcon,
  Send as SendIcon,
  Refresh as RefreshIcon,
  Phone as PhoneIcon,
  Group as GroupIcon,
  CheckCircle as CheckIcon,
  Error as ErrorIcon,
  Warning as WarningIcon
} from '@mui/icons-material';
import axios from 'axios';
import { useSnackbar } from 'notistack';

interface Group {
  id: number;
  group_name: string;
  student_count?: number;
}

interface Student {
  id: number;
  student_name: string;
  student_phone?: string;
  father_phone?: string;
  mother_phone?: string;
}

interface WhatsAppStatus {
  status: string;
  isReady: boolean;
  qrCode?: string;
  error?: string;
}

const SimpleWhatsApp: React.FC = () => {
  const { enqueueSnackbar } = useSnackbar();
  
  // States
  const [whatsappStatus, setWhatsappStatus] = useState<WhatsAppStatus>({
    status: 'disconnected',
    isReady: false
  });
  const [loading, setLoading] = useState(false);
  const [groups, setGroups] = useState<Group[]>([]);
  const [selectedGroup, setSelectedGroup] = useState<number>(0);
  const [students, setStudents] = useState<Student[]>([]);
  const [message, setMessage] = useState('');
  const [testNumbers, setTestNumbers] = useState<string[]>([]);
  const [customNumber, setCustomNumber] = useState('');
  const [sending, setSending] = useState(false);
  const [sendResults, setSendResults] = useState<any[]>([]);

  // Load initial data
  useEffect(() => {
    checkWhatsAppStatus();
    loadGroups();
  }, []);

  // Check WhatsApp status
  const checkWhatsAppStatus = async () => {
    try {
      const response = await axios.get('/api/whatsapp/status');
      setWhatsappStatus(response.data.data);
    } catch (error) {
      console.error('Error checking WhatsApp status:', error);
      setWhatsappStatus({
        status: 'error',
        isReady: false,
        error: 'فشل في الاتصال بالسيرفر'
      });
    }
  };

  // Initialize WhatsApp
  const initializeWhatsApp = async () => {
    setLoading(true);
    try {
      await axios.post('/api/whatsapp/initialize');
      enqueueSnackbar('تم بدء تشغيل الواتساب', { variant: 'success' });
      
      // Check status after 3 seconds
      setTimeout(() => {
        checkWhatsAppStatus();
      }, 3000);
      
    } catch (error) {
      console.error('Error initializing WhatsApp:', error);
      enqueueSnackbar('خطأ في تشغيل الواتساب', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  // Load groups
  const loadGroups = async () => {
    try {
      const response = await axios.get('/api/groups');
      setGroups(response.data.data || []);
    } catch (error) {
      console.error('Error loading groups:', error);
      enqueueSnackbar('خطأ في تحميل المجموعات', { variant: 'error' });
    }
  };

  // Load students when group changes
  useEffect(() => {
    if (selectedGroup) {
      loadStudents(selectedGroup);
    } else {
      setStudents([]);
      setTestNumbers([]);
    }
  }, [selectedGroup]);

  // Load students
  const loadStudents = async (groupId: number) => {
    try {
      const response = await axios.get(`/api/groups/${groupId}/students`);
      const studentsData = response.data.data || [];
      setStudents(studentsData);
      
      // Extract phone numbers
      const numbers: string[] = [];
      studentsData.forEach((student: Student) => {
        if (student.student_phone) numbers.push(student.student_phone);
        if (student.father_phone) numbers.push(student.father_phone);
        if (student.mother_phone) numbers.push(student.mother_phone);
      });
      
      setTestNumbers([...new Set(numbers)]); // Remove duplicates
      
    } catch (error) {
      console.error('Error loading students:', error);
      enqueueSnackbar('خطأ في تحميل الطلاب', { variant: 'error' });
    }
  };

  // Add custom number
  const addCustomNumber = () => {
    if (customNumber.trim() && !testNumbers.includes(customNumber.trim())) {
      setTestNumbers([...testNumbers, customNumber.trim()]);
      setCustomNumber('');
    }
  };

  // Remove number
  const removeNumber = (numberToRemove: string) => {
    setTestNumbers(testNumbers.filter(num => num !== numberToRemove));
  };

  // Test numbers (send to all)
  const testNumbers_send = async () => {
    if (!whatsappStatus.isReady) {
      enqueueSnackbar('الواتساب غير متصل', { variant: 'error' });
      return;
    }

    if (testNumbers.length === 0) {
      enqueueSnackbar('لا توجد أرقام للاختبار', { variant: 'warning' });
      return;
    }

    if (!message.trim()) {
      enqueueSnackbar('يرجى كتابة رسالة', { variant: 'warning' });
      return;
    }

    setSending(true);
    setSendResults([]);

    try {
      const results = [];
      
      for (let i = 0; i < testNumbers.length; i++) {
        const number = testNumbers[i];
        
        try {
          const response = await axios.post('/api/whatsapp/send-real', {
            phone_number: number,
            message: `${message}\n\n📱 رقم الاختبار: ${i + 1}/${testNumbers.length}\n⏰ ${new Date().toLocaleString('ar-EG')}`
          });
          
          results.push({
            number,
            success: response.data.success,
            message: response.data.message,
            chatId: response.data.data?.chatId
          });
          
          enqueueSnackbar(`تم إرسال الرسالة ${i + 1}/${testNumbers.length}`, { variant: 'success' });
          
        } catch (error) {
          results.push({
            number,
            success: false,
            message: 'فشل في الإرسال',
            error: error.response?.data?.error || error.message
          });
        }
        
        // Wait 2 seconds between messages
        if (i < testNumbers.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 2000));
        }
      }
      
      setSendResults(results);
      
      const successCount = results.filter(r => r.success).length;
      enqueueSnackbar(`تم إرسال ${successCount}/${testNumbers.length} رسالة بنجاح`, { 
        variant: successCount === testNumbers.length ? 'success' : 'warning' 
      });
      
    } catch (error) {
      console.error('Error sending messages:', error);
      enqueueSnackbar('خطأ في إرسال الرسائل', { variant: 'error' });
    } finally {
      setSending(false);
    }
  };

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ready': return 'success';
      case 'qr_received': return 'warning';
      case 'authenticated': return 'info';
      case 'disconnected': return 'default';
      case 'error': return 'error';
      default: return 'default';
    }
  };

  // Get status text
  const getStatusText = (status: string) => {
    switch (status) {
      case 'ready': return 'متصل ومستعد';
      case 'qr_received': return 'في انتظار مسح QR Code';
      case 'authenticated': return 'تم التوثيق';
      case 'disconnected': return 'غير متصل';
      case 'error': return 'خطأ في الاتصال';
      default: return status;
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
        <WhatsAppIcon color="success" />
        نظام الواتساب المبسط
      </Typography>

      <Grid container spacing={3}>
        {/* WhatsApp Status */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                حالة الواتساب
              </Typography>
              
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                <Chip 
                  label={getStatusText(whatsappStatus.status)}
                  color={getStatusColor(whatsappStatus.status)}
                  icon={whatsappStatus.isReady ? <CheckIcon /> : <ErrorIcon />}
                />
                
                <Button
                  variant="outlined"
                  onClick={checkWhatsAppStatus}
                  startIcon={<RefreshIcon />}
                  size="small"
                >
                  تحديث الحالة
                </Button>
                
                {!whatsappStatus.isReady && (
                  <Button
                    variant="contained"
                    onClick={initializeWhatsApp}
                    disabled={loading}
                    startIcon={loading ? <CircularProgress size={20} /> : <WhatsAppIcon />}
                  >
                    {loading ? 'جاري التشغيل...' : 'تشغيل الواتساب'}
                  </Button>
                )}
              </Box>
              
              {whatsappStatus.error && (
                <Alert severity="error" sx={{ mt: 2 }}>
                  {whatsappStatus.error}
                </Alert>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Group Selection */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                اختيار المجموعة
              </Typography>
              
              <FormControl fullWidth sx={{ mb: 2 }}>
                <InputLabel>المجموعة</InputLabel>
                <Select
                  value={selectedGroup}
                  label="المجموعة"
                  onChange={(e) => setSelectedGroup(Number(e.target.value))}
                >
                  <MenuItem value={0}>اختر المجموعة</MenuItem>
                  {groups.map((group) => (
                    <MenuItem key={group.id} value={group.id}>
                      {group.group_name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
              
              {students.length > 0 && (
                <Alert severity="info">
                  تم تحميل {students.length} طالب و {testNumbers.length} رقم هاتف
                </Alert>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Custom Numbers */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                إضافة أرقام مخصصة
              </Typography>
              
              <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
                <TextField
                  label="رقم الهاتف"
                  value={customNumber}
                  onChange={(e) => setCustomNumber(e.target.value)}
                  placeholder="01121381487"
                  size="small"
                  sx={{ flex: 1 }}
                />
                <Button
                  variant="outlined"
                  onClick={addCustomNumber}
                  disabled={!customNumber.trim()}
                >
                  إضافة
                </Button>
              </Box>
              
              <Button
                variant="outlined"
                onClick={() => {
                  setTestNumbers(['01121381487', '01008198992', '01017839220']);
                }}
                size="small"
              >
                إضافة أرقام الاختبار
              </Button>
            </CardContent>
          </Card>
        </Grid>

        {/* Test Numbers */}
        {testNumbers.length > 0 && (
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  أرقام الاختبار ({testNumbers.length})
                </Typography>
                
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                  {testNumbers.map((number, index) => (
                    <Chip
                      key={index}
                      label={number}
                      onDelete={() => removeNumber(number)}
                      icon={<PhoneIcon />}
                      variant="outlined"
                    />
                  ))}
                </Box>
              </CardContent>
            </Card>
          </Grid>
        )}

        {/* Message */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                الرسالة
              </Typography>
              
              <TextField
                multiline
                rows={4}
                fullWidth
                label="اكتب رسالتك هنا"
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                placeholder="🧪 رسالة اختبار من نظام إدارة المراكز التعليمية..."
              />
              
              <Box sx={{ mt: 2, display: 'flex', gap: 2 }}>
                <Button
                  variant="contained"
                  onClick={testNumbers_send}
                  disabled={!whatsappStatus.isReady || sending || testNumbers.length === 0 || !message.trim()}
                  startIcon={sending ? <CircularProgress size={20} /> : <SendIcon />}
                  size="large"
                >
                  {sending ? `جاري الإرسال...` : `إرسال لـ ${testNumbers.length} رقم`}
                </Button>
                
                <Button
                  variant="outlined"
                  onClick={() => {
                    setMessage(`🧪 رسالة اختبار من نظام إدارة المراكز التعليمية
📱 اختبار الأرقام والإرسال
⏰ الوقت: ${new Date().toLocaleString('ar-EG')}
✅ إذا وصلتك هذه الرسالة، فالنظام يعمل بنجاح!`);
                  }}
                >
                  رسالة اختبار جاهزة
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Send Results */}
        {sendResults.length > 0 && (
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  نتائج الإرسال
                </Typography>
                
                <List>
                  {sendResults.map((result, index) => (
                    <ListItem key={index}>
                      <ListItemIcon>
                        {result.success ? 
                          <CheckIcon color="success" /> : 
                          <ErrorIcon color="error" />
                        }
                      </ListItemIcon>
                      <ListItemText
                        primary={result.number}
                        secondary={
                          result.success ? 
                            `✅ ${result.message} - ${result.chatId}` :
                            `❌ ${result.message} - ${result.error}`
                        }
                      />
                    </ListItem>
                  ))}
                </List>
              </CardContent>
            </Card>
          </Grid>
        )}
      </Grid>
    </Box>
  );
};

export default SimpleWhatsApp;
