// اختبار مباشر للواتساب
const axios = require('axios');

const API_BASE = 'http://localhost:7080/api';
const TEST_PHONE = '201121381487'; // الرقم الذي أعطيته

async function testAPI(endpoint, method = 'GET', data = null) {
    try {
        console.log(`🔄 Testing ${method} ${endpoint}...`);
        
        const config = {
            method,
            url: `${API_BASE}${endpoint}`,
            timeout: 10000,
            headers: {
                'Content-Type': 'application/json'
            }
        };
        
        if (data) {
            config.data = data;
        }
        
        const response = await axios(config);
        console.log(`✅ Success:`, response.data);
        return { success: true, data: response.data };
        
    } catch (error) {
        console.log(`❌ Error:`, error.response?.data || error.message);
        return { success: false, error: error.response?.data || error.message };
    }
}

async function runTests() {
    console.log('🚀 بدء اختبار نظام الواتساب...\n');
    
    // 1. اختبار صحة السيرفر
    console.log('1️⃣ اختبار صحة السيرفر:');
    await testAPI('/health');
    console.log('\n');
    
    // 2. اختبار حالة الواتساب
    console.log('2️⃣ اختبار حالة الواتساب:');
    const statusResult = await testAPI('/whatsapp/status');
    console.log('\n');
    
    // 3. إذا لم يكن الواتساب جاهز، تهيئته
    if (statusResult.success && statusResult.data?.data?.status !== 'ready') {
        console.log('3️⃣ تهيئة الواتساب:');
        await testAPI('/whatsapp/initialize', 'POST');
        console.log('⏳ انتظار 10 ثوانٍ للتهيئة...');
        await new Promise(resolve => setTimeout(resolve, 10000));
        
        // تحقق من الحالة مرة أخرى
        console.log('🔄 تحقق من الحالة بعد التهيئة:');
        await testAPI('/whatsapp/status');
        console.log('\n');
    }
    
    // 4. اختبار الإرسال السريع
    console.log('4️⃣ اختبار الإرسال السريع:');
    await testAPI('/whatsapp/quick-test', 'POST');
    console.log('\n');
    
    // 5. اختبار الإرسال المخصص
    console.log('5️⃣ اختبار الإرسال المخصص:');
    await testAPI('/whatsapp/send-real', 'POST', {
        phone_number: TEST_PHONE,
        message: `🧪 رسالة اختبار مباشرة من النظام\nالوقت: ${new Date().toLocaleString('ar-EG')}\n✅ إذا وصلتك هذه الرسالة، فالنظام يعمل بنجاح!`
    });
    console.log('\n');
    
    // 6. اختبار الإرسال المجمع
    console.log('6️⃣ اختبار الإرسال المجمع:');
    await testAPI('/whatsapp/send-bulk', 'POST', {
        recipients: [
            { phone: TEST_PHONE, name: 'اختبار 1' },
            { student_phone: TEST_PHONE, name: 'اختبار 2' }
        ],
        message: `📱 رسالة اختبار مجمعة\nالوقت: ${new Date().toLocaleString('ar-EG')}`
    });
    console.log('\n');
    
    console.log('🎉 انتهى الاختبار!');
    console.log('📱 تحقق من هاتفك للتأكد من وصول الرسائل');
}

// تشغيل الاختبارات
runTests().catch(console.error);
