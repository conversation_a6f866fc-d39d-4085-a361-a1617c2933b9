// فحص هيكل قاعدة البيانات
const { getConnection } = require('./config/database');

async function checkDatabaseStructure() {
    let connection;
    
    try {
        connection = await getConnection();
        console.log('🔗 متصل بقاعدة البيانات...\n');

        // فحص جدول المجموعات
        console.log('📚 فحص جدول المجموعات (groups):');
        const groupsColumns = await connection.request().query(`
            SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = 'groups'
            ORDER BY ORDINAL_POSITION
        `);
        
        if (groupsColumns.recordset.length > 0) {
            groupsColumns.recordset.forEach(col => {
                console.log(`  - ${col.COLUMN_NAME} (${col.DATA_TYPE}) ${col.IS_NULLABLE === 'YES' ? 'NULL' : 'NOT NULL'}`);
            });
        } else {
            console.log('  ❌ جدول groups غير موجود');
        }

        // فحص جدول الطلاب
        console.log('\n👨‍🎓 فحص جدول الطلاب (students):');
        const studentsColumns = await connection.request().query(`
            SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = 'students'
            ORDER BY ORDINAL_POSITION
        `);
        
        if (studentsColumns.recordset.length > 0) {
            studentsColumns.recordset.forEach(col => {
                console.log(`  - ${col.COLUMN_NAME} (${col.DATA_TYPE}) ${col.IS_NULLABLE === 'YES' ? 'NULL' : 'NOT NULL'}`);
            });
        } else {
            console.log('  ❌ جدول students غير موجود');
        }

        // فحص جدول ربط الطلاب بالمجموعات
        console.log('\n🔗 فحص جدول ربط الطلاب بالمجموعات (student_groups):');
        const studentGroupsColumns = await connection.request().query(`
            SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = 'student_groups'
            ORDER BY ORDINAL_POSITION
        `);
        
        if (studentGroupsColumns.recordset.length > 0) {
            studentGroupsColumns.recordset.forEach(col => {
                console.log(`  - ${col.COLUMN_NAME} (${col.DATA_TYPE}) ${col.IS_NULLABLE === 'YES' ? 'NULL' : 'NOT NULL'}`);
            });
        } else {
            console.log('  ❌ جدول student_groups غير موجود');
        }

        // فحص الجداول الموجودة
        console.log('\n📋 جميع الجداول الموجودة:');
        const allTables = await connection.request().query(`
            SELECT TABLE_NAME 
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_TYPE = 'BASE TABLE'
            ORDER BY TABLE_NAME
        `);
        
        allTables.recordset.forEach(table => {
            console.log(`  - ${table.TABLE_NAME}`);
        });

        // فحص البيانات الموجودة في المجموعات
        console.log('\n📊 المجموعات الموجودة:');
        try {
            const existingGroups = await connection.request().query(`
                SELECT TOP 5 * FROM groups
            `);
            
            if (existingGroups.recordset.length > 0) {
                console.log('المجموعات الموجودة:');
                existingGroups.recordset.forEach(group => {
                    console.log(`  - ID: ${group.id}, الاسم: ${group.group_name || group.name || 'غير محدد'}`);
                });
            } else {
                console.log('  لا توجد مجموعات');
            }
        } catch (error) {
            console.log(`  خطأ في قراءة المجموعات: ${error.message}`);
        }

        // فحص البيانات الموجودة في الطلاب
        console.log('\n👥 الطلاب الموجودون:');
        try {
            const existingStudents = await connection.request().query(`
                SELECT TOP 5 * FROM students
            `);
            
            if (existingStudents.recordset.length > 0) {
                console.log('الطلاب الموجودون:');
                existingStudents.recordset.forEach(student => {
                    console.log(`  - ID: ${student.id}, الاسم: ${student.student_name || student.name || 'غير محدد'}, الهاتف: ${student.student_phone || 'غير محدد'}`);
                });
            } else {
                console.log('  لا يوجد طلاب');
            }
        } catch (error) {
            console.log(`  خطأ في قراءة الطلاب: ${error.message}`);
        }

    } catch (error) {
        console.error('❌ خطأ في فحص قاعدة البيانات:', error);
    } finally {
        if (connection) {
            await connection.close();
            console.log('\n🔒 تم إغلاق الاتصال بقاعدة البيانات');
        }
    }
}

// تشغيل فحص قاعدة البيانات
checkDatabaseStructure();
