{"ast": null, "code": "var _jsxFileName = \"I:\\\\\\u0628\\u0631\\u0645\\u062C\\u0629\\\\\\u062A\\u0639\\u0644\\u064A\\u0645\\u064A ali2 2\\\\\\u062A\\u0639\\u0644\\u064A\\u0645\\u064A 2\\\\\\u0627\\u062F\\u0627\\u0631\\u0629 \\u0645\\u0631\\u0627\\u0643\\u0632 \\u062A\\u0639\\u0644\\u064A\\u0645\\u064A\\u0629\\\\edu 2\\\\client\\\\src\\\\components\\\\Layout\\\\Sidebar.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Box, List, ListItem, ListItemButton, ListItemIcon, ListItemText, Toolbar, Typography, Divider, Avatar, Button } from '@mui/material';\nimport { Dashboard as DashboardIcon, School as StudentsIcon, Business as BranchesIcon, Person as TeachersIcon, Group as GroupsIcon, MenuBook as SubjectsIcon, Class as GradesIcon, Assessment as ReportsIcon, Quiz as ExamsIcon, Settings as SettingsIcon, Add as AddIcon, EventAvailable as AttendanceIcon, QrCodeScanner as BarcodeIcon, WhatsApp as WhatsAppIcon, Receipt as BookletsIcon, Payment as FinanceIcon } from '@mui/icons-material';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { useTranslation } from 'react-i18next';\nimport { useUser } from '../../contexts/UserContext';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Sidebar = ({\n  onItemClick\n}) => {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const {\n    t\n  } = useTranslation();\n  const {\n    user\n  } = useUser();\n  const menuItems = [{\n    text: t('dashboard'),\n    icon: /*#__PURE__*/_jsxDEV(DashboardIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 13\n    }, this),\n    path: '/dashboard'\n  }, {\n    text: t('students'),\n    icon: /*#__PURE__*/_jsxDEV(StudentsIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 13\n    }, this),\n    path: '/students'\n  }, {\n    text: 'الفروع',\n    icon: /*#__PURE__*/_jsxDEV(BranchesIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 13\n    }, this),\n    path: '/branches'\n  }, {\n    text: 'الصفوف التعليمية',\n    icon: /*#__PURE__*/_jsxDEV(GradesIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 13\n    }, this),\n    path: '/grades'\n  }, {\n    text: t('teachers'),\n    icon: /*#__PURE__*/_jsxDEV(TeachersIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 70,\n      columnNumber: 13\n    }, this),\n    path: '/teachers'\n  }, {\n    text: t('groups'),\n    icon: /*#__PURE__*/_jsxDEV(GroupsIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 13\n    }, this),\n    path: '/groups'\n  }, {\n    text: 'المواد الدراسية',\n    icon: /*#__PURE__*/_jsxDEV(SubjectsIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 13\n    }, this),\n    path: '/subjects'\n  }, {\n    text: 'الحضور',\n    icon: /*#__PURE__*/_jsxDEV(AttendanceIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 85,\n      columnNumber: 13\n    }, this),\n    path: '/attendance',\n    children: [{\n      text: 'تسجيل حضور عادي',\n      icon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 90,\n        columnNumber: 17\n      }, this),\n      path: '/attendance/add'\n    }, {\n      text: 'تسجيل حضور بالباركود',\n      icon: /*#__PURE__*/_jsxDEV(BarcodeIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 17\n      }, this),\n      path: '/attendance/barcode'\n    }, {\n      text: 'تسجيل حضور بـ QR',\n      icon: /*#__PURE__*/_jsxDEV(BarcodeIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 100,\n        columnNumber: 17\n      }, this),\n      path: '/attendance/qr'\n    }]\n  }, {\n    text: 'رسائل واتساب',\n    icon: /*#__PURE__*/_jsxDEV(WhatsAppIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 107,\n      columnNumber: 13\n    }, this),\n    path: '/whatsapp',\n    children: [{\n      text: 'إرسال الرسائل',\n      icon: /*#__PURE__*/_jsxDEV(WhatsAppIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 17\n      }, this),\n      path: '/whatsapp'\n    }, {\n      text: 'لوحة التحكم المحسنة',\n      icon: /*#__PURE__*/_jsxDEV(WhatsAppIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 17\n      }, this),\n      path: '/whatsapp/enhanced'\n    }, {\n      text: 'واتساب مبسط',\n      icon: /*#__PURE__*/_jsxDEV(WhatsAppIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 17\n      }, this),\n      path: '/simple-whatsapp'\n    }]\n  }, {\n    text: 'الملازم',\n    icon: /*#__PURE__*/_jsxDEV(BookletsIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 13\n    }, this),\n    path: '/booklets'\n  }, {\n    text: 'إدارة الملازم',\n    icon: /*#__PURE__*/_jsxDEV(FinanceIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 134,\n      columnNumber: 13\n    }, this),\n    path: '/booklets/management'\n  }, {\n    text: 'استلام الملازم',\n    icon: /*#__PURE__*/_jsxDEV(FinanceIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 139,\n      columnNumber: 13\n    }, this),\n    path: '/booklet-deliveries'\n  }, {\n    text: 'النظام المالي',\n    icon: /*#__PURE__*/_jsxDEV(FinanceIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 13\n    }, this),\n    path: '/financial',\n    children: [{\n      text: 'الاستلام والصرف النقدي',\n      icon: /*#__PURE__*/_jsxDEV(FinanceIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 149,\n        columnNumber: 17\n      }, this),\n      path: '/financial/cash-transactions'\n    }, {\n      text: 'القيود اليومية',\n      icon: /*#__PURE__*/_jsxDEV(FinanceIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 154,\n        columnNumber: 17\n      }, this),\n      path: '/financial/journal-entries'\n    }, {\n      text: 'دليل الحسابات',\n      icon: /*#__PURE__*/_jsxDEV(FinanceIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 17\n      }, this),\n      path: '/financial/chart-of-accounts'\n    }, {\n      text: 'التقارير المالية',\n      icon: /*#__PURE__*/_jsxDEV(ReportsIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 17\n      }, this),\n      path: '/financial/reports'\n    }]\n  }, {\n    text: 'الامتحانات',\n    icon: /*#__PURE__*/_jsxDEV(ExamsIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 171,\n      columnNumber: 13\n    }, this),\n    path: '/exams',\n    subItems: [{\n      text: 'قائمة الامتحانات',\n      icon: /*#__PURE__*/_jsxDEV(ExamsIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 17\n      }, this),\n      path: '/exams'\n    }, {\n      text: 'إضافة امتحان',\n      icon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 17\n      }, this),\n      path: '/exams/add'\n    }]\n  }, {\n    text: t('reports'),\n    icon: /*#__PURE__*/_jsxDEV(ReportsIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 188,\n      columnNumber: 13\n    }, this),\n    path: '/reports'\n  }, {\n    text: t('settings'),\n    icon: /*#__PURE__*/_jsxDEV(SettingsIcon, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 193,\n      columnNumber: 13\n    }, this),\n    path: '/settings',\n    children: [{\n      text: 'إدارة المستخدمين',\n      icon: /*#__PURE__*/_jsxDEV(SettingsIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 17\n      }, this),\n      path: '/settings/users'\n    }, {\n      text: 'إعدادات عامة',\n      icon: /*#__PURE__*/_jsxDEV(SettingsIcon, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 17\n      }, this),\n      path: '/settings'\n    }]\n  }];\n  const handleItemClick = path => {\n    navigate(path);\n    if (onItemClick) {\n      onItemClick();\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      height: '100%',\n      display: 'flex',\n      flexDirection: 'column'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Toolbar, {\n      sx: {\n        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n        color: 'white',\n        minHeight: '64px !important'\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Avatar, {\n          sx: {\n            bgcolor: 'rgba(255,255,255,0.2)',\n            mr: 2,\n            width: 40,\n            height: 40\n          },\n          children: /*#__PURE__*/_jsxDEV(StudentsIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              fontWeight: 600,\n              fontSize: '1rem'\n            },\n            children: \"\\u0646\\u0638\\u0627\\u0645 \\u0625\\u062F\\u0627\\u0631\\u0629\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            sx: {\n              opacity: 0.8\n            },\n            children: \"\\u0627\\u0644\\u0645\\u0631\\u0627\\u0643\\u0632 \\u0627\\u0644\\u062A\\u0639\\u0644\\u064A\\u0645\\u064A\\u0629\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 227,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 220,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 249,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(List, {\n      sx: {\n        flexGrow: 1,\n        pt: 2\n      },\n      children: menuItems.map(item => {\n        const isActive = location.pathname === item.path || item.path !== '/dashboard' && location.pathname.startsWith(item.path);\n        return /*#__PURE__*/_jsxDEV(React.Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(ListItem, {\n            disablePadding: true,\n            sx: {\n              mb: 0.5,\n              px: 2\n            },\n            children: /*#__PURE__*/_jsxDEV(ListItemButton, {\n              onClick: () => handleItemClick(item.path),\n              sx: {\n                borderRadius: 2,\n                backgroundColor: isActive ? 'rgba(25, 118, 210, 0.1)' : 'transparent',\n                color: isActive ? 'primary.main' : 'text.primary',\n                '&:hover': {\n                  backgroundColor: isActive ? 'rgba(25, 118, 210, 0.15)' : 'rgba(0, 0, 0, 0.04)'\n                },\n                transition: 'all 0.2s ease-in-out'\n              },\n              children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                sx: {\n                  color: isActive ? 'primary.main' : 'text.secondary',\n                  minWidth: 40\n                },\n                children: item.icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                primary: item.text,\n                sx: {\n                  '& .MuiListItemText-primary': {\n                    fontWeight: isActive ? 600 : 400,\n                    fontSize: '0.95rem'\n                  }\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 282,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 15\n          }, this), item.children && item.children.map(child => {\n            const childIsActive = location.pathname === child.path;\n            return /*#__PURE__*/_jsxDEV(ListItem, {\n              disablePadding: true,\n              sx: {\n                mb: 0.5,\n                px: 2,\n                pl: 4\n              },\n              children: /*#__PURE__*/_jsxDEV(ListItemButton, {\n                onClick: () => handleItemClick(child.path),\n                sx: {\n                  borderRadius: 2,\n                  backgroundColor: childIsActive ? 'rgba(25, 118, 210, 0.1)' : 'transparent',\n                  color: childIsActive ? 'primary.main' : 'text.primary',\n                  '&:hover': {\n                    backgroundColor: childIsActive ? 'rgba(25, 118, 210, 0.15)' : 'rgba(0, 0, 0, 0.04)'\n                  },\n                  transition: 'all 0.2s ease-in-out',\n                  pl: 2\n                },\n                children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n                  sx: {\n                    color: childIsActive ? 'primary.main' : 'text.secondary',\n                    minWidth: 30\n                  },\n                  children: 'icon' in child ? child.icon : /*#__PURE__*/_jsxDEV(WhatsAppIcon, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 320,\n                    columnNumber: 57\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 314,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n                  primary: child.text,\n                  sx: {\n                    '& .MuiListItemText-primary': {\n                      fontWeight: childIsActive ? 600 : 400,\n                      fontSize: '0.85rem'\n                    }\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 322,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 299,\n                columnNumber: 21\n              }, this)\n            }, child.path, false, {\n              fileName: _jsxFileName,\n              lineNumber: 298,\n              columnNumber: 19\n            }, this);\n          })]\n        }, item.path, true, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 252,\n      columnNumber: 7\n    }, this), (user === null || user === void 0 ? void 0 : user.isGlobalAdmin) && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 343,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          p: 2\n        },\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          fullWidth: true,\n          variant: \"contained\",\n          startIcon: /*#__PURE__*/_jsxDEV(AddIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 348,\n            columnNumber: 26\n          }, this),\n          onClick: () => handleItemClick('/groups/add'),\n          sx: {\n            backgroundColor: 'primary.main',\n            '&:hover': {\n              backgroundColor: 'primary.dark'\n            }\n          },\n          children: \"\\u0625\\u0636\\u0627\\u0641\\u0629 \\u0645\\u062C\\u0645\\u0648\\u0639\\u0629 \\u062C\\u062F\\u064A\\u062F\\u0629\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 345,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 344,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 363,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        p: 2,\n        textAlign: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"caption\",\n        color: \"text.secondary\",\n        children: \"\\u0627\\u0644\\u0625\\u0635\\u062F\\u0627\\u0631 1.0.0\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 367,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 366,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 218,\n    columnNumber: 5\n  }, this);\n};\n_s(Sidebar, \"gOrUFOSTHHdh8siI1scd/qQ36oA=\", false, function () {\n  return [useNavigate, useLocation, useTranslation, useUser];\n});\n_c = Sidebar;\nexport default Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");", "map": {"version": 3, "names": ["React", "Box", "List", "ListItem", "ListItemButton", "ListItemIcon", "ListItemText", "<PERSON><PERSON><PERSON>", "Typography", "Divider", "Avatar", "<PERSON><PERSON>", "Dashboard", "DashboardIcon", "School", "StudentsIcon", "Business", "BranchesIcon", "Person", "TeachersIcon", "Group", "GroupsIcon", "MenuBook", "SubjectsIcon", "Class", "GradesIcon", "Assessment", "ReportsIcon", "Quiz", "ExamsIcon", "Settings", "SettingsIcon", "Add", "AddIcon", "EventAvailable", "AttendanceIcon", "QrCodeScanner", "BarcodeIcon", "WhatsApp", "WhatsAppIcon", "Receipt", "BookletsIcon", "Payment", "FinanceIcon", "useNavigate", "useLocation", "useTranslation", "useUser", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Sidebar", "onItemClick", "_s", "navigate", "location", "t", "user", "menuItems", "text", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "children", "subItems", "handleItemClick", "sx", "height", "display", "flexDirection", "background", "color", "minHeight", "alignItems", "width", "bgcolor", "mr", "variant", "fontWeight", "fontSize", "opacity", "flexGrow", "pt", "map", "item", "isActive", "pathname", "startsWith", "disablePadding", "mb", "px", "onClick", "borderRadius", "backgroundColor", "transition", "min<PERSON><PERSON><PERSON>", "primary", "child", "childIsActive", "pl", "isGlobalAdmin", "p", "fullWidth", "startIcon", "textAlign", "_c", "$RefreshReg$"], "sources": ["I:/برمجة/تعليمي ali2 2/تعليمي 2/ادارة مراكز تعليمية/edu 2/client/src/components/Layout/Sidebar.tsx"], "sourcesContent": ["import React from 'react';\nimport {\n  Box,\n  List,\n  ListItem,\n  ListItemButton,\n  ListItemIcon,\n  ListItemText,\n  Toolbar,\n  Typography,\n  Divider,\n  Avatar,\n  Button,\n} from '@mui/material';\nimport {\n  Dashboard as DashboardIcon,\n  School as StudentsIcon,\n  Business as BranchesIcon,\n  Person as TeachersIcon,\n  Group as GroupsIcon,\n  MenuBook as SubjectsIcon,\n  Class as GradesIcon,\n  Assessment as ReportsIcon,\n  Quiz as ExamsIcon,\n  Settings as SettingsIcon,\n  Add as AddIcon,\n  EventAvailable as AttendanceIcon,\n  QrCodeScanner as BarcodeIcon,\n  WhatsApp as WhatsAppIcon,\n  Receipt as BookletsIcon,\n  Payment as FinanceIcon,\n} from '@mui/icons-material';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { useTranslation } from 'react-i18next';\nimport { useUser } from '../../contexts/UserContext';\n\ninterface SidebarProps {\n  onItemClick?: () => void;\n}\n\nconst Sidebar: React.FC<SidebarProps> = ({ onItemClick }) => {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const { t } = useTranslation();\n  const { user } = useUser();\n\n  const menuItems = [\n    {\n      text: t('dashboard'),\n      icon: <DashboardIcon />,\n      path: '/dashboard',\n    },\n    {\n      text: t('students'),\n      icon: <StudentsIcon />,\n      path: '/students',\n    },\n    {\n      text: 'الفروع',\n      icon: <BranchesIcon />,\n      path: '/branches',\n    },\n    {\n      text: 'الصفوف التعليمية',\n      icon: <GradesIcon />,\n      path: '/grades',\n    },\n    {\n      text: t('teachers'),\n      icon: <TeachersIcon />,\n      path: '/teachers',\n    },\n    {\n      text: t('groups'),\n      icon: <GroupsIcon />,\n      path: '/groups',\n    },\n    {\n      text: 'المواد الدراسية',\n      icon: <SubjectsIcon />,\n      path: '/subjects',\n    },\n    {\n      text: 'الحضور',\n      icon: <AttendanceIcon />,\n      path: '/attendance',\n      children: [\n        {\n          text: 'تسجيل حضور عادي',\n          icon: <AddIcon />,\n          path: '/attendance/add',\n        },\n        {\n          text: 'تسجيل حضور بالباركود',\n          icon: <BarcodeIcon />,\n          path: '/attendance/barcode',\n        },\n        {\n          text: 'تسجيل حضور بـ QR',\n          icon: <BarcodeIcon />,\n          path: '/attendance/qr',\n        },\n      ],\n    },\n    {\n      text: 'رسائل واتساب',\n      icon: <WhatsAppIcon />,\n      path: '/whatsapp',\n      children: [\n        {\n          text: 'إرسال الرسائل',\n          icon: <WhatsAppIcon />,\n          path: '/whatsapp',\n        },\n        {\n          text: 'لوحة التحكم المحسنة',\n          icon: <WhatsAppIcon />,\n          path: '/whatsapp/enhanced',\n        },\n        {\n          text: 'واتساب مبسط',\n          icon: <WhatsAppIcon />,\n          path: '/simple-whatsapp',\n        },\n      ],\n    },\n    {\n      text: 'الملازم',\n      icon: <BookletsIcon />,\n      path: '/booklets',\n    },\n    {\n      text: 'إدارة الملازم',\n      icon: <FinanceIcon />,\n      path: '/booklets/management',\n    },\n    {\n      text: 'استلام الملازم',\n      icon: <FinanceIcon />,\n      path: '/booklet-deliveries',\n    },\n    {\n      text: 'النظام المالي',\n      icon: <FinanceIcon />,\n      path: '/financial',\n      children: [\n        {\n          text: 'الاستلام والصرف النقدي',\n          icon: <FinanceIcon />,\n          path: '/financial/cash-transactions',\n        },\n        {\n          text: 'القيود اليومية',\n          icon: <FinanceIcon />,\n          path: '/financial/journal-entries',\n        },\n        {\n          text: 'دليل الحسابات',\n          icon: <FinanceIcon />,\n          path: '/financial/chart-of-accounts',\n        },\n        {\n          text: 'التقارير المالية',\n          icon: <ReportsIcon />,\n          path: '/financial/reports',\n        },\n      ],\n    },\n    {\n      text: 'الامتحانات',\n      icon: <ExamsIcon />,\n      path: '/exams',\n      subItems: [\n        {\n          text: 'قائمة الامتحانات',\n          icon: <ExamsIcon />,\n          path: '/exams',\n        },\n        {\n          text: 'إضافة امتحان',\n          icon: <AddIcon />,\n          path: '/exams/add',\n        },\n      ],\n    },\n    {\n      text: t('reports'),\n      icon: <ReportsIcon />,\n      path: '/reports',\n    },\n    {\n      text: t('settings'),\n      icon: <SettingsIcon />,\n      path: '/settings',\n      children: [\n        {\n          text: 'إدارة المستخدمين',\n          icon: <SettingsIcon />,\n          path: '/settings/users',\n        },\n        {\n          text: 'إعدادات عامة',\n          icon: <SettingsIcon />,\n          path: '/settings',\n        },\n      ],\n    },\n  ];\n\n  const handleItemClick = (path: string) => {\n    navigate(path);\n    if (onItemClick) {\n      onItemClick();\n    }\n  };\n\n  return (\n    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>\n      {/* Logo/Header */}\n      <Toolbar\n        sx={{\n          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',\n          color: 'white',\n          minHeight: '64px !important',\n        }}\n      >\n        <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>\n          <Avatar\n            sx={{\n              bgcolor: 'rgba(255,255,255,0.2)',\n              mr: 2,\n              width: 40,\n              height: 40,\n            }}\n          >\n            <StudentsIcon />\n          </Avatar>\n          <Box>\n            <Typography variant=\"h6\" sx={{ fontWeight: 600, fontSize: '1rem' }}>\n              نظام إدارة\n            </Typography>\n            <Typography variant=\"caption\" sx={{ opacity: 0.8 }}>\n              المراكز التعليمية\n            </Typography>\n          </Box>\n        </Box>\n      </Toolbar>\n\n      <Divider />\n\n      {/* Navigation Menu */}\n      <List sx={{ flexGrow: 1, pt: 2 }}>\n        {menuItems.map((item) => {\n          const isActive = location.pathname === item.path ||\n                          (item.path !== '/dashboard' && location.pathname.startsWith(item.path));\n\n          return (\n            <React.Fragment key={item.path}>\n              <ListItem disablePadding sx={{ mb: 0.5, px: 2 }}>\n                <ListItemButton\n                  onClick={() => handleItemClick(item.path)}\n                  sx={{\n                    borderRadius: 2,\n                    backgroundColor: isActive ? 'rgba(25, 118, 210, 0.1)' : 'transparent',\n                    color: isActive ? 'primary.main' : 'text.primary',\n                    '&:hover': {\n                      backgroundColor: isActive\n                        ? 'rgba(25, 118, 210, 0.15)'\n                        : 'rgba(0, 0, 0, 0.04)',\n                    },\n                    transition: 'all 0.2s ease-in-out',\n                  }}\n                >\n                  <ListItemIcon\n                    sx={{\n                      color: isActive ? 'primary.main' : 'text.secondary',\n                      minWidth: 40,\n                    }}\n                  >\n                    {item.icon}\n                  </ListItemIcon>\n                  <ListItemText\n                    primary={item.text}\n                    sx={{\n                      '& .MuiListItemText-primary': {\n                        fontWeight: isActive ? 600 : 400,\n                        fontSize: '0.95rem',\n                      },\n                    }}\n                  />\n                </ListItemButton>\n              </ListItem>\n\n              {/* Render children if they exist */}\n              {item.children && item.children.map((child) => {\n                const childIsActive = location.pathname === child.path;\n                return (\n                  <ListItem key={child.path} disablePadding sx={{ mb: 0.5, px: 2, pl: 4 }}>\n                    <ListItemButton\n                      onClick={() => handleItemClick(child.path)}\n                      sx={{\n                        borderRadius: 2,\n                        backgroundColor: childIsActive ? 'rgba(25, 118, 210, 0.1)' : 'transparent',\n                        color: childIsActive ? 'primary.main' : 'text.primary',\n                        '&:hover': {\n                          backgroundColor: childIsActive\n                            ? 'rgba(25, 118, 210, 0.15)'\n                            : 'rgba(0, 0, 0, 0.04)',\n                        },\n                        transition: 'all 0.2s ease-in-out',\n                        pl: 2,\n                      }}\n                    >\n                      <ListItemIcon\n                        sx={{\n                          color: childIsActive ? 'primary.main' : 'text.secondary',\n                          minWidth: 30,\n                        }}\n                      >\n                        {'icon' in child ? child.icon : <WhatsAppIcon />}\n                      </ListItemIcon>\n                      <ListItemText\n                        primary={child.text}\n                        sx={{\n                          '& .MuiListItemText-primary': {\n                            fontWeight: childIsActive ? 600 : 400,\n                            fontSize: '0.85rem',\n                          },\n                        }}\n                      />\n                    </ListItemButton>\n                  </ListItem>\n                );\n              })}\n            </React.Fragment>\n          );\n        })}\n      </List>\n\n      {/* Add Center Button for Global Admin */}\n      {user?.isGlobalAdmin && (\n        <>\n          <Divider />\n          <Box sx={{ p: 2 }}>\n            <Button\n              fullWidth\n              variant=\"contained\"\n              startIcon={<AddIcon />}\n              onClick={() => handleItemClick('/groups/add')}\n              sx={{\n                backgroundColor: 'primary.main',\n                '&:hover': {\n                  backgroundColor: 'primary.dark',\n                },\n              }}\n            >\n              إضافة مجموعة جديدة\n            </Button>\n          </Box>\n        </>\n      )}\n\n      <Divider />\n\n      {/* Footer */}\n      <Box sx={{ p: 2, textAlign: 'center' }}>\n        <Typography variant=\"caption\" color=\"text.secondary\">\n          الإصدار 1.0.0\n        </Typography>\n      </Box>\n    </Box>\n  );\n};\n\nexport default Sidebar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,GAAG,EACHC,IAAI,EACJC,QAAQ,EACRC,cAAc,EACdC,YAAY,EACZC,YAAY,EACZC,OAAO,EACPC,UAAU,EACVC,OAAO,EACPC,MAAM,EACNC,MAAM,QACD,eAAe;AACtB,SACEC,SAAS,IAAIC,aAAa,EAC1BC,MAAM,IAAIC,YAAY,EACtBC,QAAQ,IAAIC,YAAY,EACxBC,MAAM,IAAIC,YAAY,EACtBC,KAAK,IAAIC,UAAU,EACnBC,QAAQ,IAAIC,YAAY,EACxBC,KAAK,IAAIC,UAAU,EACnBC,UAAU,IAAIC,WAAW,EACzBC,IAAI,IAAIC,SAAS,EACjBC,QAAQ,IAAIC,YAAY,EACxBC,GAAG,IAAIC,OAAO,EACdC,cAAc,IAAIC,cAAc,EAChCC,aAAa,IAAIC,WAAW,EAC5BC,QAAQ,IAAIC,YAAY,EACxBC,OAAO,IAAIC,YAAY,EACvBC,OAAO,IAAIC,WAAW,QACjB,qBAAqB;AAC5B,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SAASC,cAAc,QAAQ,eAAe;AAC9C,SAASC,OAAO,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAMrD,MAAMC,OAA+B,GAAGA,CAAC;EAAEC;AAAY,CAAC,KAAK;EAAAC,EAAA;EAC3D,MAAMC,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAMY,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEY;EAAE,CAAC,GAAGX,cAAc,CAAC,CAAC;EAC9B,MAAM;IAAEY;EAAK,CAAC,GAAGX,OAAO,CAAC,CAAC;EAE1B,MAAMY,SAAS,GAAG,CAChB;IACEC,IAAI,EAAEH,CAAC,CAAC,WAAW,CAAC;IACpBI,IAAI,eAAEZ,OAAA,CAACpC,aAAa;MAAAiD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvBC,IAAI,EAAE;EACR,CAAC,EACD;IACEN,IAAI,EAAEH,CAAC,CAAC,UAAU,CAAC;IACnBI,IAAI,eAAEZ,OAAA,CAAClC,YAAY;MAAA+C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,IAAI,EAAE;EACR,CAAC,EACD;IACEN,IAAI,EAAE,QAAQ;IACdC,IAAI,eAAEZ,OAAA,CAAChC,YAAY;MAAA6C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,IAAI,EAAE;EACR,CAAC,EACD;IACEN,IAAI,EAAE,kBAAkB;IACxBC,IAAI,eAAEZ,OAAA,CAACxB,UAAU;MAAAqC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACpBC,IAAI,EAAE;EACR,CAAC,EACD;IACEN,IAAI,EAAEH,CAAC,CAAC,UAAU,CAAC;IACnBI,IAAI,eAAEZ,OAAA,CAAC9B,YAAY;MAAA2C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,IAAI,EAAE;EACR,CAAC,EACD;IACEN,IAAI,EAAEH,CAAC,CAAC,QAAQ,CAAC;IACjBI,IAAI,eAAEZ,OAAA,CAAC5B,UAAU;MAAAyC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACpBC,IAAI,EAAE;EACR,CAAC,EACD;IACEN,IAAI,EAAE,iBAAiB;IACvBC,IAAI,eAAEZ,OAAA,CAAC1B,YAAY;MAAAuC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,IAAI,EAAE;EACR,CAAC,EACD;IACEN,IAAI,EAAE,QAAQ;IACdC,IAAI,eAAEZ,OAAA,CAACd,cAAc;MAAA2B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxBC,IAAI,EAAE,aAAa;IACnBC,QAAQ,EAAE,CACR;MACEP,IAAI,EAAE,iBAAiB;MACvBC,IAAI,eAAEZ,OAAA,CAAChB,OAAO;QAAA6B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACjBC,IAAI,EAAE;IACR,CAAC,EACD;MACEN,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,eAAEZ,OAAA,CAACZ,WAAW;QAAAyB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACrBC,IAAI,EAAE;IACR,CAAC,EACD;MACEN,IAAI,EAAE,kBAAkB;MACxBC,IAAI,eAAEZ,OAAA,CAACZ,WAAW;QAAAyB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACrBC,IAAI,EAAE;IACR,CAAC;EAEL,CAAC,EACD;IACEN,IAAI,EAAE,cAAc;IACpBC,IAAI,eAAEZ,OAAA,CAACV,YAAY;MAAAuB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,IAAI,EAAE,WAAW;IACjBC,QAAQ,EAAE,CACR;MACEP,IAAI,EAAE,eAAe;MACrBC,IAAI,eAAEZ,OAAA,CAACV,YAAY;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACtBC,IAAI,EAAE;IACR,CAAC,EACD;MACEN,IAAI,EAAE,qBAAqB;MAC3BC,IAAI,eAAEZ,OAAA,CAACV,YAAY;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACtBC,IAAI,EAAE;IACR,CAAC,EACD;MACEN,IAAI,EAAE,aAAa;MACnBC,IAAI,eAAEZ,OAAA,CAACV,YAAY;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACtBC,IAAI,EAAE;IACR,CAAC;EAEL,CAAC,EACD;IACEN,IAAI,EAAE,SAAS;IACfC,IAAI,eAAEZ,OAAA,CAACR,YAAY;MAAAqB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,IAAI,EAAE;EACR,CAAC,EACD;IACEN,IAAI,EAAE,eAAe;IACrBC,IAAI,eAAEZ,OAAA,CAACN,WAAW;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACrBC,IAAI,EAAE;EACR,CAAC,EACD;IACEN,IAAI,EAAE,gBAAgB;IACtBC,IAAI,eAAEZ,OAAA,CAACN,WAAW;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACrBC,IAAI,EAAE;EACR,CAAC,EACD;IACEN,IAAI,EAAE,eAAe;IACrBC,IAAI,eAAEZ,OAAA,CAACN,WAAW;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACrBC,IAAI,EAAE,YAAY;IAClBC,QAAQ,EAAE,CACR;MACEP,IAAI,EAAE,wBAAwB;MAC9BC,IAAI,eAAEZ,OAAA,CAACN,WAAW;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACrBC,IAAI,EAAE;IACR,CAAC,EACD;MACEN,IAAI,EAAE,gBAAgB;MACtBC,IAAI,eAAEZ,OAAA,CAACN,WAAW;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACrBC,IAAI,EAAE;IACR,CAAC,EACD;MACEN,IAAI,EAAE,eAAe;MACrBC,IAAI,eAAEZ,OAAA,CAACN,WAAW;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACrBC,IAAI,EAAE;IACR,CAAC,EACD;MACEN,IAAI,EAAE,kBAAkB;MACxBC,IAAI,eAAEZ,OAAA,CAACtB,WAAW;QAAAmC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACrBC,IAAI,EAAE;IACR,CAAC;EAEL,CAAC,EACD;IACEN,IAAI,EAAE,YAAY;IAClBC,IAAI,eAAEZ,OAAA,CAACpB,SAAS;MAAAiC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACnBC,IAAI,EAAE,QAAQ;IACdE,QAAQ,EAAE,CACR;MACER,IAAI,EAAE,kBAAkB;MACxBC,IAAI,eAAEZ,OAAA,CAACpB,SAAS;QAAAiC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACnBC,IAAI,EAAE;IACR,CAAC,EACD;MACEN,IAAI,EAAE,cAAc;MACpBC,IAAI,eAAEZ,OAAA,CAAChB,OAAO;QAAA6B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACjBC,IAAI,EAAE;IACR,CAAC;EAEL,CAAC,EACD;IACEN,IAAI,EAAEH,CAAC,CAAC,SAAS,CAAC;IAClBI,IAAI,eAAEZ,OAAA,CAACtB,WAAW;MAAAmC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACrBC,IAAI,EAAE;EACR,CAAC,EACD;IACEN,IAAI,EAAEH,CAAC,CAAC,UAAU,CAAC;IACnBI,IAAI,eAAEZ,OAAA,CAAClB,YAAY;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBC,IAAI,EAAE,WAAW;IACjBC,QAAQ,EAAE,CACR;MACEP,IAAI,EAAE,kBAAkB;MACxBC,IAAI,eAAEZ,OAAA,CAAClB,YAAY;QAAA+B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACtBC,IAAI,EAAE;IACR,CAAC,EACD;MACEN,IAAI,EAAE,cAAc;MACpBC,IAAI,eAAEZ,OAAA,CAAClB,YAAY;QAAA+B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACtBC,IAAI,EAAE;IACR,CAAC;EAEL,CAAC,CACF;EAED,MAAMG,eAAe,GAAIH,IAAY,IAAK;IACxCX,QAAQ,CAACW,IAAI,CAAC;IACd,IAAIb,WAAW,EAAE;MACfA,WAAW,CAAC,CAAC;IACf;EACF,CAAC;EAED,oBACEJ,OAAA,CAAChD,GAAG;IAACqE,EAAE,EAAE;MAAEC,MAAM,EAAE,MAAM;MAAEC,OAAO,EAAE,MAAM;MAAEC,aAAa,EAAE;IAAS,CAAE;IAAAN,QAAA,gBAEpElB,OAAA,CAAC1C,OAAO;MACN+D,EAAE,EAAE;QACFI,UAAU,EAAE,mDAAmD;QAC/DC,KAAK,EAAE,OAAO;QACdC,SAAS,EAAE;MACb,CAAE;MAAAT,QAAA,eAEFlB,OAAA,CAAChD,GAAG;QAACqE,EAAE,EAAE;UAAEE,OAAO,EAAE,MAAM;UAAEK,UAAU,EAAE,QAAQ;UAAEC,KAAK,EAAE;QAAO,CAAE;QAAAX,QAAA,gBAChElB,OAAA,CAACvC,MAAM;UACL4D,EAAE,EAAE;YACFS,OAAO,EAAE,uBAAuB;YAChCC,EAAE,EAAE,CAAC;YACLF,KAAK,EAAE,EAAE;YACTP,MAAM,EAAE;UACV,CAAE;UAAAJ,QAAA,eAEFlB,OAAA,CAAClC,YAAY;YAAA+C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACThB,OAAA,CAAChD,GAAG;UAAAkE,QAAA,gBACFlB,OAAA,CAACzC,UAAU;YAACyE,OAAO,EAAC,IAAI;YAACX,EAAE,EAAE;cAAEY,UAAU,EAAE,GAAG;cAAEC,QAAQ,EAAE;YAAO,CAAE;YAAAhB,QAAA,EAAC;UAEpE;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbhB,OAAA,CAACzC,UAAU;YAACyE,OAAO,EAAC,SAAS;YAACX,EAAE,EAAE;cAAEc,OAAO,EAAE;YAAI,CAAE;YAAAjB,QAAA,EAAC;UAEpD;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEVhB,OAAA,CAACxC,OAAO;MAAAqD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGXhB,OAAA,CAAC/C,IAAI;MAACoE,EAAE,EAAE;QAAEe,QAAQ,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAnB,QAAA,EAC9BR,SAAS,CAAC4B,GAAG,CAAEC,IAAI,IAAK;QACvB,MAAMC,QAAQ,GAAGjC,QAAQ,CAACkC,QAAQ,KAAKF,IAAI,CAACtB,IAAI,IAC/BsB,IAAI,CAACtB,IAAI,KAAK,YAAY,IAAIV,QAAQ,CAACkC,QAAQ,CAACC,UAAU,CAACH,IAAI,CAACtB,IAAI,CAAE;QAEvF,oBACEjB,OAAA,CAACjD,KAAK,CAACkD,QAAQ;UAAAiB,QAAA,gBACblB,OAAA,CAAC9C,QAAQ;YAACyF,cAAc;YAACtB,EAAE,EAAE;cAAEuB,EAAE,EAAE,GAAG;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAA3B,QAAA,eAC9ClB,OAAA,CAAC7C,cAAc;cACb2F,OAAO,EAAEA,CAAA,KAAM1B,eAAe,CAACmB,IAAI,CAACtB,IAAI,CAAE;cAC1CI,EAAE,EAAE;gBACF0B,YAAY,EAAE,CAAC;gBACfC,eAAe,EAAER,QAAQ,GAAG,yBAAyB,GAAG,aAAa;gBACrEd,KAAK,EAAEc,QAAQ,GAAG,cAAc,GAAG,cAAc;gBACjD,SAAS,EAAE;kBACTQ,eAAe,EAAER,QAAQ,GACrB,0BAA0B,GAC1B;gBACN,CAAC;gBACDS,UAAU,EAAE;cACd,CAAE;cAAA/B,QAAA,gBAEFlB,OAAA,CAAC5C,YAAY;gBACXiE,EAAE,EAAE;kBACFK,KAAK,EAAEc,QAAQ,GAAG,cAAc,GAAG,gBAAgB;kBACnDU,QAAQ,EAAE;gBACZ,CAAE;gBAAAhC,QAAA,EAEDqB,IAAI,CAAC3B;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eACfhB,OAAA,CAAC3C,YAAY;gBACX8F,OAAO,EAAEZ,IAAI,CAAC5B,IAAK;gBACnBU,EAAE,EAAE;kBACF,4BAA4B,EAAE;oBAC5BY,UAAU,EAAEO,QAAQ,GAAG,GAAG,GAAG,GAAG;oBAChCN,QAAQ,EAAE;kBACZ;gBACF;cAAE;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,EAGVuB,IAAI,CAACrB,QAAQ,IAAIqB,IAAI,CAACrB,QAAQ,CAACoB,GAAG,CAAEc,KAAK,IAAK;YAC7C,MAAMC,aAAa,GAAG9C,QAAQ,CAACkC,QAAQ,KAAKW,KAAK,CAACnC,IAAI;YACtD,oBACEjB,OAAA,CAAC9C,QAAQ;cAAkByF,cAAc;cAACtB,EAAE,EAAE;gBAAEuB,EAAE,EAAE,GAAG;gBAAEC,EAAE,EAAE,CAAC;gBAAES,EAAE,EAAE;cAAE,CAAE;cAAApC,QAAA,eACtElB,OAAA,CAAC7C,cAAc;gBACb2F,OAAO,EAAEA,CAAA,KAAM1B,eAAe,CAACgC,KAAK,CAACnC,IAAI,CAAE;gBAC3CI,EAAE,EAAE;kBACF0B,YAAY,EAAE,CAAC;kBACfC,eAAe,EAAEK,aAAa,GAAG,yBAAyB,GAAG,aAAa;kBAC1E3B,KAAK,EAAE2B,aAAa,GAAG,cAAc,GAAG,cAAc;kBACtD,SAAS,EAAE;oBACTL,eAAe,EAAEK,aAAa,GAC1B,0BAA0B,GAC1B;kBACN,CAAC;kBACDJ,UAAU,EAAE,sBAAsB;kBAClCK,EAAE,EAAE;gBACN,CAAE;gBAAApC,QAAA,gBAEFlB,OAAA,CAAC5C,YAAY;kBACXiE,EAAE,EAAE;oBACFK,KAAK,EAAE2B,aAAa,GAAG,cAAc,GAAG,gBAAgB;oBACxDH,QAAQ,EAAE;kBACZ,CAAE;kBAAAhC,QAAA,EAED,MAAM,IAAIkC,KAAK,GAAGA,KAAK,CAACxC,IAAI,gBAAGZ,OAAA,CAACV,YAAY;oBAAAuB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC,CAAC,eACfhB,OAAA,CAAC3C,YAAY;kBACX8F,OAAO,EAAEC,KAAK,CAACzC,IAAK;kBACpBU,EAAE,EAAE;oBACF,4BAA4B,EAAE;sBAC5BY,UAAU,EAAEoB,aAAa,GAAG,GAAG,GAAG,GAAG;sBACrCnB,QAAQ,EAAE;oBACZ;kBACF;gBAAE;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACY;YAAC,GAjCJoC,KAAK,CAACnC,IAAI;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAkCf,CAAC;UAEf,CAAC,CAAC;QAAA,GA5EiBuB,IAAI,CAACtB,IAAI;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA6Ed,CAAC;MAErB,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGN,CAAAP,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8C,aAAa,kBAClBvD,OAAA,CAAAE,SAAA;MAAAgB,QAAA,gBACElB,OAAA,CAACxC,OAAO;QAAAqD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACXhB,OAAA,CAAChD,GAAG;QAACqE,EAAE,EAAE;UAAEmC,CAAC,EAAE;QAAE,CAAE;QAAAtC,QAAA,eAChBlB,OAAA,CAACtC,MAAM;UACL+F,SAAS;UACTzB,OAAO,EAAC,WAAW;UACnB0B,SAAS,eAAE1D,OAAA,CAAChB,OAAO;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvB8B,OAAO,EAAEA,CAAA,KAAM1B,eAAe,CAAC,aAAa,CAAE;UAC9CC,EAAE,EAAE;YACF2B,eAAe,EAAE,cAAc;YAC/B,SAAS,EAAE;cACTA,eAAe,EAAE;YACnB;UACF,CAAE;UAAA9B,QAAA,EACH;QAED;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA,eACN,CACH,eAEDhB,OAAA,CAACxC,OAAO;MAAAqD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGXhB,OAAA,CAAChD,GAAG;MAACqE,EAAE,EAAE;QAAEmC,CAAC,EAAE,CAAC;QAAEG,SAAS,EAAE;MAAS,CAAE;MAAAzC,QAAA,eACrClB,OAAA,CAACzC,UAAU;QAACyE,OAAO,EAAC,SAAS;QAACN,KAAK,EAAC,gBAAgB;QAAAR,QAAA,EAAC;MAErD;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACX,EAAA,CA5UIF,OAA+B;EAAA,QAClBR,WAAW,EACXC,WAAW,EACdC,cAAc,EACXC,OAAO;AAAA;AAAA8D,EAAA,GAJpBzD,OAA+B;AA8UrC,eAAeA,OAAO;AAAC,IAAAyD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}