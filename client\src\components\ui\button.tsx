import React from 'react';
import { Button as Mu<PERSON>Button } from '@mui/material';

interface ButtonProps {
    children: React.ReactNode;
    onClick?: () => void;
    disabled?: boolean;
    variant?: 'default' | 'outline' | 'ghost';
    size?: 'sm' | 'md' | 'lg';
    className?: string;
}

export const Button: React.FC<ButtonProps> = ({ 
    children, 
    onClick, 
    disabled, 
    variant = 'default', 
    size = 'md',
    className 
}) => {
    const getVariant = () => {
        switch (variant) {
            case 'outline':
                return 'outlined';
            case 'ghost':
                return 'text';
            default:
                return 'contained';
        }
    };

    const getSize = () => {
        switch (size) {
            case 'sm':
                return 'small';
            case 'lg':
                return 'large';
            default:
                return 'medium';
        }
    };

    return (
        <MuiButton
            variant={getVariant()}
            size={getSize()}
            onClick={onClick}
            disabled={disabled}
            className={className}
        >
            {children}
        </MuiButton>
    );
};
