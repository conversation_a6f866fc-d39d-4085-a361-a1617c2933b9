import React from 'react';
import { Chip } from '@mui/material';

interface BadgeProps {
    children: React.ReactNode;
    variant?: 'default' | 'secondary' | 'destructive';
    className?: string;
}

export const Badge: React.FC<BadgeProps> = ({ children, variant = 'default', className }) => {
    const getColor = () => {
        switch (variant) {
            case 'secondary':
                return 'default';
            case 'destructive':
                return 'error';
            default:
                return 'primary';
        }
    };

    return (
        <Chip
            label={children}
            color={getColor()}
            size="small"
            className={className}
        />
    );
};
