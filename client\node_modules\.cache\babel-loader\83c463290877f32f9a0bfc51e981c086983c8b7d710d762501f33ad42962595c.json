{"ast": null, "code": "var _jsxFileName = \"I:\\\\\\u0628\\u0631\\u0645\\u062C\\u0629\\\\\\u062A\\u0639\\u0644\\u064A\\u0645\\u064A ali2 2\\\\\\u062A\\u0639\\u0644\\u064A\\u0645\\u064A 2\\\\\\u0627\\u062F\\u0627\\u0631\\u0629 \\u0645\\u0631\\u0627\\u0643\\u0632 \\u062A\\u0639\\u0644\\u064A\\u0645\\u064A\\u0629\\\\edu 2\\\\client\\\\src\\\\components\\\\ui\\\\separator.tsx\";\nimport React from 'react';\nimport { Divider } from '@mui/material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const Separator = ({\n  className\n}) => {\n  return /*#__PURE__*/_jsxDEV(Divider, {\n    className: className,\n    sx: {\n      my: 2\n    }\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 9,\n    columnNumber: 12\n  }, this);\n};\n_c = Separator;\nvar _c;\n$RefreshReg$(_c, \"Separator\");", "map": {"version": 3, "names": ["React", "Divider", "jsxDEV", "_jsxDEV", "Separator", "className", "sx", "my", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["I:/برمجة/تعليمي ali2 2/تعليمي 2/ادارة مراكز تعليمية/edu 2/client/src/components/ui/separator.tsx"], "sourcesContent": ["import React from 'react';\nimport { Divider } from '@mui/material';\n\ninterface SeparatorProps {\n    className?: string;\n}\n\nexport const Separator: React.FC<SeparatorProps> = ({ className }) => {\n    return <Divider className={className} sx={{ my: 2 }} />;\n};\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAMxC,OAAO,MAAMC,SAAmC,GAAGA,CAAC;EAAEC;AAAU,CAAC,KAAK;EAClE,oBAAOF,OAAA,CAACF,OAAO;IAACI,SAAS,EAAEA,SAAU;IAACC,EAAE,EAAE;MAAEC,EAAE,EAAE;IAAE;EAAE;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AAC3D,CAAC;AAACC,EAAA,GAFWR,SAAmC;AAAA,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}