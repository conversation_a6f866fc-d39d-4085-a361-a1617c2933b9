{"ast": null, "code": "var _jsxFileName = \"I:\\\\\\u0628\\u0631\\u0645\\u062C\\u0629\\\\\\u062A\\u0639\\u0644\\u064A\\u0645\\u064A ali2 2\\\\\\u062A\\u0639\\u0644\\u064A\\u0645\\u064A 2\\\\\\u0627\\u062F\\u0627\\u0631\\u0629 \\u0645\\u0631\\u0627\\u0643\\u0632 \\u062A\\u0639\\u0644\\u064A\\u0645\\u064A\\u0629\\\\edu 2\\\\client\\\\src\\\\pages\\\\WhatsApp\\\\WhatsAppMessaging.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Card, CardContent, Typography, Button, TextField, Grid, FormControl, InputLabel, Select, MenuItem, Checkbox, FormControlLabel, FormGroup, Alert, CircularProgress, Chip, List, ListItem, ListItemText, ListItemIcon, Dialog, DialogTitle, DialogContent, DialogActions } from '@mui/material';\nimport { WhatsApp as WhatsAppIcon, Send as SendIcon, Person as PersonIcon, QrCode as QrCodeIcon, CheckCircle as CheckCircleIcon, Error as ErrorIcon, Refresh as RefreshIcon } from '@mui/icons-material';\nimport { useSnackbar } from 'notistack';\nimport axios from 'axios';\nimport WhatsAppViewer from '../../components/WhatsAppViewer';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst WhatsAppMessaging = () => {\n  _s();\n  const {\n    enqueueSnackbar\n  } = useSnackbar();\n  const [groups, setGroups] = useState([]);\n  const [selectedGroup, setSelectedGroup] = useState(0);\n  const [students, setStudents] = useState([]);\n  const [message, setMessage] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [sending, setSending] = useState(false);\n  const [whatsappStatus, setWhatsappStatus] = useState(null);\n  const [qrDialogOpen, setQrDialogOpen] = useState(false);\n\n  // Recipient selection\n  const [sendToStudent, setSendToStudent] = useState(true);\n  const [sendToFather, setSendToFather] = useState(true);\n  const [sendToMother, setSendToMother] = useState(false);\n  useEffect(() => {\n    loadGroups();\n    checkWhatsAppStatus();\n  }, []);\n  useEffect(() => {\n    if (selectedGroup > 0) {\n      loadStudents();\n    }\n  }, [selectedGroup]);\n  const loadGroups = async () => {\n    try {\n      const institutionId = localStorage.getItem('institutionId') || '1';\n      const response = await axios.get(`/api/groups?centerID=${institutionId}&limit=100`);\n      if (response.data.success) {\n        setGroups(response.data.data);\n      }\n    } catch (error) {\n      console.error('Error loading groups:', error);\n      enqueueSnackbar('خطأ في تحميل المجموعات', {\n        variant: 'error'\n      });\n    }\n  };\n  const loadStudents = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get(`/api/groups/${selectedGroup}/students`);\n      if (response.data.success) {\n        setStudents(response.data.data);\n      }\n    } catch (error) {\n      console.error('Error loading students:', error);\n      enqueueSnackbar('خطأ في تحميل الطلاب', {\n        variant: 'error'\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n  const checkWhatsAppStatus = async () => {\n    try {\n      const response = await axios.get('/api/whatsapp/status');\n      setWhatsappStatus(response.data);\n    } catch (error) {\n      console.error('Error checking WhatsApp status:', error);\n    }\n  };\n  const initializeWhatsApp = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.post('/api/whatsapp/initialize');\n      if (response.data.success) {\n        enqueueSnackbar('تم تشغيل الواتساب بنجاح', {\n          variant: 'success'\n        });\n        // Check status after initialization\n        setTimeout(checkWhatsAppStatus, 2000);\n      }\n    } catch (error) {\n      console.error('Error initializing WhatsApp:', error);\n      enqueueSnackbar('خطأ في تشغيل الواتساب', {\n        variant: 'error'\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n  const disconnectWhatsApp = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.post('/api/whatsapp/disconnect');\n      if (response.data.success) {\n        enqueueSnackbar('تم قطع الاتصال بنجاح', {\n          variant: 'success'\n        });\n        checkWhatsAppStatus();\n      }\n    } catch (error) {\n      console.error('Error disconnecting WhatsApp:', error);\n      enqueueSnackbar('خطأ في قطع الاتصال', {\n        variant: 'error'\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n  const sendMessages = async () => {\n    if (!selectedGroup || !message.trim()) {\n      enqueueSnackbar('يرجى اختيار المجموعة وكتابة الرسالة', {\n        variant: 'warning'\n      });\n      return;\n    }\n    if (!(whatsappStatus !== null && whatsappStatus !== void 0 && whatsappStatus.isReady)) {\n      enqueueSnackbar('تحذير: الواتساب غير متصل - سيتم محاولة الإرسال', {\n        variant: 'warning'\n      });\n    }\n    try {\n      setSending(true);\n\n      // Prepare recipients list\n      const recipients = [];\n      students.forEach(student => {\n        if (sendToStudent && student.student_phone) {\n          recipients.push({\n            name: student.student_name,\n            phone: student.student_phone,\n            type: 'طالب'\n          });\n        }\n        if (sendToFather && student.father_phone) {\n          recipients.push({\n            name: `والد ${student.student_name}`,\n            phone: student.father_phone,\n            type: 'والد'\n          });\n        }\n        if (sendToMother && student.mother_phone) {\n          recipients.push({\n            name: `والدة ${student.student_name}`,\n            phone: student.mother_phone,\n            type: 'والدة'\n          });\n        }\n      });\n      if (recipients.length === 0) {\n        enqueueSnackbar('لا توجد أرقام هواتف للإرسال إليها', {\n          variant: 'warning'\n        });\n        return;\n      }\n      const response = await axios.post('/api/whatsapp/send-bulk', {\n        recipients,\n        message,\n        group_id: selectedGroup\n      });\n      if (response.data.success) {\n        enqueueSnackbar(`تم إرسال ${recipients.length} رسالة بنجاح`, {\n          variant: 'success'\n        });\n        setMessage('');\n      }\n    } catch (error) {\n      console.error('Error sending messages:', error);\n      enqueueSnackbar('خطأ في إرسال الرسائل', {\n        variant: 'error'\n      });\n    } finally {\n      setSending(false);\n    }\n  };\n  const getStatusColor = status => {\n    switch (status) {\n      case 'ready':\n        return 'success';\n      case 'qr_received':\n        return 'warning';\n      case 'authenticated':\n        return 'info';\n      case 'disconnected':\n        return 'default';\n      case 'error':\n        return 'error';\n      default:\n        return 'default';\n    }\n  };\n  const getStatusText = status => {\n    switch (status) {\n      case 'ready':\n        return 'متصل ومستعد';\n      case 'qr_received':\n        return 'في انتظار مسح QR Code';\n      case 'authenticated':\n        return 'تم التوثيق';\n      case 'disconnected':\n        return 'غير متصل';\n      case 'error':\n        return 'خطأ في الاتصال';\n      default:\n        return 'غير معروف';\n    }\n  };\n  const validRecipients = students.filter(student => sendToStudent && student.student_phone || sendToFather && student.father_phone || sendToMother && student.mother_phone).length;\n  return /*#__PURE__*/_jsxDEV(Box, {\n    p: 3,\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      display: \"flex\",\n      alignItems: \"center\",\n      mb: 3,\n      children: [/*#__PURE__*/_jsxDEV(WhatsAppIcon, {\n        sx: {\n          mr: 2,\n          color: '#25D366',\n          fontSize: 40\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 262,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h4\",\n        component: \"h1\",\n        children: \"\\u0625\\u0631\\u0633\\u0627\\u0644 \\u0631\\u0633\\u0627\\u0626\\u0644 \\u0648\\u0627\\u062A\\u0633\\u0627\\u0628 \\u0644\\u0644\\u0645\\u062C\\u0645\\u0648\\u0639\\u0627\\u062A\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 261,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Grid, {\n      container: true,\n      spacing: 3,\n      sx: {\n        mb: 3\n      },\n      children: [/*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(CardContent, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              gutterBottom: true,\n              children: \"\\u062D\\u0627\\u0644\\u0629 \\u0627\\u0644\\u0648\\u0627\\u062A\\u0633\\u0627\\u0628\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              alignItems: \"center\",\n              gap: 2,\n              mb: 2,\n              children: [/*#__PURE__*/_jsxDEV(Chip, {\n                icon: whatsappStatus !== null && whatsappStatus !== void 0 && whatsappStatus.isReady ? /*#__PURE__*/_jsxDEV(CheckCircleIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 278,\n                  columnNumber: 51\n                }, this) : /*#__PURE__*/_jsxDEV(ErrorIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 278,\n                  columnNumber: 73\n                }, this),\n                label: whatsappStatus ? getStatusText(whatsappStatus.status) : 'جاري التحقق...',\n                color: whatsappStatus ? getStatusColor(whatsappStatus.status) : 'default'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 277,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outlined\",\n                onClick: checkWhatsAppStatus,\n                startIcon: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 285,\n                  columnNumber: 30\n                }, this),\n                size: \"small\",\n                children: \"\\u062A\\u062D\\u062F\\u064A\\u062B \\u0627\\u0644\\u062D\\u0627\\u0644\\u0629\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 282,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 276,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              display: \"flex\",\n              gap: 2,\n              children: [!(whatsappStatus !== null && whatsappStatus !== void 0 && whatsappStatus.isReady) && /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"contained\",\n                onClick: initializeWhatsApp,\n                disabled: loading,\n                startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                  size: 20\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 298,\n                  columnNumber: 42\n                }, this) : /*#__PURE__*/_jsxDEV(WhatsAppIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 298,\n                  columnNumber: 75\n                }, this),\n                children: \"\\u062A\\u0634\\u063A\\u064A\\u0644 \\u0627\\u0644\\u0648\\u0627\\u062A\\u0633\\u0627\\u0628\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 294,\n                columnNumber: 19\n              }, this), (whatsappStatus === null || whatsappStatus === void 0 ? void 0 : whatsappStatus.hasQR) && /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outlined\",\n                onClick: () => setQrDialogOpen(true),\n                startIcon: /*#__PURE__*/_jsxDEV(QrCodeIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 308,\n                  columnNumber: 32\n                }, this),\n                children: \"\\u0639\\u0631\\u0636 QR Code\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 305,\n                columnNumber: 19\n              }, this), (whatsappStatus === null || whatsappStatus === void 0 ? void 0 : whatsappStatus.isReady) && /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outlined\",\n                color: \"error\",\n                onClick: disconnectWhatsApp,\n                disabled: loading,\n                children: \"\\u0642\\u0637\\u0639 \\u0627\\u0644\\u0627\\u062A\\u0635\\u0627\\u0644\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 292,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 270,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        item: true,\n        xs: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(WhatsAppViewer, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 329,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 269,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"\\u0627\\u062E\\u062A\\u064A\\u0627\\u0631 \\u0627\\u0644\\u0645\\u062C\\u0645\\u0648\\u0639\\u0629\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 337,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(FormControl, {\n          fullWidth: true,\n          children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n            children: \"\\u0627\\u0644\\u0645\\u062C\\u0645\\u0648\\u0639\\u0629\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 341,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            value: selectedGroup,\n            onChange: e => setSelectedGroup(Number(e.target.value)),\n            label: \"\\u0627\\u0644\\u0645\\u062C\\u0645\\u0648\\u0639\\u0629\",\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              value: 0,\n              children: \"\\u0627\\u062E\\u062A\\u0631 \\u0627\\u0644\\u0645\\u062C\\u0645\\u0648\\u0639\\u0629\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 347,\n              columnNumber: 15\n            }, this), groups.map(group => /*#__PURE__*/_jsxDEV(MenuItem, {\n              value: group.id,\n              children: [group.group_name, \" - \", group.teacher_name, \" (\", group.student_count, \" \\u0637\\u0627\\u0644\\u0628)\"]\n            }, group.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 349,\n              columnNumber: 17\n            }, this))]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 342,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 340,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 336,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 335,\n      columnNumber: 7\n    }, this), selectedGroup > 0 && /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"\\u0627\\u062E\\u062A\\u064A\\u0627\\u0631 \\u0627\\u0644\\u0645\\u0633\\u062A\\u0642\\u0628\\u0644\\u064A\\u0646\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 362,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(FormGroup, {\n          row: true,\n          children: [/*#__PURE__*/_jsxDEV(FormControlLabel, {\n            control: /*#__PURE__*/_jsxDEV(Checkbox, {\n              checked: sendToStudent,\n              onChange: e => setSendToStudent(e.target.checked)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 368,\n              columnNumber: 19\n            }, this),\n            label: \"\\u0627\\u0644\\u0637\\u0644\\u0627\\u0628\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n            control: /*#__PURE__*/_jsxDEV(Checkbox, {\n              checked: sendToFather,\n              onChange: e => setSendToFather(e.target.checked)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 377,\n              columnNumber: 19\n            }, this),\n            label: \"\\u0623\\u0648\\u0644\\u064A\\u0627\\u0621 \\u0627\\u0644\\u0623\\u0645\\u0648\\u0631 (\\u0627\\u0644\\u0622\\u0628\\u0627\\u0621)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 375,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(FormControlLabel, {\n            control: /*#__PURE__*/_jsxDEV(Checkbox, {\n              checked: sendToMother,\n              onChange: e => setSendToMother(e.target.checked)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 386,\n              columnNumber: 19\n            }, this),\n            label: \"\\u0623\\u0648\\u0644\\u064A\\u0627\\u0621 \\u0627\\u0644\\u0623\\u0645\\u0648\\u0631 (\\u0627\\u0644\\u0623\\u0645\\u0647\\u0627\\u062A)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 384,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 365,\n          columnNumber: 13\n        }, this), validRecipients > 0 && /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mt: 2\n          },\n          children: [\"\\u0633\\u064A\\u062A\\u0645 \\u0627\\u0644\\u0625\\u0631\\u0633\\u0627\\u0644 \\u0625\\u0644\\u0649 \", validRecipients, \" \\u0645\\u0633\\u062A\\u0642\\u0628\\u0644\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 396,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 361,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 360,\n      columnNumber: 9\n    }, this), selectedGroup > 0 && /*#__PURE__*/_jsxDEV(Card, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: \"\\u0643\\u062A\\u0627\\u0628\\u0629 \\u0627\\u0644\\u0631\\u0633\\u0627\\u0644\\u0629\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 408,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          fullWidth: true,\n          multiline: true,\n          rows: 6,\n          label: \"\\u0646\\u0635 \\u0627\\u0644\\u0631\\u0633\\u0627\\u0644\\u0629\",\n          value: message,\n          onChange: e => setMessage(e.target.value),\n          placeholder: \"\\u0627\\u0643\\u062A\\u0628 \\u0631\\u0633\\u0627\\u0644\\u062A\\u0643 \\u0647\\u0646\\u0627...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 411,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          mt: 2,\n          display: \"flex\",\n          justifyContent: \"space-between\",\n          alignItems: \"center\",\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"textSecondary\",\n            children: [\"\\u0639\\u062F\\u062F \\u0627\\u0644\\u0623\\u062D\\u0631\\u0641: \", message.length]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 422,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            onClick: sendMessages,\n            disabled: !message.trim() || sending || validRecipients === 0,\n            startIcon: sending ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n              size: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 430,\n              columnNumber: 38\n            }, this) : /*#__PURE__*/_jsxDEV(SendIcon, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 430,\n              columnNumber: 71\n            }, this),\n            size: \"large\",\n            color: whatsappStatus !== null && whatsappStatus !== void 0 && whatsappStatus.isReady ? 'primary' : 'warning',\n            children: sending ? 'جاري الإرسال...' : `إرسال إلى ${validRecipients} مستقبل`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 426,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 421,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 407,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 406,\n      columnNumber: 9\n    }, this), selectedGroup > 0 && students.length > 0 && /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(CardContent, {\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: [\"\\u0642\\u0627\\u0626\\u0645\\u0629 \\u0627\\u0644\\u0637\\u0644\\u0627\\u0628 (\", students.length, \")\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 445,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(List, {\n          children: [students.slice(0, 10).map(student => /*#__PURE__*/_jsxDEV(ListItem, {\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(PersonIcon, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 452,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 451,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: student.student_name,\n              secondary: /*#__PURE__*/_jsxDEV(React.Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  component: \"div\",\n                  children: [\"\\u0643\\u0648\\u062F \\u0627\\u0644\\u0637\\u0627\\u0644\\u0628: \", student.student_code]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 458,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    mt: 0.5\n                  },\n                  children: [student.student_phone && /*#__PURE__*/_jsxDEV(Chip, {\n                    size: \"small\",\n                    label: `طالب: ${student.student_phone}`,\n                    sx: {\n                      mr: 1,\n                      mt: 0.5\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 463,\n                    columnNumber: 29\n                  }, this), student.father_phone && /*#__PURE__*/_jsxDEV(Chip, {\n                    size: \"small\",\n                    label: `والد: ${student.father_phone}`,\n                    sx: {\n                      mr: 1,\n                      mt: 0.5\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 466,\n                    columnNumber: 29\n                  }, this), student.mother_phone && /*#__PURE__*/_jsxDEV(Chip, {\n                    size: \"small\",\n                    label: `والدة: ${student.mother_phone}`,\n                    sx: {\n                      mr: 1,\n                      mt: 0.5\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 469,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 461,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 457,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 454,\n              columnNumber: 19\n            }, this)]\n          }, student.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 450,\n            columnNumber: 17\n          }, this)), students.length > 10 && /*#__PURE__*/_jsxDEV(ListItem, {\n            children: /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: `... و ${students.length - 10} طالب آخر`,\n              sx: {\n                textAlign: 'center',\n                fontStyle: 'italic'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 479,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 478,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 448,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 444,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 443,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Dialog, {\n      open: qrDialogOpen,\n      onClose: () => setQrDialogOpen(false),\n      maxWidth: \"sm\",\n      fullWidth: true,\n      children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n        children: \"\\u0645\\u0633\\u062D QR Code \\u0644\\u0644\\u0648\\u0627\\u062A\\u0633\\u0627\\u0628\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 492,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n        children: whatsappStatus !== null && whatsappStatus !== void 0 && whatsappStatus.qrCode ? /*#__PURE__*/_jsxDEV(Box, {\n          textAlign: \"center\",\n          children: [/*#__PURE__*/_jsxDEV(\"img\", {\n            src: whatsappStatus.qrCode,\n            alt: \"WhatsApp QR Code\",\n            style: {\n              maxWidth: '100%',\n              height: 'auto'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 496,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            color: \"textSecondary\",\n            mt: 2,\n            children: \"\\u0627\\u0645\\u0633\\u062D \\u0647\\u0630\\u0627 \\u0627\\u0644\\u0643\\u0648\\u062F \\u0628\\u0643\\u0627\\u0645\\u064A\\u0631\\u0627 \\u0627\\u0644\\u0648\\u0627\\u062A\\u0633\\u0627\\u0628 \\u0639\\u0644\\u0649 \\u0647\\u0627\\u062A\\u0641\\u0643\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 501,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 495,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          children: \"\\u0644\\u0627 \\u064A\\u0648\\u062C\\u062F QR Code \\u0645\\u062A\\u0627\\u062D \\u062D\\u0627\\u0644\\u064A\\u0627\\u064B\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 506,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 493,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setQrDialogOpen(false),\n          children: \"\\u0625\\u063A\\u0644\\u0627\\u0642\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 512,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: checkWhatsAppStatus,\n          startIcon: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 513,\n            columnNumber: 60\n          }, this),\n          children: \"\\u062A\\u062D\\u062F\\u064A\\u062B\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 513,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 511,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 491,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 259,\n    columnNumber: 5\n  }, this);\n};\n_s(WhatsAppMessaging, \"uDYjqc4EffE7CEzjOD5kC/ZXItM=\", false, function () {\n  return [useSnackbar];\n});\n_c = WhatsAppMessaging;\nexport default WhatsAppMessaging;\nvar _c;\n$RefreshReg$(_c, \"WhatsAppMessaging\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "<PERSON><PERSON>", "TextField", "Grid", "FormControl", "InputLabel", "Select", "MenuItem", "Checkbox", "FormControlLabel", "FormGroup", "<PERSON><PERSON>", "CircularProgress", "Chip", "List", "ListItem", "ListItemText", "ListItemIcon", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "WhatsApp", "WhatsAppIcon", "Send", "SendIcon", "Person", "PersonIcon", "QrCode", "QrCodeIcon", "CheckCircle", "CheckCircleIcon", "Error", "ErrorIcon", "Refresh", "RefreshIcon", "useSnackbar", "axios", "WhatsAppViewer", "jsxDEV", "_jsxDEV", "WhatsAppMessaging", "_s", "enqueueSnackbar", "groups", "setGroups", "selectedGroup", "setSelectedGroup", "students", "setStudents", "message", "setMessage", "loading", "setLoading", "sending", "setSending", "whatsappStatus", "setWhatsappStatus", "qrDialogOpen", "setQrDialogOpen", "sendToStudent", "setSendToStudent", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "loadGroups", "checkWhatsAppStatus", "loadStudents", "institutionId", "localStorage", "getItem", "response", "get", "data", "success", "error", "console", "variant", "initializeWhatsApp", "post", "setTimeout", "disconnectWhatsApp", "sendMessages", "trim", "isReady", "recipients", "for<PERSON>ach", "student", "student_phone", "push", "name", "student_name", "phone", "type", "father_phone", "mother_phone", "length", "group_id", "getStatusColor", "status", "getStatusText", "validRecipients", "filter", "p", "children", "display", "alignItems", "mb", "sx", "mr", "color", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "component", "container", "spacing", "item", "xs", "md", "gutterBottom", "gap", "icon", "label", "onClick", "startIcon", "size", "disabled", "hasQR", "fullWidth", "value", "onChange", "e", "Number", "target", "map", "group", "id", "group_name", "teacher_name", "student_count", "row", "control", "checked", "severity", "mt", "multiline", "rows", "placeholder", "justifyContent", "slice", "primary", "secondary", "Fragment", "student_code", "textAlign", "fontStyle", "open", "onClose", "max<PERSON><PERSON><PERSON>", "qrCode", "src", "alt", "style", "height", "_c", "$RefreshReg$"], "sources": ["I:/برمجة/تعليمي ali2 2/تعليمي 2/ادارة مراكز تعليمية/edu 2/client/src/pages/WhatsApp/WhatsAppMessaging.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  <PERSON>,\n  Card,\n  CardContent,\n  Typography,\n  Button,\n  TextField,\n  Grid,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Checkbox,\n  FormControlLabel,\n  FormGroup,\n  Alert,\n  CircularProgress,\n  Divider,\n  Chip,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemIcon,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n} from '@mui/material';\nimport {\n  WhatsApp as WhatsAppIcon,\n  Send as SendIcon,\n  Group as GroupIcon,\n  Person as PersonIcon,\n  Phone as PhoneIcon,\n  QrCode as QrCodeIcon,\n  CheckCircle as CheckCircleIcon,\n  Error as ErrorIcon,\n  Refresh as RefreshIcon,\n} from '@mui/icons-material';\nimport { useSnackbar } from 'notistack';\nimport axios from 'axios';\nimport WhatsAppViewer from '../../components/WhatsAppViewer';\n\ninterface Group {\n  id: number;\n  group_name: string;\n  group_code: string;\n  teacher_name: string;\n  student_count: number;\n}\n\ninterface Student {\n  id: number;\n  student_name: string;\n  student_code: string;\n  student_phone?: string;\n  father_phone?: string;\n  mother_phone?: string;\n}\n\ninterface WhatsAppStatus {\n  status: string;\n  isReady: boolean;\n  qrCode?: string;\n  hasQR: boolean;\n}\n\nconst WhatsAppMessaging: React.FC = () => {\n  const { enqueueSnackbar } = useSnackbar();\n  \n  const [groups, setGroups] = useState<Group[]>([]);\n  const [selectedGroup, setSelectedGroup] = useState<number>(0);\n  const [students, setStudents] = useState<Student[]>([]);\n  const [message, setMessage] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [sending, setSending] = useState(false);\n  const [whatsappStatus, setWhatsappStatus] = useState<WhatsAppStatus | null>(null);\n  const [qrDialogOpen, setQrDialogOpen] = useState(false);\n  \n  // Recipient selection\n  const [sendToStudent, setSendToStudent] = useState(true);\n  const [sendToFather, setSendToFather] = useState(true);\n  const [sendToMother, setSendToMother] = useState(false);\n\n  useEffect(() => {\n    loadGroups();\n    checkWhatsAppStatus();\n  }, []);\n\n  useEffect(() => {\n    if (selectedGroup > 0) {\n      loadStudents();\n    }\n  }, [selectedGroup]);\n\n  const loadGroups = async () => {\n    try {\n      const institutionId = localStorage.getItem('institutionId') || '1';\n      const response = await axios.get(`/api/groups?centerID=${institutionId}&limit=100`);\n      if (response.data.success) {\n        setGroups(response.data.data);\n      }\n    } catch (error) {\n      console.error('Error loading groups:', error);\n      enqueueSnackbar('خطأ في تحميل المجموعات', { variant: 'error' });\n    }\n  };\n\n  const loadStudents = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get(`/api/groups/${selectedGroup}/students`);\n      if (response.data.success) {\n        setStudents(response.data.data);\n      }\n    } catch (error) {\n      console.error('Error loading students:', error);\n      enqueueSnackbar('خطأ في تحميل الطلاب', { variant: 'error' });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const checkWhatsAppStatus = async () => {\n    try {\n      const response = await axios.get('/api/whatsapp/status');\n      setWhatsappStatus(response.data);\n    } catch (error) {\n      console.error('Error checking WhatsApp status:', error);\n    }\n  };\n\n  const initializeWhatsApp = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.post('/api/whatsapp/initialize');\n      if (response.data.success) {\n        enqueueSnackbar('تم تشغيل الواتساب بنجاح', { variant: 'success' });\n        // Check status after initialization\n        setTimeout(checkWhatsAppStatus, 2000);\n      }\n    } catch (error) {\n      console.error('Error initializing WhatsApp:', error);\n      enqueueSnackbar('خطأ في تشغيل الواتساب', { variant: 'error' });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const disconnectWhatsApp = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.post('/api/whatsapp/disconnect');\n      if (response.data.success) {\n        enqueueSnackbar('تم قطع الاتصال بنجاح', { variant: 'success' });\n        checkWhatsAppStatus();\n      }\n    } catch (error) {\n      console.error('Error disconnecting WhatsApp:', error);\n      enqueueSnackbar('خطأ في قطع الاتصال', { variant: 'error' });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const sendMessages = async () => {\n    if (!selectedGroup || !message.trim()) {\n      enqueueSnackbar('يرجى اختيار المجموعة وكتابة الرسالة', { variant: 'warning' });\n      return;\n    }\n\n    if (!whatsappStatus?.isReady) {\n      enqueueSnackbar('تحذير: الواتساب غير متصل - سيتم محاولة الإرسال', { variant: 'warning' });\n    }\n\n    try {\n      setSending(true);\n      \n      // Prepare recipients list\n      const recipients: Array<{name: string, phone: string, type: string}> = [];\n      \n      students.forEach(student => {\n        if (sendToStudent && student.student_phone) {\n          recipients.push({\n            name: student.student_name,\n            phone: student.student_phone,\n            type: 'طالب'\n          });\n        }\n        if (sendToFather && student.father_phone) {\n          recipients.push({\n            name: `والد ${student.student_name}`,\n            phone: student.father_phone,\n            type: 'والد'\n          });\n        }\n        if (sendToMother && student.mother_phone) {\n          recipients.push({\n            name: `والدة ${student.student_name}`,\n            phone: student.mother_phone,\n            type: 'والدة'\n          });\n        }\n      });\n\n      if (recipients.length === 0) {\n        enqueueSnackbar('لا توجد أرقام هواتف للإرسال إليها', { variant: 'warning' });\n        return;\n      }\n\n      const response = await axios.post('/api/whatsapp/send-bulk', {\n        recipients,\n        message,\n        group_id: selectedGroup\n      });\n\n      if (response.data.success) {\n        enqueueSnackbar(`تم إرسال ${recipients.length} رسالة بنجاح`, { variant: 'success' });\n        setMessage('');\n      }\n    } catch (error) {\n      console.error('Error sending messages:', error);\n      enqueueSnackbar('خطأ في إرسال الرسائل', { variant: 'error' });\n    } finally {\n      setSending(false);\n    }\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'ready': return 'success';\n      case 'qr_received': return 'warning';\n      case 'authenticated': return 'info';\n      case 'disconnected': return 'default';\n      case 'error': return 'error';\n      default: return 'default';\n    }\n  };\n\n  const getStatusText = (status: string) => {\n    switch (status) {\n      case 'ready': return 'متصل ومستعد';\n      case 'qr_received': return 'في انتظار مسح QR Code';\n      case 'authenticated': return 'تم التوثيق';\n      case 'disconnected': return 'غير متصل';\n      case 'error': return 'خطأ في الاتصال';\n      default: return 'غير معروف';\n    }\n  };\n\n  const validRecipients = students.filter(student => \n    (sendToStudent && student.student_phone) ||\n    (sendToFather && student.father_phone) ||\n    (sendToMother && student.mother_phone)\n  ).length;\n\n  return (\n    <Box p={3}>\n      {/* Header */}\n      <Box display=\"flex\" alignItems=\"center\" mb={3}>\n        <WhatsAppIcon sx={{ mr: 2, color: '#25D366', fontSize: 40 }} />\n        <Typography variant=\"h4\" component=\"h1\">\n          إرسال رسائل واتساب للمجموعات\n        </Typography>\n      </Box>\n\n      {/* WhatsApp Status and Viewer */}\n      <Grid container spacing={3} sx={{ mb: 3 }}>\n        <Grid item xs={12} md={6}>\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" gutterBottom>\n                حالة الواتساب\n              </Typography>\n              <Box display=\"flex\" alignItems=\"center\" gap={2} mb={2}>\n                <Chip\n                  icon={whatsappStatus?.isReady ? <CheckCircleIcon /> : <ErrorIcon />}\n                  label={whatsappStatus ? getStatusText(whatsappStatus.status) : 'جاري التحقق...'}\n                  color={whatsappStatus ? getStatusColor(whatsappStatus.status) : 'default'}\n                />\n                <Button\n                  variant=\"outlined\"\n                  onClick={checkWhatsAppStatus}\n                  startIcon={<RefreshIcon />}\n                  size=\"small\"\n                >\n                  تحديث الحالة\n                </Button>\n              </Box>\n\n              <Box display=\"flex\" gap={2}>\n                {!whatsappStatus?.isReady && (\n                  <Button\n                    variant=\"contained\"\n                    onClick={initializeWhatsApp}\n                    disabled={loading}\n                    startIcon={loading ? <CircularProgress size={20} /> : <WhatsAppIcon />}\n                  >\n                    تشغيل الواتساب\n                  </Button>\n                )}\n\n                {whatsappStatus?.hasQR && (\n                  <Button\n                    variant=\"outlined\"\n                    onClick={() => setQrDialogOpen(true)}\n                    startIcon={<QrCodeIcon />}\n                  >\n                    عرض QR Code\n                  </Button>\n                )}\n\n                {whatsappStatus?.isReady && (\n                  <Button\n                    variant=\"outlined\"\n                    color=\"error\"\n                    onClick={disconnectWhatsApp}\n                    disabled={loading}\n                  >\n                    قطع الاتصال\n                  </Button>\n                )}\n              </Box>\n            </CardContent>\n          </Card>\n        </Grid>\n\n        <Grid item xs={12} md={6}>\n          <WhatsAppViewer />\n        </Grid>\n      </Grid>\n\n      {/* Group Selection */}\n      <Card sx={{ mb: 3 }}>\n        <CardContent>\n          <Typography variant=\"h6\" gutterBottom>\n            اختيار المجموعة\n          </Typography>\n          <FormControl fullWidth>\n            <InputLabel>المجموعة</InputLabel>\n            <Select\n              value={selectedGroup}\n              onChange={(e) => setSelectedGroup(Number(e.target.value))}\n              label=\"المجموعة\"\n            >\n              <MenuItem value={0}>اختر المجموعة</MenuItem>\n              {groups.map((group) => (\n                <MenuItem key={group.id} value={group.id}>\n                  {group.group_name} - {group.teacher_name} ({group.student_count} طالب)\n                </MenuItem>\n              ))}\n            </Select>\n          </FormControl>\n        </CardContent>\n      </Card>\n\n      {/* Recipients Selection */}\n      {selectedGroup > 0 && (\n        <Card sx={{ mb: 3 }}>\n          <CardContent>\n            <Typography variant=\"h6\" gutterBottom>\n              اختيار المستقبلين\n            </Typography>\n            <FormGroup row>\n              <FormControlLabel\n                control={\n                  <Checkbox\n                    checked={sendToStudent}\n                    onChange={(e) => setSendToStudent(e.target.checked)}\n                  />\n                }\n                label=\"الطلاب\"\n              />\n              <FormControlLabel\n                control={\n                  <Checkbox\n                    checked={sendToFather}\n                    onChange={(e) => setSendToFather(e.target.checked)}\n                  />\n                }\n                label=\"أولياء الأمور (الآباء)\"\n              />\n              <FormControlLabel\n                control={\n                  <Checkbox\n                    checked={sendToMother}\n                    onChange={(e) => setSendToMother(e.target.checked)}\n                  />\n                }\n                label=\"أولياء الأمور (الأمهات)\"\n              />\n            </FormGroup>\n            \n            {validRecipients > 0 && (\n              <Alert severity=\"info\" sx={{ mt: 2 }}>\n                سيتم الإرسال إلى {validRecipients} مستقبل\n              </Alert>\n            )}\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Message Composition */}\n      {selectedGroup > 0 && (\n        <Card sx={{ mb: 3 }}>\n          <CardContent>\n            <Typography variant=\"h6\" gutterBottom>\n              كتابة الرسالة\n            </Typography>\n            <TextField\n              fullWidth\n              multiline\n              rows={6}\n              label=\"نص الرسالة\"\n              value={message}\n              onChange={(e) => setMessage(e.target.value)}\n              placeholder=\"اكتب رسالتك هنا...\"\n            />\n            \n            <Box mt={2} display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\">\n              <Typography variant=\"body2\" color=\"textSecondary\">\n                عدد الأحرف: {message.length}\n              </Typography>\n              \n              <Button\n                variant=\"contained\"\n                onClick={sendMessages}\n                disabled={!message.trim() || sending || validRecipients === 0}\n                startIcon={sending ? <CircularProgress size={20} /> : <SendIcon />}\n                size=\"large\"\n                color={whatsappStatus?.isReady ? 'primary' : 'warning'}\n              >\n                {sending ? 'جاري الإرسال...' : `إرسال إلى ${validRecipients} مستقبل`}\n              </Button>\n            </Box>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Students List */}\n      {selectedGroup > 0 && students.length > 0 && (\n        <Card>\n          <CardContent>\n            <Typography variant=\"h6\" gutterBottom>\n              قائمة الطلاب ({students.length})\n            </Typography>\n            <List>\n              {students.slice(0, 10).map((student) => (\n                <ListItem key={student.id}>\n                  <ListItemIcon>\n                    <PersonIcon />\n                  </ListItemIcon>\n                  <ListItemText\n                    primary={student.student_name}\n                    secondary={\n                      <React.Fragment>\n                        <Typography variant=\"body2\" component=\"div\">\n                          كود الطالب: {student.student_code}\n                        </Typography>\n                        <Box sx={{ mt: 0.5 }}>\n                          {student.student_phone && (\n                            <Chip size=\"small\" label={`طالب: ${student.student_phone}`} sx={{ mr: 1, mt: 0.5 }} />\n                          )}\n                          {student.father_phone && (\n                            <Chip size=\"small\" label={`والد: ${student.father_phone}`} sx={{ mr: 1, mt: 0.5 }} />\n                          )}\n                          {student.mother_phone && (\n                            <Chip size=\"small\" label={`والدة: ${student.mother_phone}`} sx={{ mr: 1, mt: 0.5 }} />\n                          )}\n                        </Box>\n                      </React.Fragment>\n                    }\n                  />\n                </ListItem>\n              ))}\n              {students.length > 10 && (\n                <ListItem>\n                  <ListItemText\n                    primary={`... و ${students.length - 10} طالب آخر`}\n                    sx={{ textAlign: 'center', fontStyle: 'italic' }}\n                  />\n                </ListItem>\n              )}\n            </List>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* QR Code Dialog */}\n      <Dialog open={qrDialogOpen} onClose={() => setQrDialogOpen(false)} maxWidth=\"sm\" fullWidth>\n        <DialogTitle>مسح QR Code للواتساب</DialogTitle>\n        <DialogContent>\n          {whatsappStatus?.qrCode ? (\n            <Box textAlign=\"center\">\n              <img \n                src={whatsappStatus.qrCode} \n                alt=\"WhatsApp QR Code\" \n                style={{ maxWidth: '100%', height: 'auto' }}\n              />\n              <Typography variant=\"body2\" color=\"textSecondary\" mt={2}>\n                امسح هذا الكود بكاميرا الواتساب على هاتفك\n              </Typography>\n            </Box>\n          ) : (\n            <Alert severity=\"info\">\n              لا يوجد QR Code متاح حالياً\n            </Alert>\n          )}\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setQrDialogOpen(false)}>إغلاق</Button>\n          <Button onClick={checkWhatsAppStatus} startIcon={<RefreshIcon />}>\n            تحديث\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n};\n\nexport default WhatsAppMessaging;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,SAAS,EACTC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,QAAQ,EACRC,gBAAgB,EAChBC,SAAS,EACTC,KAAK,EACLC,gBAAgB,EAEhBC,IAAI,EACJC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,QACR,eAAe;AACtB,SACEC,QAAQ,IAAIC,YAAY,EACxBC,IAAI,IAAIC,QAAQ,EAEhBC,MAAM,IAAIC,UAAU,EAEpBC,MAAM,IAAIC,UAAU,EACpBC,WAAW,IAAIC,eAAe,EAC9BC,KAAK,IAAIC,SAAS,EAClBC,OAAO,IAAIC,WAAW,QACjB,qBAAqB;AAC5B,SAASC,WAAW,QAAQ,WAAW;AACvC,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,cAAc,MAAM,iCAAiC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AA0B7D,MAAMC,iBAA2B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxC,MAAM;IAAEC;EAAgB,CAAC,GAAGP,WAAW,CAAC,CAAC;EAEzC,MAAM,CAACQ,MAAM,EAAEC,SAAS,CAAC,GAAGlD,QAAQ,CAAU,EAAE,CAAC;EACjD,MAAM,CAACmD,aAAa,EAAEC,gBAAgB,CAAC,GAAGpD,QAAQ,CAAS,CAAC,CAAC;EAC7D,MAAM,CAACqD,QAAQ,EAAEC,WAAW,CAAC,GAAGtD,QAAQ,CAAY,EAAE,CAAC;EACvD,MAAM,CAACuD,OAAO,EAAEC,UAAU,CAAC,GAAGxD,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACyD,OAAO,EAAEC,UAAU,CAAC,GAAG1D,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC2D,OAAO,EAAEC,UAAU,CAAC,GAAG5D,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC6D,cAAc,EAAEC,iBAAiB,CAAC,GAAG9D,QAAQ,CAAwB,IAAI,CAAC;EACjF,MAAM,CAAC+D,YAAY,EAAEC,eAAe,CAAC,GAAGhE,QAAQ,CAAC,KAAK,CAAC;;EAEvD;EACA,MAAM,CAACiE,aAAa,EAAEC,gBAAgB,CAAC,GAAGlE,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACmE,YAAY,EAAEC,eAAe,CAAC,GAAGpE,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAACqE,YAAY,EAAEC,eAAe,CAAC,GAAGtE,QAAQ,CAAC,KAAK,CAAC;EAEvDC,SAAS,CAAC,MAAM;IACdsE,UAAU,CAAC,CAAC;IACZC,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,EAAE,CAAC;EAENvE,SAAS,CAAC,MAAM;IACd,IAAIkD,aAAa,GAAG,CAAC,EAAE;MACrBsB,YAAY,CAAC,CAAC;IAChB;EACF,CAAC,EAAE,CAACtB,aAAa,CAAC,CAAC;EAEnB,MAAMoB,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF,MAAMG,aAAa,GAAGC,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC,IAAI,GAAG;MAClE,MAAMC,QAAQ,GAAG,MAAMnC,KAAK,CAACoC,GAAG,CAAC,wBAAwBJ,aAAa,YAAY,CAAC;MACnF,IAAIG,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzB9B,SAAS,CAAC2B,QAAQ,CAACE,IAAI,CAACA,IAAI,CAAC;MAC/B;IACF,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CjC,eAAe,CAAC,wBAAwB,EAAE;QAAEmC,OAAO,EAAE;MAAQ,CAAC,CAAC;IACjE;EACF,CAAC;EAED,MAAMV,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACFf,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMmB,QAAQ,GAAG,MAAMnC,KAAK,CAACoC,GAAG,CAAC,eAAe3B,aAAa,WAAW,CAAC;MACzE,IAAI0B,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzB1B,WAAW,CAACuB,QAAQ,CAACE,IAAI,CAACA,IAAI,CAAC;MACjC;IACF,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CjC,eAAe,CAAC,qBAAqB,EAAE;QAAEmC,OAAO,EAAE;MAAQ,CAAC,CAAC;IAC9D,CAAC,SAAS;MACRzB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMc,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,MAAMK,QAAQ,GAAG,MAAMnC,KAAK,CAACoC,GAAG,CAAC,sBAAsB,CAAC;MACxDhB,iBAAiB,CAACe,QAAQ,CAACE,IAAI,CAAC;IAClC,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;IACzD;EACF,CAAC;EAED,MAAMG,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACF1B,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMmB,QAAQ,GAAG,MAAMnC,KAAK,CAAC2C,IAAI,CAAC,0BAA0B,CAAC;MAC7D,IAAIR,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzBhC,eAAe,CAAC,yBAAyB,EAAE;UAAEmC,OAAO,EAAE;QAAU,CAAC,CAAC;QAClE;QACAG,UAAU,CAACd,mBAAmB,EAAE,IAAI,CAAC;MACvC;IACF,CAAC,CAAC,OAAOS,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpDjC,eAAe,CAAC,uBAAuB,EAAE;QAAEmC,OAAO,EAAE;MAAQ,CAAC,CAAC;IAChE,CAAC,SAAS;MACRzB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM6B,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACF7B,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMmB,QAAQ,GAAG,MAAMnC,KAAK,CAAC2C,IAAI,CAAC,0BAA0B,CAAC;MAC7D,IAAIR,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzBhC,eAAe,CAAC,sBAAsB,EAAE;UAAEmC,OAAO,EAAE;QAAU,CAAC,CAAC;QAC/DX,mBAAmB,CAAC,CAAC;MACvB;IACF,CAAC,CAAC,OAAOS,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrDjC,eAAe,CAAC,oBAAoB,EAAE;QAAEmC,OAAO,EAAE;MAAQ,CAAC,CAAC;IAC7D,CAAC,SAAS;MACRzB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM8B,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI,CAACrC,aAAa,IAAI,CAACI,OAAO,CAACkC,IAAI,CAAC,CAAC,EAAE;MACrCzC,eAAe,CAAC,qCAAqC,EAAE;QAAEmC,OAAO,EAAE;MAAU,CAAC,CAAC;MAC9E;IACF;IAEA,IAAI,EAACtB,cAAc,aAAdA,cAAc,eAAdA,cAAc,CAAE6B,OAAO,GAAE;MAC5B1C,eAAe,CAAC,gDAAgD,EAAE;QAAEmC,OAAO,EAAE;MAAU,CAAC,CAAC;IAC3F;IAEA,IAAI;MACFvB,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAM+B,UAA8D,GAAG,EAAE;MAEzEtC,QAAQ,CAACuC,OAAO,CAACC,OAAO,IAAI;QAC1B,IAAI5B,aAAa,IAAI4B,OAAO,CAACC,aAAa,EAAE;UAC1CH,UAAU,CAACI,IAAI,CAAC;YACdC,IAAI,EAAEH,OAAO,CAACI,YAAY;YAC1BC,KAAK,EAAEL,OAAO,CAACC,aAAa;YAC5BK,IAAI,EAAE;UACR,CAAC,CAAC;QACJ;QACA,IAAIhC,YAAY,IAAI0B,OAAO,CAACO,YAAY,EAAE;UACxCT,UAAU,CAACI,IAAI,CAAC;YACdC,IAAI,EAAE,QAAQH,OAAO,CAACI,YAAY,EAAE;YACpCC,KAAK,EAAEL,OAAO,CAACO,YAAY;YAC3BD,IAAI,EAAE;UACR,CAAC,CAAC;QACJ;QACA,IAAI9B,YAAY,IAAIwB,OAAO,CAACQ,YAAY,EAAE;UACxCV,UAAU,CAACI,IAAI,CAAC;YACdC,IAAI,EAAE,SAASH,OAAO,CAACI,YAAY,EAAE;YACrCC,KAAK,EAAEL,OAAO,CAACQ,YAAY;YAC3BF,IAAI,EAAE;UACR,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;MAEF,IAAIR,UAAU,CAACW,MAAM,KAAK,CAAC,EAAE;QAC3BtD,eAAe,CAAC,mCAAmC,EAAE;UAAEmC,OAAO,EAAE;QAAU,CAAC,CAAC;QAC5E;MACF;MAEA,MAAMN,QAAQ,GAAG,MAAMnC,KAAK,CAAC2C,IAAI,CAAC,yBAAyB,EAAE;QAC3DM,UAAU;QACVpC,OAAO;QACPgD,QAAQ,EAAEpD;MACZ,CAAC,CAAC;MAEF,IAAI0B,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzBhC,eAAe,CAAC,YAAY2C,UAAU,CAACW,MAAM,cAAc,EAAE;UAAEnB,OAAO,EAAE;QAAU,CAAC,CAAC;QACpF3B,UAAU,CAAC,EAAE,CAAC;MAChB;IACF,CAAC,CAAC,OAAOyB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CjC,eAAe,CAAC,sBAAsB,EAAE;QAAEmC,OAAO,EAAE;MAAQ,CAAC,CAAC;IAC/D,CAAC,SAAS;MACRvB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM4C,cAAc,GAAIC,MAAc,IAAK;IACzC,QAAQA,MAAM;MACZ,KAAK,OAAO;QAAE,OAAO,SAAS;MAC9B,KAAK,aAAa;QAAE,OAAO,SAAS;MACpC,KAAK,eAAe;QAAE,OAAO,MAAM;MACnC,KAAK,cAAc;QAAE,OAAO,SAAS;MACrC,KAAK,OAAO;QAAE,OAAO,OAAO;MAC5B;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,MAAMC,aAAa,GAAID,MAAc,IAAK;IACxC,QAAQA,MAAM;MACZ,KAAK,OAAO;QAAE,OAAO,aAAa;MAClC,KAAK,aAAa;QAAE,OAAO,uBAAuB;MAClD,KAAK,eAAe;QAAE,OAAO,YAAY;MACzC,KAAK,cAAc;QAAE,OAAO,UAAU;MACtC,KAAK,OAAO;QAAE,OAAO,gBAAgB;MACrC;QAAS,OAAO,WAAW;IAC7B;EACF,CAAC;EAED,MAAME,eAAe,GAAGtD,QAAQ,CAACuD,MAAM,CAACf,OAAO,IAC5C5B,aAAa,IAAI4B,OAAO,CAACC,aAAa,IACtC3B,YAAY,IAAI0B,OAAO,CAACO,YAAa,IACrC/B,YAAY,IAAIwB,OAAO,CAACQ,YAC3B,CAAC,CAACC,MAAM;EAER,oBACEzD,OAAA,CAAC3C,GAAG;IAAC2G,CAAC,EAAE,CAAE;IAAAC,QAAA,gBAERjE,OAAA,CAAC3C,GAAG;MAAC6G,OAAO,EAAC,MAAM;MAACC,UAAU,EAAC,QAAQ;MAACC,EAAE,EAAE,CAAE;MAAAH,QAAA,gBAC5CjE,OAAA,CAACjB,YAAY;QAACsF,EAAE,EAAE;UAAEC,EAAE,EAAE,CAAC;UAAEC,KAAK,EAAE,SAAS;UAAEC,QAAQ,EAAE;QAAG;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC/D5E,OAAA,CAACxC,UAAU;QAAC8E,OAAO,EAAC,IAAI;QAACuC,SAAS,EAAC,IAAI;QAAAZ,QAAA,EAAC;MAExC;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAGN5E,OAAA,CAACrC,IAAI;MAACmH,SAAS;MAACC,OAAO,EAAE,CAAE;MAACV,EAAE,EAAE;QAAED,EAAE,EAAE;MAAE,CAAE;MAAAH,QAAA,gBACxCjE,OAAA,CAACrC,IAAI;QAACqH,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAjB,QAAA,eACvBjE,OAAA,CAAC1C,IAAI;UAAA2G,QAAA,eACHjE,OAAA,CAACzC,WAAW;YAAA0G,QAAA,gBACVjE,OAAA,CAACxC,UAAU;cAAC8E,OAAO,EAAC,IAAI;cAAC6C,YAAY;cAAAlB,QAAA,EAAC;YAEtC;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb5E,OAAA,CAAC3C,GAAG;cAAC6G,OAAO,EAAC,MAAM;cAACC,UAAU,EAAC,QAAQ;cAACiB,GAAG,EAAE,CAAE;cAAChB,EAAE,EAAE,CAAE;cAAAH,QAAA,gBACpDjE,OAAA,CAAC3B,IAAI;gBACHgH,IAAI,EAAErE,cAAc,aAAdA,cAAc,eAAdA,cAAc,CAAE6B,OAAO,gBAAG7C,OAAA,CAACT,eAAe;kBAAAkF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAG5E,OAAA,CAACP,SAAS;kBAAAgF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACpEU,KAAK,EAAEtE,cAAc,GAAG6C,aAAa,CAAC7C,cAAc,CAAC4C,MAAM,CAAC,GAAG,gBAAiB;gBAChFW,KAAK,EAAEvD,cAAc,GAAG2C,cAAc,CAAC3C,cAAc,CAAC4C,MAAM,CAAC,GAAG;cAAU;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3E,CAAC,eACF5E,OAAA,CAACvC,MAAM;gBACL6E,OAAO,EAAC,UAAU;gBAClBiD,OAAO,EAAE5D,mBAAoB;gBAC7B6D,SAAS,eAAExF,OAAA,CAACL,WAAW;kBAAA8E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC3Ba,IAAI,EAAC,OAAO;gBAAAxB,QAAA,EACb;cAED;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAEN5E,OAAA,CAAC3C,GAAG;cAAC6G,OAAO,EAAC,MAAM;cAACkB,GAAG,EAAE,CAAE;cAAAnB,QAAA,GACxB,EAACjD,cAAc,aAAdA,cAAc,eAAdA,cAAc,CAAE6B,OAAO,kBACvB7C,OAAA,CAACvC,MAAM;gBACL6E,OAAO,EAAC,WAAW;gBACnBiD,OAAO,EAAEhD,kBAAmB;gBAC5BmD,QAAQ,EAAE9E,OAAQ;gBAClB4E,SAAS,EAAE5E,OAAO,gBAAGZ,OAAA,CAAC5B,gBAAgB;kBAACqH,IAAI,EAAE;gBAAG;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAG5E,OAAA,CAACjB,YAAY;kBAAA0F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAX,QAAA,EACxE;cAED;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CACT,EAEA,CAAA5D,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE2E,KAAK,kBACpB3F,OAAA,CAACvC,MAAM;gBACL6E,OAAO,EAAC,UAAU;gBAClBiD,OAAO,EAAEA,CAAA,KAAMpE,eAAe,CAAC,IAAI,CAAE;gBACrCqE,SAAS,eAAExF,OAAA,CAACX,UAAU;kBAAAoF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAX,QAAA,EAC3B;cAED;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CACT,EAEA,CAAA5D,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE6B,OAAO,kBACtB7C,OAAA,CAACvC,MAAM;gBACL6E,OAAO,EAAC,UAAU;gBAClBiC,KAAK,EAAC,OAAO;gBACbgB,OAAO,EAAE7C,kBAAmB;gBAC5BgD,QAAQ,EAAE9E,OAAQ;gBAAAqD,QAAA,EACnB;cAED;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CACT;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEP5E,OAAA,CAACrC,IAAI;QAACqH,IAAI;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAjB,QAAA,eACvBjE,OAAA,CAACF,cAAc;UAAA2E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGP5E,OAAA,CAAC1C,IAAI;MAAC+G,EAAE,EAAE;QAAED,EAAE,EAAE;MAAE,CAAE;MAAAH,QAAA,eAClBjE,OAAA,CAACzC,WAAW;QAAA0G,QAAA,gBACVjE,OAAA,CAACxC,UAAU;UAAC8E,OAAO,EAAC,IAAI;UAAC6C,YAAY;UAAAlB,QAAA,EAAC;QAEtC;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb5E,OAAA,CAACpC,WAAW;UAACgI,SAAS;UAAA3B,QAAA,gBACpBjE,OAAA,CAACnC,UAAU;YAAAoG,QAAA,EAAC;UAAQ;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACjC5E,OAAA,CAAClC,MAAM;YACL+H,KAAK,EAAEvF,aAAc;YACrBwF,QAAQ,EAAGC,CAAC,IAAKxF,gBAAgB,CAACyF,MAAM,CAACD,CAAC,CAACE,MAAM,CAACJ,KAAK,CAAC,CAAE;YAC1DP,KAAK,EAAC,kDAAU;YAAArB,QAAA,gBAEhBjE,OAAA,CAACjC,QAAQ;cAAC8H,KAAK,EAAE,CAAE;cAAA5B,QAAA,EAAC;YAAa;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,EAC3CxE,MAAM,CAAC8F,GAAG,CAAEC,KAAK,iBAChBnG,OAAA,CAACjC,QAAQ;cAAgB8H,KAAK,EAAEM,KAAK,CAACC,EAAG;cAAAnC,QAAA,GACtCkC,KAAK,CAACE,UAAU,EAAC,KAAG,EAACF,KAAK,CAACG,YAAY,EAAC,IAAE,EAACH,KAAK,CAACI,aAAa,EAAC,4BAClE;YAAA,GAFeJ,KAAK,CAACC,EAAE;cAAA3B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEb,CACX,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,EAGNtE,aAAa,GAAG,CAAC,iBAChBN,OAAA,CAAC1C,IAAI;MAAC+G,EAAE,EAAE;QAAED,EAAE,EAAE;MAAE,CAAE;MAAAH,QAAA,eAClBjE,OAAA,CAACzC,WAAW;QAAA0G,QAAA,gBACVjE,OAAA,CAACxC,UAAU;UAAC8E,OAAO,EAAC,IAAI;UAAC6C,YAAY;UAAAlB,QAAA,EAAC;QAEtC;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb5E,OAAA,CAAC9B,SAAS;UAACsI,GAAG;UAAAvC,QAAA,gBACZjE,OAAA,CAAC/B,gBAAgB;YACfwI,OAAO,eACLzG,OAAA,CAAChC,QAAQ;cACP0I,OAAO,EAAEtF,aAAc;cACvB0E,QAAQ,EAAGC,CAAC,IAAK1E,gBAAgB,CAAC0E,CAAC,CAACE,MAAM,CAACS,OAAO;YAAE;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CACF;YACDU,KAAK,EAAC;UAAQ;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC,eACF5E,OAAA,CAAC/B,gBAAgB;YACfwI,OAAO,eACLzG,OAAA,CAAChC,QAAQ;cACP0I,OAAO,EAAEpF,YAAa;cACtBwE,QAAQ,EAAGC,CAAC,IAAKxE,eAAe,CAACwE,CAAC,CAACE,MAAM,CAACS,OAAO;YAAE;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CACF;YACDU,KAAK,EAAC;UAAwB;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC,eACF5E,OAAA,CAAC/B,gBAAgB;YACfwI,OAAO,eACLzG,OAAA,CAAChC,QAAQ;cACP0I,OAAO,EAAElF,YAAa;cACtBsE,QAAQ,EAAGC,CAAC,IAAKtE,eAAe,CAACsE,CAAC,CAACE,MAAM,CAACS,OAAO;YAAE;cAAAjC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CACF;YACDU,KAAK,EAAC;UAAyB;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,EAEXd,eAAe,GAAG,CAAC,iBAClB9D,OAAA,CAAC7B,KAAK;UAACwI,QAAQ,EAAC,MAAM;UAACtC,EAAE,EAAE;YAAEuC,EAAE,EAAE;UAAE,CAAE;UAAA3C,QAAA,GAAC,yFACnB,EAACH,eAAe,EAAC,uCACpC;QAAA;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACU;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACP,EAGAtE,aAAa,GAAG,CAAC,iBAChBN,OAAA,CAAC1C,IAAI;MAAC+G,EAAE,EAAE;QAAED,EAAE,EAAE;MAAE,CAAE;MAAAH,QAAA,eAClBjE,OAAA,CAACzC,WAAW;QAAA0G,QAAA,gBACVjE,OAAA,CAACxC,UAAU;UAAC8E,OAAO,EAAC,IAAI;UAAC6C,YAAY;UAAAlB,QAAA,EAAC;QAEtC;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb5E,OAAA,CAACtC,SAAS;UACRkI,SAAS;UACTiB,SAAS;UACTC,IAAI,EAAE,CAAE;UACRxB,KAAK,EAAC,yDAAY;UAClBO,KAAK,EAAEnF,OAAQ;UACfoF,QAAQ,EAAGC,CAAC,IAAKpF,UAAU,CAACoF,CAAC,CAACE,MAAM,CAACJ,KAAK,CAAE;UAC5CkB,WAAW,EAAC;QAAoB;UAAAtC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC,eAEF5E,OAAA,CAAC3C,GAAG;UAACuJ,EAAE,EAAE,CAAE;UAAC1C,OAAO,EAAC,MAAM;UAAC8C,cAAc,EAAC,eAAe;UAAC7C,UAAU,EAAC,QAAQ;UAAAF,QAAA,gBAC3EjE,OAAA,CAACxC,UAAU;YAAC8E,OAAO,EAAC,OAAO;YAACiC,KAAK,EAAC,eAAe;YAAAN,QAAA,GAAC,2DACpC,EAACvD,OAAO,CAAC+C,MAAM;UAAA;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CAAC,eAEb5E,OAAA,CAACvC,MAAM;YACL6E,OAAO,EAAC,WAAW;YACnBiD,OAAO,EAAE5C,YAAa;YACtB+C,QAAQ,EAAE,CAAChF,OAAO,CAACkC,IAAI,CAAC,CAAC,IAAI9B,OAAO,IAAIgD,eAAe,KAAK,CAAE;YAC9D0B,SAAS,EAAE1E,OAAO,gBAAGd,OAAA,CAAC5B,gBAAgB;cAACqH,IAAI,EAAE;YAAG;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAG5E,OAAA,CAACf,QAAQ;cAAAwF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACnEa,IAAI,EAAC,OAAO;YACZlB,KAAK,EAAEvD,cAAc,aAAdA,cAAc,eAAdA,cAAc,CAAE6B,OAAO,GAAG,SAAS,GAAG,SAAU;YAAAoB,QAAA,EAEtDnD,OAAO,GAAG,iBAAiB,GAAG,aAAagD,eAAe;UAAS;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACP,EAGAtE,aAAa,GAAG,CAAC,IAAIE,QAAQ,CAACiD,MAAM,GAAG,CAAC,iBACvCzD,OAAA,CAAC1C,IAAI;MAAA2G,QAAA,eACHjE,OAAA,CAACzC,WAAW;QAAA0G,QAAA,gBACVjE,OAAA,CAACxC,UAAU;UAAC8E,OAAO,EAAC,IAAI;UAAC6C,YAAY;UAAAlB,QAAA,GAAC,uEACtB,EAACzD,QAAQ,CAACiD,MAAM,EAAC,GACjC;QAAA;UAAAgB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACb5E,OAAA,CAAC1B,IAAI;UAAA2F,QAAA,GACFzD,QAAQ,CAACyG,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAACf,GAAG,CAAElD,OAAO,iBACjChD,OAAA,CAACzB,QAAQ;YAAA0F,QAAA,gBACPjE,OAAA,CAACvB,YAAY;cAAAwF,QAAA,eACXjE,OAAA,CAACb,UAAU;gBAAAsF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACf5E,OAAA,CAACxB,YAAY;cACX0I,OAAO,EAAElE,OAAO,CAACI,YAAa;cAC9B+D,SAAS,eACPnH,OAAA,CAAC9C,KAAK,CAACkK,QAAQ;gBAAAnD,QAAA,gBACbjE,OAAA,CAACxC,UAAU;kBAAC8E,OAAO,EAAC,OAAO;kBAACuC,SAAS,EAAC,KAAK;kBAAAZ,QAAA,GAAC,2DAC9B,EAACjB,OAAO,CAACqE,YAAY;gBAAA;kBAAA5C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB,CAAC,eACb5E,OAAA,CAAC3C,GAAG;kBAACgH,EAAE,EAAE;oBAAEuC,EAAE,EAAE;kBAAI,CAAE;kBAAA3C,QAAA,GAClBjB,OAAO,CAACC,aAAa,iBACpBjD,OAAA,CAAC3B,IAAI;oBAACoH,IAAI,EAAC,OAAO;oBAACH,KAAK,EAAE,SAAStC,OAAO,CAACC,aAAa,EAAG;oBAACoB,EAAE,EAAE;sBAAEC,EAAE,EAAE,CAAC;sBAAEsC,EAAE,EAAE;oBAAI;kBAAE;oBAAAnC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CACtF,EACA5B,OAAO,CAACO,YAAY,iBACnBvD,OAAA,CAAC3B,IAAI;oBAACoH,IAAI,EAAC,OAAO;oBAACH,KAAK,EAAE,SAAStC,OAAO,CAACO,YAAY,EAAG;oBAACc,EAAE,EAAE;sBAAEC,EAAE,EAAE,CAAC;sBAAEsC,EAAE,EAAE;oBAAI;kBAAE;oBAAAnC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CACrF,EACA5B,OAAO,CAACQ,YAAY,iBACnBxD,OAAA,CAAC3B,IAAI;oBAACoH,IAAI,EAAC,OAAO;oBAACH,KAAK,EAAE,UAAUtC,OAAO,CAACQ,YAAY,EAAG;oBAACa,EAAE,EAAE;sBAAEC,EAAE,EAAE,CAAC;sBAAEsC,EAAE,EAAE;oBAAI;kBAAE;oBAAAnC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CACtF;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YACjB;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA,GAxBW5B,OAAO,CAACoD,EAAE;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAyBf,CACX,CAAC,EACDpE,QAAQ,CAACiD,MAAM,GAAG,EAAE,iBACnBzD,OAAA,CAACzB,QAAQ;YAAA0F,QAAA,eACPjE,OAAA,CAACxB,YAAY;cACX0I,OAAO,EAAE,SAAS1G,QAAQ,CAACiD,MAAM,GAAG,EAAE,WAAY;cAClDY,EAAE,EAAE;gBAAEiD,SAAS,EAAE,QAAQ;gBAAEC,SAAS,EAAE;cAAS;YAAE;cAAA9C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CACX;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CACP,eAGD5E,OAAA,CAACtB,MAAM;MAAC8I,IAAI,EAAEtG,YAAa;MAACuG,OAAO,EAAEA,CAAA,KAAMtG,eAAe,CAAC,KAAK,CAAE;MAACuG,QAAQ,EAAC,IAAI;MAAC9B,SAAS;MAAA3B,QAAA,gBACxFjE,OAAA,CAACrB,WAAW;QAAAsF,QAAA,EAAC;MAAoB;QAAAQ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAa,CAAC,eAC/C5E,OAAA,CAACpB,aAAa;QAAAqF,QAAA,EACXjD,cAAc,aAAdA,cAAc,eAAdA,cAAc,CAAE2G,MAAM,gBACrB3H,OAAA,CAAC3C,GAAG;UAACiK,SAAS,EAAC,QAAQ;UAAArD,QAAA,gBACrBjE,OAAA;YACE4H,GAAG,EAAE5G,cAAc,CAAC2G,MAAO;YAC3BE,GAAG,EAAC,kBAAkB;YACtBC,KAAK,EAAE;cAAEJ,QAAQ,EAAE,MAAM;cAAEK,MAAM,EAAE;YAAO;UAAE;YAAAtD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC,eACF5E,OAAA,CAACxC,UAAU;YAAC8E,OAAO,EAAC,OAAO;YAACiC,KAAK,EAAC,eAAe;YAACqC,EAAE,EAAE,CAAE;YAAA3C,QAAA,EAAC;UAEzD;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,gBAEN5E,OAAA,CAAC7B,KAAK;UAACwI,QAAQ,EAAC,MAAM;UAAA1C,QAAA,EAAC;QAEvB;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MACR;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACY,CAAC,eAChB5E,OAAA,CAACnB,aAAa;QAAAoF,QAAA,gBACZjE,OAAA,CAACvC,MAAM;UAAC8H,OAAO,EAAEA,CAAA,KAAMpE,eAAe,CAAC,KAAK,CAAE;UAAA8C,QAAA,EAAC;QAAK;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAC7D5E,OAAA,CAACvC,MAAM;UAAC8H,OAAO,EAAE5D,mBAAoB;UAAC6D,SAAS,eAAExF,OAAA,CAACL,WAAW;YAAA8E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAX,QAAA,EAAC;QAElE;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAAC1E,EAAA,CAncID,iBAA2B;EAAA,QACHL,WAAW;AAAA;AAAAoI,EAAA,GADnC/H,iBAA2B;AAqcjC,eAAeA,iBAAiB;AAAC,IAAA+H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}