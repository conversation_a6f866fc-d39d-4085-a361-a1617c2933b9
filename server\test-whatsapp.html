<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الواتساب</title>
    <style>
        body {
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            direction: rtl;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            background: #f9f9f9;
        }
        button {
            background: #25D366;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
            transition: all 0.3s;
        }
        button:hover {
            background: #128C7E;
            transform: translateY(-2px);
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        input, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            margin: 5px 0;
            font-size: 14px;
            box-sizing: border-box;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        .phone-input {
            font-family: monospace;
            direction: ltr;
            text-align: left;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 اختبار نظام الواتساب</h1>
        
        <!-- حالة الواتساب -->
        <div class="test-section">
            <h3>📊 حالة الواتساب</h3>
            <button onclick="checkStatus()">تحديث الحالة</button>
            <div id="status-result"></div>
        </div>

        <!-- اختبار سريع -->
        <div class="test-section">
            <h3>⚡ اختبار سريع</h3>
            <p>سيتم إرسال رسالة اختبار للرقم: <strong>201121381487</strong></p>
            <button onclick="quickTest()">إرسال رسالة اختبار</button>
            <div id="quick-test-result"></div>
        </div>

        <!-- اختبار مخصص -->
        <div class="test-section">
            <h3>🎯 اختبار مخصص</h3>
            <input type="text" id="custom-phone" placeholder="رقم الهاتف (مثال: 201234567890)" class="phone-input">
            <textarea id="custom-message" placeholder="اكتب رسالتك هنا...">مرحباً! هذه رسالة اختبار من نظام إدارة المراكز التعليمية 🎓</textarea>
            <button onclick="customTest()">إرسال رسالة مخصصة</button>
            <div id="custom-test-result"></div>
        </div>

        <!-- تهيئة الواتساب -->
        <div class="test-section">
            <h3>🔧 إدارة الواتساب</h3>
            <button onclick="initializeWhatsApp()">تشغيل الواتساب</button>
            <button onclick="disconnectWhatsApp()">قطع الاتصال</button>
            <div id="management-result"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:7080/api';

        async function makeRequest(url, method = 'GET', data = null) {
            try {
                const options = {
                    method,
                    headers: {
                        'Content-Type': 'application/json',
                    }
                };
                
                if (data) {
                    options.body = JSON.stringify(data);
                }
                
                const response = await fetch(url, options);
                const result = await response.json();
                
                return { success: response.ok, data: result };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        function showResult(elementId, result, message = '') {
            const element = document.getElementById(elementId);
            let className = result.success ? 'success' : 'error';
            let content = message || (result.success ? 'نجح!' : 'فشل!');
            
            if (result.data) {
                content += '<br><pre>' + JSON.stringify(result.data, null, 2) + '</pre>';
            }
            if (result.error) {
                content += '<br>خطأ: ' + result.error;
            }
            
            element.innerHTML = `<div class="status ${className}">${content}</div>`;
        }

        async function checkStatus() {
            const result = await makeRequest(`${API_BASE}/whatsapp/status`);
            showResult('status-result', result);
        }

        async function quickTest() {
            const result = await makeRequest(`${API_BASE}/whatsapp/quick-test`, 'POST');
            showResult('quick-test-result', result);
        }

        async function customTest() {
            const phone = document.getElementById('custom-phone').value;
            const message = document.getElementById('custom-message').value;
            
            if (!phone || !message) {
                showResult('custom-test-result', { success: false }, 'يرجى إدخال رقم الهاتف والرسالة');
                return;
            }
            
            const result = await makeRequest(`${API_BASE}/whatsapp/send-real`, 'POST', {
                phone_number: phone,
                message: message
            });
            showResult('custom-test-result', result);
        }

        async function initializeWhatsApp() {
            const result = await makeRequest(`${API_BASE}/whatsapp/initialize`, 'POST');
            showResult('management-result', result);
        }

        async function disconnectWhatsApp() {
            const result = await makeRequest(`${API_BASE}/whatsapp/disconnect`, 'POST');
            showResult('management-result', result);
        }

        // تحديث الحالة تلقائياً عند تحميل الصفحة
        window.onload = function() {
            checkStatus();
        };
    </script>
</body>
</html>
