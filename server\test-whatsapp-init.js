// اختبار تهيئة خدمة الواتساب
const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 7080;

// Middleware
app.use(cors());
app.use(express.json());

// Test WhatsApp service loading
let whatsappService;
let whatsappError = null;

try {
    console.log('🔄 Loading WhatsApp service...');
    const WhatsAppService = require('./services/whatsappService');
    whatsappService = new WhatsAppService();
    console.log('✅ WhatsApp service loaded successfully');
} catch (error) {
    console.error('❌ Error loading WhatsApp service:', error.message);
    whatsappError = error.message;
    
    // Create mock service
    whatsappService = {
        getStatus: () => ({
            status: 'error',
            isReady: false,
            qrCode: null,
            qrCodeDataURL: null,
            sessionInfo: null,
            lastActivity: null,
            messageQueueLength: 0,
            isProcessingQueue: false,
            reconnectAttempts: 0,
            error: `Service loading error: ${whatsappError}`
        }),
        initialize: () => Promise.reject(new Error(`Service not loaded: ${whatsappError}`)),
        disconnect: () => Promise.reject(new Error(`Service not loaded: ${whatsappError}`)),
        sendMessage: () => Promise.reject(new Error(`Service not loaded: ${whatsappError}`)),
        sendBulkMessages: () => Promise.reject(new Error(`Service not loaded: ${whatsappError}`))
    };
}

// Health check
app.get('/api/health', (req, res) => {
    res.json({
        status: 'OK',
        message: 'Test server is running',
        timestamp: new Date().toISOString(),
        whatsappServiceLoaded: !whatsappError,
        whatsappError: whatsappError
    });
});

// WhatsApp status
app.get('/api/whatsapp/status', (req, res) => {
    try {
        const status = whatsappService.getStatus();
        res.json({
            success: true,
            data: status
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// WhatsApp initialize
app.post('/api/whatsapp/initialize', async (req, res) => {
    try {
        console.log('🔄 Initializing WhatsApp...');
        const result = await whatsappService.initialize();
        res.json({
            success: true,
            message: 'WhatsApp initialized successfully',
            data: result
        });
    } catch (error) {
        console.error('❌ Error initializing WhatsApp:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// WhatsApp disconnect
app.post('/api/whatsapp/disconnect', async (req, res) => {
    try {
        console.log('🔌 Disconnecting WhatsApp...');
        const result = await whatsappService.disconnect();
        res.json({
            success: true,
            message: 'WhatsApp disconnected successfully',
            data: result
        });
    } catch (error) {
        console.error('❌ Error disconnecting WhatsApp:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Send bulk messages
app.post('/api/whatsapp/send-bulk', async (req, res) => {
    try {
        const { recipients, message, group_id } = req.body;
        
        if (!message || !message.trim()) {
            return res.status(400).json({
                success: false,
                error: 'Message is required'
            });
        }

        // Check if WhatsApp is ready
        const status = whatsappService.getStatus();
        if (!status.isReady) {
            return res.status(400).json({
                success: false,
                error: 'WhatsApp is not ready. Please initialize first.',
                currentStatus: status.status
            });
        }

        console.log(`📤 Sending bulk messages to ${recipients?.length || 0} recipients`);
        
        // For now, just return success (you can implement actual sending later)
        res.json({
            success: true,
            message: `Messages queued for ${recipients?.length || 0} recipients`,
            data: {
                queued: recipients?.length || 0,
                message: message.substring(0, 50) + '...'
            }
        });
        
    } catch (error) {
        console.error('❌ Error sending bulk messages:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// Start server
app.listen(PORT, () => {
    console.log('🎉 ================================');
    console.log(`🚀 TEST WHATSAPP SERVER RUNNING on http://localhost:${PORT}`);
    console.log(`📊 Health check: http://localhost:${PORT}/api/health`);
    console.log(`📱 WhatsApp status: http://localhost:${PORT}/api/whatsapp/status`);
    console.log(`✅ WhatsApp service: ${whatsappError ? '❌ Error' : '✅ Loaded'}`);
    if (whatsappError) {
        console.log(`❌ Error: ${whatsappError}`);
    }
    console.log('🎉 ================================');
});

// Handle process termination
process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down server...');
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\n🛑 Shutting down server...');
    process.exit(0);
});
