import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Ty<PERSON>graphy,
  Button,
  Grid,
  Alert,
  CircularProgress,
  Divider,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Paper,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  WhatsApp as WhatsAppIcon,
  QrCode as QrCodeIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Refresh as RefreshIcon,
  OpenInNew as OpenInNewIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  Phone as PhoneIcon,
  Message as MessageIcon,
  Schedule as ScheduleIcon,
  Warning as WarningIcon,
} from '@mui/icons-material';
import { useSnackbar } from 'notistack';
import axios from 'axios';

interface WhatsAppStatus {
  status: 'disconnected' | 'qr_received' | 'authenticated' | 'ready' | 'error';
  isReady: boolean;
  qrCode?: string;
  qrCodeDataURL?: string;
  sessionInfo?: {
    hasSession: boolean;
    clientId: string;
  };
  lastActivity?: string;
  messageQueueLength: number;
  isProcessingQueue: boolean;
  reconnectAttempts: number;
  error?: string;
}

const WhatsAppDashboardEnhanced: React.FC = () => {
  const { enqueueSnackbar } = useSnackbar();
  
  const [status, setStatus] = useState<WhatsAppStatus | null>(null);
  const [loading, setLoading] = useState(false);
  const [qrDialogOpen, setQrDialogOpen] = useState(false);
  const [webViewOpen, setWebViewOpen] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState(true);

  // Check WhatsApp status
  const checkStatus = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/api/whatsapp/status');
      if (response.data.success) {
        setStatus(response.data.data);
      }
    } catch (error) {
      console.error('Error checking WhatsApp status:', error);
      enqueueSnackbar('خطأ في التحقق من حالة الواتساب', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  // Initialize WhatsApp
  const initializeWhatsApp = async () => {
    try {
      setLoading(true);
      const response = await axios.post('/api/whatsapp/initialize');
      if (response.data.success) {
        enqueueSnackbar('تم بدء تشغيل الواتساب بنجاح', { variant: 'success' });
        await checkStatus();
      }
    } catch (error) {
      console.error('Error initializing WhatsApp:', error);
      enqueueSnackbar('خطأ في تشغيل الواتساب', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  // Disconnect WhatsApp
  const disconnectWhatsApp = async () => {
    try {
      setLoading(true);
      const response = await axios.post('/api/whatsapp/disconnect');
      if (response.data.success) {
        enqueueSnackbar('تم قطع الاتصال بنجاح', { variant: 'success' });
        await checkStatus();
      }
    } catch (error) {
      console.error('Error disconnecting WhatsApp:', error);
      enqueueSnackbar('خطأ في قطع الاتصال', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  // Get status color and text
  const getStatusInfo = () => {
    if (!status) return { color: 'default', text: 'غير معروف', icon: <WarningIcon /> };

    switch (status.status) {
      case 'ready':
        return { color: 'success', text: 'متصل ومستعد', icon: <CheckCircleIcon /> };
      case 'authenticated':
        return { color: 'info', text: 'مصادق عليه', icon: <ScheduleIcon /> };
      case 'qr_received':
        return { color: 'warning', text: 'انتظار مسح الكود', icon: <QrCodeIcon /> };
      case 'disconnected':
        return { color: 'default', text: 'غير متصل', icon: <ErrorIcon /> };
      case 'error':
        return { color: 'error', text: 'خطأ في الاتصال', icon: <ErrorIcon /> };
      default:
        return { color: 'default', text: 'غير معروف', icon: <WarningIcon /> };
    }
  };

  // Auto-refresh status
  useEffect(() => {
    checkStatus();
    
    if (autoRefresh) {
      const interval = setInterval(checkStatus, 5000);
      return () => clearInterval(interval);
    }
  }, [autoRefresh]);

  const statusInfo = getStatusInfo();

  return (
    <Box p={3}>
      {/* Header */}
      <Box display="flex" alignItems="center" justifyContent="space-between" mb={3}>
        <Box display="flex" alignItems="center">
          <WhatsAppIcon sx={{ mr: 2, color: '#25D366', fontSize: 40 }} />
          <Typography variant="h4" component="h1">
            لوحة تحكم الواتساب المحسنة
          </Typography>
        </Box>
        
        <Box display="flex" gap={1}>
          <Tooltip title={autoRefresh ? 'إيقاف التحديث التلقائي' : 'تشغيل التحديث التلقائي'}>
            <IconButton 
              onClick={() => setAutoRefresh(!autoRefresh)}
              color={autoRefresh ? 'primary' : 'default'}
            >
              <RefreshIcon />
            </IconButton>
          </Tooltip>
          
          <Button
            variant="outlined"
            onClick={checkStatus}
            disabled={loading}
            startIcon={loading ? <CircularProgress size={20} /> : <RefreshIcon />}
          >
            تحديث الحالة
          </Button>
        </Box>
      </Box>

      <Grid container spacing={3}>
        {/* Status Card */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                حالة الاتصال
              </Typography>
              
              <Box display="flex" alignItems="center" gap={2} mb={2}>
                <Chip
                  icon={statusInfo.icon}
                  label={statusInfo.text}
                  color={statusInfo.color as any}
                  size="medium"
                />
                
                {status?.isReady && (
                  <Chip
                    icon={<CheckCircleIcon />}
                    label="جاهز للإرسال"
                    color="success"
                    variant="outlined"
                  />
                )}
              </Box>

              {/* Status Details */}
              <Grid container spacing={2} sx={{ mt: 1 }}>
                <Grid item xs={6}>
                  <Typography variant="body2" color="textSecondary">
                    طوابير الرسائل
                  </Typography>
                  <Typography variant="h6">
                    {status?.messageQueueLength || 0}
                  </Typography>
                </Grid>
                
                <Grid item xs={6}>
                  <Typography variant="body2" color="textSecondary">
                    محاولات الاتصال
                  </Typography>
                  <Typography variant="h6">
                    {status?.reconnectAttempts || 0}
                  </Typography>
                </Grid>
              </Grid>

              {status?.lastActivity && (
                <Typography variant="caption" color="textSecondary" sx={{ mt: 2, display: 'block' }}>
                  آخر نشاط: {new Date(status.lastActivity).toLocaleString('ar-EG')}
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Control Panel */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                لوحة التحكم
              </Typography>
              
              <Box display="flex" flexDirection="column" gap={2}>
                {!status?.isReady && (
                  <Button
                    variant="contained"
                    onClick={initializeWhatsApp}
                    disabled={loading}
                    startIcon={loading ? <CircularProgress size={20} /> : <WhatsAppIcon />}
                    fullWidth
                  >
                    تشغيل الواتساب
                  </Button>
                )}
                
                {status?.qrCode && (
                  <Button
                    variant="outlined"
                    onClick={() => setQrDialogOpen(true)}
                    startIcon={<QrCodeIcon />}
                    fullWidth
                  >
                    عرض QR Code
                  </Button>
                )}
                
                <Button
                  variant="outlined"
                  onClick={() => setWebViewOpen(!webViewOpen)}
                  startIcon={webViewOpen ? <VisibilityOffIcon /> : <VisibilityIcon />}
                  fullWidth
                >
                  {webViewOpen ? 'إخفاء' : 'عرض'} الواتساب
                </Button>
                
                <Button
                  variant="outlined"
                  onClick={() => window.open('https://web.whatsapp.com', '_blank')}
                  startIcon={<OpenInNewIcon />}
                  fullWidth
                >
                  فتح في نافذة جديدة
                </Button>
                
                {status?.isReady && (
                  <Button
                    variant="outlined"
                    color="error"
                    onClick={disconnectWhatsApp}
                    disabled={loading}
                    fullWidth
                  >
                    قطع الاتصال
                  </Button>
                )}
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* WhatsApp Web Viewer */}
        {webViewOpen && (
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" justifyContent="between" mb={2}>
                  <Typography variant="h6">
                    عارض الواتساب
                  </Typography>
                  <IconButton onClick={() => setWebViewOpen(false)}>
                    <VisibilityOffIcon />
                  </IconButton>
                </Box>
                
                <Paper elevation={2} sx={{ overflow: 'hidden' }}>
                  <iframe
                    src="https://web.whatsapp.com"
                    width="100%"
                    height="600px"
                    title="WhatsApp Web"
                    style={{ border: 'none' }}
                  />
                </Paper>
              </CardContent>
            </Card>
          </Grid>
        )}

        {/* Error Display */}
        {status?.error && (
          <Grid item xs={12}>
            <Alert severity="error" sx={{ mb: 2 }}>
              <strong>خطأ:</strong> {status.error}
            </Alert>
          </Grid>
        )}
      </Grid>

      {/* QR Code Dialog */}
      <Dialog open={qrDialogOpen} onClose={() => setQrDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>مسح QR Code للواتساب</DialogTitle>
        <DialogContent>
          {status?.qrCode ? (
            <Box textAlign="center">
              <img 
                src={status.qrCode} 
                alt="WhatsApp QR Code" 
                style={{ maxWidth: '100%', height: 'auto' }}
              />
              <Typography variant="body2" color="textSecondary" mt={2}>
                امسح هذا الكود بكاميرا الواتساب على هاتفك
              </Typography>
            </Box>
          ) : (
            <Alert severity="info">
              لا يوجد QR Code متاح حالياً
            </Alert>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setQrDialogOpen(false)}>إغلاق</Button>
          <Button onClick={checkStatus} startIcon={<RefreshIcon />}>
            تحديث
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default WhatsAppDashboardEnhanced;
