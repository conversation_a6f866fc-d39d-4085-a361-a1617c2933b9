{"ast": null, "code": "var _jsxFileName = \"I:\\\\\\u0628\\u0631\\u0645\\u062C\\u0629\\\\\\u062A\\u0639\\u0644\\u064A\\u0645\\u064A ali2 2\\\\\\u062A\\u0639\\u0644\\u064A\\u0645\\u064A 2\\\\\\u0627\\u062F\\u0627\\u0631\\u0629 \\u0645\\u0631\\u0627\\u0643\\u0632 \\u062A\\u0639\\u0644\\u064A\\u0645\\u064A\\u0629\\\\edu 2\\\\client\\\\src\\\\components\\\\WhatsAppViewer.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, CardContent, CardHeader, CardTitle } from './ui/card';\nimport { Button } from './ui/button';\nimport { Badge } from './ui/badge';\nimport { Separator } from './ui/separator';\nimport { WhatsApp as MessageCircle } from '@mui/icons-material';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst WhatsAppViewer = ({\n  className\n}) => {\n  _s();\n  const [status, setStatus] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [showWebView, setShowWebView] = useState(false);\n\n  // Check WhatsApp status\n  const checkStatus = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get('/api/whatsapp/status');\n      if (response.data.success) {\n        setStatus(response.data.data);\n      }\n    } catch (error) {\n      console.error('Error checking WhatsApp status:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Initialize WhatsApp\n  const initializeWhatsApp = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.post('/api/whatsapp/initialize');\n      if (response.data.success) {\n        await checkStatus();\n      }\n    } catch (error) {\n      console.error('Error initializing WhatsApp:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Disconnect WhatsApp\n  const disconnectWhatsApp = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.post('/api/whatsapp/disconnect');\n      if (response.data.success) {\n        await checkStatus();\n      }\n    } catch (error) {\n      console.error('Error disconnecting WhatsApp:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Get status badge\n  const getStatusBadge = () => {\n    if (!status) return /*#__PURE__*/_jsxDEV(Badge, {\n      variant: \"secondary\",\n      children: \"\\u063A\\u064A\\u0631 \\u0645\\u0639\\u0631\\u0648\\u0641\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 29\n    }, this);\n    switch (status.status) {\n      case 'ready':\n        return /*#__PURE__*/_jsxDEV(Badge, {\n          variant: \"default\",\n          children: \"\\u0645\\u062A\\u0635\\u0644\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 24\n        }, this);\n      case 'authenticated':\n        return /*#__PURE__*/_jsxDEV(Badge, {\n          variant: \"default\",\n          children: \"\\u0645\\u0635\\u0627\\u062F\\u0642 \\u0639\\u0644\\u064A\\u0647\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 24\n        }, this);\n      case 'qr_received':\n        return /*#__PURE__*/_jsxDEV(Badge, {\n          variant: \"default\",\n          children: \"\\u0627\\u0646\\u062A\\u0638\\u0627\\u0631 \\u0627\\u0644\\u0645\\u0633\\u062D\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 24\n        }, this);\n      case 'disconnected':\n        return /*#__PURE__*/_jsxDEV(Badge, {\n          variant: \"secondary\",\n          children: \"\\u063A\\u064A\\u0631 \\u0645\\u062A\\u0635\\u0644\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 24\n        }, this);\n      case 'error':\n        return /*#__PURE__*/_jsxDEV(Badge, {\n          variant: \"destructive\",\n          children: \"\\u062E\\u0637\\u0623\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 24\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(Badge, {\n          variant: \"secondary\",\n          children: \"\\u063A\\u064A\\u0631 \\u0645\\u0639\\u0631\\u0648\\u0641\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 24\n        }, this);\n    }\n  };\n\n  // Auto-refresh status\n  useEffect(() => {\n    checkStatus();\n    const interval = setInterval(checkStatus, 5000); // Check every 5 seconds\n    return () => clearInterval(interval);\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: className,\n    children: /*#__PURE__*/_jsxDEV(Card, {\n      children: [/*#__PURE__*/_jsxDEV(CardHeader, {\n        children: /*#__PURE__*/_jsxDEV(CardTitle, {\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '8px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(MessageCircle, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 29\n            }, this), \"\\u062D\\u0627\\u0644\\u0629 \\u0627\\u0644\\u0648\\u0627\\u062A\\u0633\\u0627\\u0628\", getStatusBadge()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 25\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n        className: \"space-y-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-2 gap-4 text-sm\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-medium\",\n              children: \"\\u0627\\u0644\\u062D\\u0627\\u0644\\u0629:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"mr-2\",\n              children: (status === null || status === void 0 ? void 0 : status.status) || 'غير معروف'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 130,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-medium\",\n              children: \"\\u062C\\u0627\\u0647\\u0632:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"mr-2\",\n              children: status !== null && status !== void 0 && status.isReady ? 'نعم' : 'لا'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-medium\",\n              children: \"\\u0637\\u0648\\u0627\\u0628\\u064A\\u0631 \\u0627\\u0644\\u0631\\u0633\\u0627\\u0626\\u0644:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"mr-2\",\n              children: (status === null || status === void 0 ? void 0 : status.messageQueueLength) || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-medium\",\n              children: \"\\u0645\\u062D\\u0627\\u0648\\u0644\\u0627\\u062A \\u0627\\u0644\\u0627\\u062A\\u0635\\u0627\\u0644:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"mr-2\",\n              children: (status === null || status === void 0 ? void 0 : status.reconnectAttempts) || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(Separator, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex gap-2 flex-wrap\",\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            onClick: initializeWhatsApp,\n            disabled: loading || (status === null || status === void 0 ? void 0 : status.status) === 'ready',\n            size: \"sm\",\n            children: \"\\u062A\\u0634\\u063A\\u064A\\u0644 \\u0627\\u0644\\u0648\\u0627\\u062A\\u0633\\u0627\\u0628\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: disconnectWhatsApp,\n            disabled: loading || (status === null || status === void 0 ? void 0 : status.status) === 'disconnected',\n            variant: \"outline\",\n            size: \"sm\",\n            children: \"\\u0642\\u0637\\u0639 \\u0627\\u0644\\u0627\\u062A\\u0635\\u0627\\u0644\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: checkStatus,\n            disabled: loading,\n            variant: \"outline\",\n            size: \"sm\",\n            children: \"\\u062A\\u062D\\u062F\\u064A\\u062B\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: () => setShowWebView(!showWebView),\n            variant: \"outline\",\n            size: \"sm\",\n            children: [showWebView ? 'إخفاء' : 'عرض', \" \\u0627\\u0644\\u0648\\u0627\\u062A\\u0633\\u0627\\u0628\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: () => window.open('https://web.whatsapp.com', '_blank'),\n            variant: \"outline\",\n            size: \"sm\",\n            children: \"\\u0641\\u062A\\u062D \\u0641\\u064A \\u0646\\u0627\\u0641\\u0630\\u0629 \\u062C\\u062F\\u064A\\u062F\\u0629\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 21\n        }, this), (status === null || status === void 0 ? void 0 : status.qrCodeDataURL) && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-muted-foreground mb-2\",\n            children: \"\\u0627\\u0645\\u0633\\u062D \\u0627\\u0644\\u0643\\u0648\\u062F \\u0628\\u0647\\u0627\\u062A\\u0641\\u0643:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n            src: status.qrCodeDataURL,\n            alt: \"WhatsApp QR Code\",\n            className: \"mx-auto border rounded\",\n            style: {\n              maxWidth: '200px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 25\n        }, this), (status === null || status === void 0 ? void 0 : status.error) && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-3 bg-red-50 border border-red-200 rounded text-red-700 text-sm\",\n          children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n            children: \"\\u062E\\u0637\\u0623:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 29\n          }, this), \" \", status.error]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 25\n        }, this), showWebView && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"border rounded-lg overflow-hidden\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-gray-100 p-2 text-sm font-medium flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"\\u0648\\u0627\\u062A\\u0633\\u0627\\u0628 \\u0648\\u064A\\u0628\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 33\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              onClick: () => setShowWebView(false),\n              variant: \"ghost\",\n              size: \"sm\",\n              children: \"\\u2715\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 33\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 29\n          }, this), /*#__PURE__*/_jsxDEV(\"iframe\", {\n            src: \"https://web.whatsapp.com\",\n            className: \"w-full\",\n            style: {\n              height: '600px'\n            },\n            title: \"WhatsApp Web\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 29\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 25\n        }, this), (status === null || status === void 0 ? void 0 : status.lastActivity) && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-xs text-muted-foreground\",\n          children: [\"\\u0622\\u062E\\u0631 \\u0646\\u0634\\u0627\\u0637: \", new Date(status.lastActivity).toLocaleString('ar-EG')]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 13\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 116,\n    columnNumber: 9\n  }, this);\n};\n_s(WhatsAppViewer, \"Vmli3u8BgAtfftB6vI6VovmLzrg=\");\n_c = WhatsAppViewer;\nexport default WhatsAppViewer;\nvar _c;\n$RefreshReg$(_c, \"WhatsAppViewer\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "CardTitle", "<PERSON><PERSON>", "Badge", "Separator", "WhatsApp", "MessageCircle", "axios", "jsxDEV", "_jsxDEV", "WhatsAppViewer", "className", "_s", "status", "setStatus", "loading", "setLoading", "showWebView", "setShowWebView", "checkStatus", "response", "get", "data", "success", "error", "console", "initializeWhatsApp", "post", "disconnectWhatsApp", "getStatusBadge", "variant", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "interval", "setInterval", "clearInterval", "style", "display", "alignItems", "gap", "isReady", "message<PERSON><PERSON>ue<PERSON>ength", "reconnectAttempts", "onClick", "disabled", "size", "window", "open", "qrCodeDataURL", "src", "alt", "max<PERSON><PERSON><PERSON>", "height", "title", "lastActivity", "Date", "toLocaleString", "_c", "$RefreshReg$"], "sources": ["I:/برمجة/تعليمي ali2 2/تعليمي 2/ادارة مراكز تعليمية/edu 2/client/src/components/WhatsAppViewer.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Card, CardContent, CardHeader, CardTitle } from './ui/card';\nimport { Button } from './ui/button';\nimport { Badge } from './ui/badge';\nimport { Separator } from './ui/separator';\nimport {\n    WhatsApp as MessageCircle,\n    Language as Globe,\n    Refresh as RefreshCw,\n    OpenInNew as ExternalLink,\n    CheckCircle,\n    Cancel as XCircle,\n    Schedule as Clock,\n    Warning as AlertCircle\n} from '@mui/icons-material';\nimport axios from 'axios';\n\ninterface WhatsAppViewerProps {\n    className?: string;\n}\n\ninterface WhatsAppStatus {\n    status: 'disconnected' | 'qr_received' | 'authenticated' | 'ready' | 'error';\n    isReady: boolean;\n    qrCode?: string;\n    qrCodeDataURL?: string;\n    sessionInfo?: {\n        hasSession: boolean;\n        clientId: string;\n    };\n    lastActivity?: string;\n    messageQueueLength: number;\n    isProcessingQueue: boolean;\n    reconnectAttempts: number;\n    error?: string;\n}\n\nconst WhatsAppViewer: React.FC<WhatsAppViewerProps> = ({ className }) => {\n    const [status, setStatus] = useState<WhatsAppStatus | null>(null);\n    const [loading, setLoading] = useState(false);\n    const [showWebView, setShowWebView] = useState(false);\n\n    // Check WhatsApp status\n    const checkStatus = async () => {\n        try {\n            setLoading(true);\n            const response = await axios.get('/api/whatsapp/status');\n            if (response.data.success) {\n                setStatus(response.data.data);\n            }\n        } catch (error) {\n            console.error('Error checking WhatsApp status:', error);\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    // Initialize WhatsApp\n    const initializeWhatsApp = async () => {\n        try {\n            setLoading(true);\n            const response = await axios.post('/api/whatsapp/initialize');\n            if (response.data.success) {\n                await checkStatus();\n            }\n        } catch (error) {\n            console.error('Error initializing WhatsApp:', error);\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    // Disconnect WhatsApp\n    const disconnectWhatsApp = async () => {\n        try {\n            setLoading(true);\n            const response = await axios.post('/api/whatsapp/disconnect');\n            if (response.data.success) {\n                await checkStatus();\n            }\n        } catch (error) {\n            console.error('Error disconnecting WhatsApp:', error);\n        } finally {\n            setLoading(false);\n        }\n    };\n\n    // Get status badge\n    const getStatusBadge = () => {\n        if (!status) return <Badge variant=\"secondary\">غير معروف</Badge>;\n\n        switch (status.status) {\n            case 'ready':\n                return <Badge variant=\"default\">متصل</Badge>;\n            case 'authenticated':\n                return <Badge variant=\"default\">مصادق عليه</Badge>;\n            case 'qr_received':\n                return <Badge variant=\"default\">انتظار المسح</Badge>;\n            case 'disconnected':\n                return <Badge variant=\"secondary\">غير متصل</Badge>;\n            case 'error':\n                return <Badge variant=\"destructive\">خطأ</Badge>;\n            default:\n                return <Badge variant=\"secondary\">غير معروف</Badge>;\n        }\n    };\n\n    // Auto-refresh status\n    useEffect(() => {\n        checkStatus();\n        const interval = setInterval(checkStatus, 5000); // Check every 5 seconds\n        return () => clearInterval(interval);\n    }, []);\n\n    return (\n        <div className={className}>\n            <Card>\n                <CardHeader>\n                    <CardTitle>\n                        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>\n                            <MessageCircle />\n                            حالة الواتساب\n                            {getStatusBadge()}\n                        </div>\n                    </CardTitle>\n                </CardHeader>\n                <CardContent className=\"space-y-4\">\n                    {/* Status Information */}\n                    <div className=\"grid grid-cols-2 gap-4 text-sm\">\n                        <div>\n                            <span className=\"font-medium\">الحالة:</span>\n                            <span className=\"mr-2\">{status?.status || 'غير معروف'}</span>\n                        </div>\n                        <div>\n                            <span className=\"font-medium\">جاهز:</span>\n                            <span className=\"mr-2\">{status?.isReady ? 'نعم' : 'لا'}</span>\n                        </div>\n                        <div>\n                            <span className=\"font-medium\">طوابير الرسائل:</span>\n                            <span className=\"mr-2\">{status?.messageQueueLength || 0}</span>\n                        </div>\n                        <div>\n                            <span className=\"font-medium\">محاولات الاتصال:</span>\n                            <span className=\"mr-2\">{status?.reconnectAttempts || 0}</span>\n                        </div>\n                    </div>\n\n                    <Separator />\n\n                    {/* Action Buttons */}\n                    <div className=\"flex gap-2 flex-wrap\">\n                        <Button\n                            onClick={initializeWhatsApp}\n                            disabled={loading || status?.status === 'ready'}\n                            size=\"sm\"\n                        >\n                            تشغيل الواتساب\n                        </Button>\n\n                        <Button\n                            onClick={disconnectWhatsApp}\n                            disabled={loading || status?.status === 'disconnected'}\n                            variant=\"outline\"\n                            size=\"sm\"\n                        >\n                            قطع الاتصال\n                        </Button>\n\n                        <Button\n                            onClick={checkStatus}\n                            disabled={loading}\n                            variant=\"outline\"\n                            size=\"sm\"\n                        >\n                            تحديث\n                        </Button>\n\n                        <Button\n                            onClick={() => setShowWebView(!showWebView)}\n                            variant=\"outline\"\n                            size=\"sm\"\n                        >\n                            {showWebView ? 'إخفاء' : 'عرض'} الواتساب\n                        </Button>\n\n                        <Button\n                            onClick={() => window.open('https://web.whatsapp.com', '_blank')}\n                            variant=\"outline\"\n                            size=\"sm\"\n                        >\n                            فتح في نافذة جديدة\n                        </Button>\n                    </div>\n\n                    {/* QR Code Display */}\n                    {status?.qrCodeDataURL && (\n                        <div className=\"text-center\">\n                            <p className=\"text-sm text-muted-foreground mb-2\">امسح الكود بهاتفك:</p>\n                            <img \n                                src={status.qrCodeDataURL} \n                                alt=\"WhatsApp QR Code\" \n                                className=\"mx-auto border rounded\"\n                                style={{ maxWidth: '200px' }}\n                            />\n                        </div>\n                    )}\n\n                    {/* Error Display */}\n                    {status?.error && (\n                        <div className=\"p-3 bg-red-50 border border-red-200 rounded text-red-700 text-sm\">\n                            <strong>خطأ:</strong> {status.error}\n                        </div>\n                    )}\n\n                    {/* WhatsApp Web Viewer */}\n                    {showWebView && (\n                        <div className=\"border rounded-lg overflow-hidden\">\n                            <div className=\"bg-gray-100 p-2 text-sm font-medium flex items-center justify-between\">\n                                <span>واتساب ويب</span>\n                                <Button \n                                    onClick={() => setShowWebView(false)}\n                                    variant=\"ghost\"\n                                    size=\"sm\"\n                                >\n                                    ✕\n                                </Button>\n                            </div>\n                            <iframe\n                                src=\"https://web.whatsapp.com\"\n                                className=\"w-full\"\n                                style={{ height: '600px' }}\n                                title=\"WhatsApp Web\"\n                            />\n                        </div>\n                    )}\n\n                    {/* Last Activity */}\n                    {status?.lastActivity && (\n                        <div className=\"text-xs text-muted-foreground\">\n                            آخر نشاط: {new Date(status.lastActivity).toLocaleString('ar-EG')}\n                        </div>\n                    )}\n                </CardContent>\n            </Card>\n        </div>\n    );\n};\n\nexport default WhatsAppViewer;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,WAAW,EAAEC,UAAU,EAAEC,SAAS,QAAQ,WAAW;AACpE,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,KAAK,QAAQ,YAAY;AAClC,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SACIC,QAAQ,IAAIC,aAAa,QAQtB,qBAAqB;AAC5B,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAsB1B,MAAMC,cAA6C,GAAGA,CAAC;EAAEC;AAAU,CAAC,KAAK;EAAAC,EAAA;EACrE,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGlB,QAAQ,CAAwB,IAAI,CAAC;EACjE,MAAM,CAACmB,OAAO,EAAEC,UAAU,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACqB,WAAW,EAAEC,cAAc,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;;EAErD;EACA,MAAMuB,WAAW,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACAH,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMI,QAAQ,GAAG,MAAMb,KAAK,CAACc,GAAG,CAAC,sBAAsB,CAAC;MACxD,IAAID,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACvBT,SAAS,CAACM,QAAQ,CAACE,IAAI,CAACA,IAAI,CAAC;MACjC;IACJ,CAAC,CAAC,OAAOE,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;IAC3D,CAAC,SAAS;MACNR,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;;EAED;EACA,MAAMU,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACAV,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMI,QAAQ,GAAG,MAAMb,KAAK,CAACoB,IAAI,CAAC,0BAA0B,CAAC;MAC7D,IAAIP,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACvB,MAAMJ,WAAW,CAAC,CAAC;MACvB;IACJ,CAAC,CAAC,OAAOK,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACxD,CAAC,SAAS;MACNR,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;;EAED;EACA,MAAMY,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACAZ,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMI,QAAQ,GAAG,MAAMb,KAAK,CAACoB,IAAI,CAAC,0BAA0B,CAAC;MAC7D,IAAIP,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACvB,MAAMJ,WAAW,CAAC,CAAC;MACvB;IACJ,CAAC,CAAC,OAAOK,KAAK,EAAE;MACZC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACzD,CAAC,SAAS;MACNR,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;;EAED;EACA,MAAMa,cAAc,GAAGA,CAAA,KAAM;IACzB,IAAI,CAAChB,MAAM,EAAE,oBAAOJ,OAAA,CAACN,KAAK;MAAC2B,OAAO,EAAC,WAAW;MAAAC,QAAA,EAAC;IAAS;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC;IAEhE,QAAQtB,MAAM,CAACA,MAAM;MACjB,KAAK,OAAO;QACR,oBAAOJ,OAAA,CAACN,KAAK;UAAC2B,OAAO,EAAC,SAAS;UAAAC,QAAA,EAAC;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAChD,KAAK,eAAe;QAChB,oBAAO1B,OAAA,CAACN,KAAK;UAAC2B,OAAO,EAAC,SAAS;UAAAC,QAAA,EAAC;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MACtD,KAAK,aAAa;QACd,oBAAO1B,OAAA,CAACN,KAAK;UAAC2B,OAAO,EAAC,SAAS;UAAAC,QAAA,EAAC;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MACxD,KAAK,cAAc;QACf,oBAAO1B,OAAA,CAACN,KAAK;UAAC2B,OAAO,EAAC,WAAW;UAAAC,QAAA,EAAC;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MACtD,KAAK,OAAO;QACR,oBAAO1B,OAAA,CAACN,KAAK;UAAC2B,OAAO,EAAC,aAAa;UAAAC,QAAA,EAAC;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MACnD;QACI,oBAAO1B,OAAA,CAACN,KAAK;UAAC2B,OAAO,EAAC,WAAW;UAAAC,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;IAC3D;EACJ,CAAC;;EAED;EACAtC,SAAS,CAAC,MAAM;IACZsB,WAAW,CAAC,CAAC;IACb,MAAMiB,QAAQ,GAAGC,WAAW,CAAClB,WAAW,EAAE,IAAI,CAAC,CAAC,CAAC;IACjD,OAAO,MAAMmB,aAAa,CAACF,QAAQ,CAAC;EACxC,CAAC,EAAE,EAAE,CAAC;EAEN,oBACI3B,OAAA;IAAKE,SAAS,EAAEA,SAAU;IAAAoB,QAAA,eACtBtB,OAAA,CAACX,IAAI;MAAAiC,QAAA,gBACDtB,OAAA,CAACT,UAAU;QAAA+B,QAAA,eACPtB,OAAA,CAACR,SAAS;UAAA8B,QAAA,eACNtB,OAAA;YAAK8B,KAAK,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAEC,GAAG,EAAE;YAAM,CAAE;YAAAX,QAAA,gBAC9DtB,OAAA,CAACH,aAAa;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,6EAEjB,EAACN,cAAc,CAAC,CAAC;UAAA;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACb1B,OAAA,CAACV,WAAW;QAACY,SAAS,EAAC,WAAW;QAAAoB,QAAA,gBAE9BtB,OAAA;UAAKE,SAAS,EAAC,gCAAgC;UAAAoB,QAAA,gBAC3CtB,OAAA;YAAAsB,QAAA,gBACItB,OAAA;cAAME,SAAS,EAAC,aAAa;cAAAoB,QAAA,EAAC;YAAO;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC5C1B,OAAA;cAAME,SAAS,EAAC,MAAM;cAAAoB,QAAA,EAAE,CAAAlB,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEA,MAAM,KAAI;YAAW;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D,CAAC,eACN1B,OAAA;YAAAsB,QAAA,gBACItB,OAAA;cAAME,SAAS,EAAC,aAAa;cAAAoB,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC1C1B,OAAA;cAAME,SAAS,EAAC,MAAM;cAAAoB,QAAA,EAAElB,MAAM,aAANA,MAAM,eAANA,MAAM,CAAE8B,OAAO,GAAG,KAAK,GAAG;YAAI;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D,CAAC,eACN1B,OAAA;YAAAsB,QAAA,gBACItB,OAAA;cAAME,SAAS,EAAC,aAAa;cAAAoB,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACpD1B,OAAA;cAAME,SAAS,EAAC,MAAM;cAAAoB,QAAA,EAAE,CAAAlB,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE+B,kBAAkB,KAAI;YAAC;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9D,CAAC,eACN1B,OAAA;YAAAsB,QAAA,gBACItB,OAAA;cAAME,SAAS,EAAC,aAAa;cAAAoB,QAAA,EAAC;YAAgB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACrD1B,OAAA;cAAME,SAAS,EAAC,MAAM;cAAAoB,QAAA,EAAE,CAAAlB,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEgC,iBAAiB,KAAI;YAAC;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAEN1B,OAAA,CAACL,SAAS;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAGb1B,OAAA;UAAKE,SAAS,EAAC,sBAAsB;UAAAoB,QAAA,gBACjCtB,OAAA,CAACP,MAAM;YACH4C,OAAO,EAAEpB,kBAAmB;YAC5BqB,QAAQ,EAAEhC,OAAO,IAAI,CAAAF,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEA,MAAM,MAAK,OAAQ;YAChDmC,IAAI,EAAC,IAAI;YAAAjB,QAAA,EACZ;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAET1B,OAAA,CAACP,MAAM;YACH4C,OAAO,EAAElB,kBAAmB;YAC5BmB,QAAQ,EAAEhC,OAAO,IAAI,CAAAF,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEA,MAAM,MAAK,cAAe;YACvDiB,OAAO,EAAC,SAAS;YACjBkB,IAAI,EAAC,IAAI;YAAAjB,QAAA,EACZ;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAET1B,OAAA,CAACP,MAAM;YACH4C,OAAO,EAAE3B,WAAY;YACrB4B,QAAQ,EAAEhC,OAAQ;YAClBe,OAAO,EAAC,SAAS;YACjBkB,IAAI,EAAC,IAAI;YAAAjB,QAAA,EACZ;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAET1B,OAAA,CAACP,MAAM;YACH4C,OAAO,EAAEA,CAAA,KAAM5B,cAAc,CAAC,CAACD,WAAW,CAAE;YAC5Ca,OAAO,EAAC,SAAS;YACjBkB,IAAI,EAAC,IAAI;YAAAjB,QAAA,GAERd,WAAW,GAAG,OAAO,GAAG,KAAK,EAAC,mDACnC;UAAA;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAET1B,OAAA,CAACP,MAAM;YACH4C,OAAO,EAAEA,CAAA,KAAMG,MAAM,CAACC,IAAI,CAAC,0BAA0B,EAAE,QAAQ,CAAE;YACjEpB,OAAO,EAAC,SAAS;YACjBkB,IAAI,EAAC,IAAI;YAAAjB,QAAA,EACZ;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,EAGL,CAAAtB,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEsC,aAAa,kBAClB1C,OAAA;UAAKE,SAAS,EAAC,aAAa;UAAAoB,QAAA,gBACxBtB,OAAA;YAAGE,SAAS,EAAC,oCAAoC;YAAAoB,QAAA,EAAC;UAAkB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACxE1B,OAAA;YACI2C,GAAG,EAAEvC,MAAM,CAACsC,aAAc;YAC1BE,GAAG,EAAC,kBAAkB;YACtB1C,SAAS,EAAC,wBAAwB;YAClC4B,KAAK,EAAE;cAAEe,QAAQ,EAAE;YAAQ;UAAE;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACR,EAGA,CAAAtB,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEW,KAAK,kBACVf,OAAA;UAAKE,SAAS,EAAC,kEAAkE;UAAAoB,QAAA,gBAC7EtB,OAAA;YAAAsB,QAAA,EAAQ;UAAI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,KAAC,EAACtB,MAAM,CAACW,KAAK;QAAA;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CACR,EAGAlB,WAAW,iBACRR,OAAA;UAAKE,SAAS,EAAC,mCAAmC;UAAAoB,QAAA,gBAC9CtB,OAAA;YAAKE,SAAS,EAAC,uEAAuE;YAAAoB,QAAA,gBAClFtB,OAAA;cAAAsB,QAAA,EAAM;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvB1B,OAAA,CAACP,MAAM;cACH4C,OAAO,EAAEA,CAAA,KAAM5B,cAAc,CAAC,KAAK,CAAE;cACrCY,OAAO,EAAC,OAAO;cACfkB,IAAI,EAAC,IAAI;cAAAjB,QAAA,EACZ;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eACN1B,OAAA;YACI2C,GAAG,EAAC,0BAA0B;YAC9BzC,SAAS,EAAC,QAAQ;YAClB4B,KAAK,EAAE;cAAEgB,MAAM,EAAE;YAAQ,CAAE;YAC3BC,KAAK,EAAC;UAAc;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CACR,EAGA,CAAAtB,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE4C,YAAY,kBACjBhD,OAAA;UAAKE,SAAS,EAAC,+BAA+B;UAAAoB,QAAA,GAAC,+CACjC,EAAC,IAAI2B,IAAI,CAAC7C,MAAM,CAAC4C,YAAY,CAAC,CAACE,cAAc,CAAC,OAAO,CAAC;QAAA;UAAA3B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D,CACR;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEd,CAAC;AAACvB,EAAA,CAjNIF,cAA6C;AAAAkD,EAAA,GAA7ClD,cAA6C;AAmNnD,eAAeA,cAAc;AAAC,IAAAkD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}