// اختبار شامل لنظام الواتساب
const axios = require('axios');

const API_BASE = 'http://localhost:7080/api';

// أرقام الاختبار
const TEST_NUMBERS = [
    '01121381487',  // رقم الطالب
    '01008198992',  // ولي أمر 1
    '01017839220'   // ولي أمر 2
];

async function completeWhatsAppTest() {
    console.log('🚀 بدء الاختبار الشامل لنظام الواتساب\n');
    
    try {
        // 1. اختبار صحة السيرفر
        console.log('1️⃣ اختبار صحة السيرفر...');
        const healthResponse = await axios.get(`${API_BASE}/health`);
        console.log(`✅ السيرفر يعمل: ${healthResponse.data.status}`);
        console.log(`📊 المنفذ: ${healthResponse.data.port}`);
        console.log(`🗄️ قاعدة البيانات: ${healthResponse.data.database ? 'متصلة' : 'غير متصلة'}\n`);
        
        // 2. اختبار حالة الواتساب
        console.log('2️⃣ اختبار حالة الواتساب...');
        const statusResponse = await axios.get(`${API_BASE}/whatsapp/status`);
        const whatsappStatus = statusResponse.data.data;
        
        console.log(`📱 الحالة: ${whatsappStatus.status}`);
        console.log(`✅ جاهز: ${whatsappStatus.isReady ? 'نعم' : 'لا'}`);
        console.log(`🔗 الجلسة: ${whatsappStatus.sessionInfo ? 'موجودة' : 'غير موجودة'}\n`);
        
        // 3. تهيئة الواتساب إذا لم يكن جاهزاً
        if (!whatsappStatus.isReady) {
            console.log('3️⃣ تهيئة الواتساب...');
            try {
                await axios.post(`${API_BASE}/whatsapp/initialize`);
                console.log('⏳ انتظار 10 ثوانٍ للتهيئة...');
                await new Promise(resolve => setTimeout(resolve, 10000));
                
                // التحقق من الحالة مرة أخرى
                const newStatusResponse = await axios.get(`${API_BASE}/whatsapp/status`);
                const newStatus = newStatusResponse.data.data;
                console.log(`📱 الحالة الجديدة: ${newStatus.status}`);
                console.log(`✅ جاهز الآن: ${newStatus.isReady ? 'نعم' : 'لا'}\n`);
            } catch (initError) {
                console.log(`❌ خطأ في التهيئة: ${initError.response?.data?.error || initError.message}\n`);
            }
        }
        
        // 4. اختبار معالجة الأرقام
        console.log('4️⃣ اختبار معالجة الأرقام...');
        for (let i = 0; i < TEST_NUMBERS.length; i++) {
            const originalNumber = TEST_NUMBERS[i];
            const testVariations = [
                originalNumber,                    // 01121381487
                `+20${originalNumber}`,           // +2001121381487
                `20${originalNumber}`,            // 2001121381487
                originalNumber.substring(1),      // 1121381487
                `+20${originalNumber.substring(1)}` // +201121381487
            ];
            
            console.log(`\n📱 اختبار الرقم ${i + 1}: ${originalNumber}`);
            
            for (const variation of testVariations) {
                try {
                    const testResponse = await axios.post(`${API_BASE}/whatsapp/send-real`, {
                        phone_number: variation,
                        message: `🧪 اختبار معالجة الرقم
📱 الرقم الأصلي: ${originalNumber}
🔄 التنسيق المُختبر: ${variation}
⏰ الوقت: ${new Date().toLocaleString('ar-EG')}
✅ إذا وصلتك هذه الرسالة، فمعالجة الأرقام تعمل بنجاح!`
                    });
                    
                    if (testResponse.data.success) {
                        console.log(`  ✅ ${variation} → نجح الإرسال`);
                        console.log(`     📋 Chat ID: ${testResponse.data.data.chatId}`);
                        break; // نجح الإرسال، لا حاجة لاختبار باقي التنسيقات
                    } else {
                        console.log(`  ❌ ${variation} → فشل: ${testResponse.data.message}`);
                    }
                } catch (error) {
                    console.log(`  ❌ ${variation} → خطأ: ${error.response?.data?.error || error.message}`);
                }
                
                // انتظار قصير بين الاختبارات
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }
        
        // 5. اختبار الإرسال المجمع
        console.log('\n5️⃣ اختبار الإرسال المجمع...');
        
        const bulkRecipients = TEST_NUMBERS.map((phone, index) => ({
            phone: phone,
            name: `طالب اختبار ${index + 1}`,
            student_phone: phone
        }));
        
        try {
            const bulkResponse = await axios.post(`${API_BASE}/whatsapp/send-bulk`, {
                recipients: bulkRecipients,
                message: `📢 رسالة مجمعة اختبارية
👥 عدد المستقبلين: ${bulkRecipients.length}
⏰ الوقت: ${new Date().toLocaleString('ar-EG')}
🎯 اختبار نظام إدارة المراكز التعليمية
✅ إذا وصلتك هذه الرسالة، فالإرسال المجمع يعمل بنجاح!`
            });
            
            console.log('📤 نتيجة الإرسال المجمع:');
            console.log(`   نجح: ${bulkResponse.data.success ? 'نعم' : 'لا'}`);
            console.log(`   الرسالة: ${bulkResponse.data.message}`);
            
            if (bulkResponse.data.data?.summary) {
                const summary = bulkResponse.data.data.summary;
                console.log(`   📊 المجموع: ${summary.total}`);
                console.log(`   ✅ نجح: ${summary.success}`);
                console.log(`   ❌ فشل: ${summary.failed}`);
            }
            
        } catch (bulkError) {
            console.log(`❌ خطأ في الإرسال المجمع: ${bulkError.response?.data?.error || bulkError.message}`);
        }
        
        console.log('\n🎉 انتهى الاختبار الشامل!');
        console.log('📱 تحقق من الهواتف للتأكد من وصول الرسائل');
        console.log('🔍 راجع console السيرفر لمزيد من التفاصيل');
        
    } catch (error) {
        console.error('💥 خطأ عام في الاختبار:', error.response?.data || error.message);
    }
}

// تشغيل الاختبار
completeWhatsAppTest();
