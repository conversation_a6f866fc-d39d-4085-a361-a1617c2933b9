// اختبار المجموعة aaa
const axios = require('axios');

const API_BASE = 'http://localhost:7080/api';

async function testGroupAAA() {
    try {
        console.log('🔍 البحث عن المجموعة aaa...\n');
        
        // 1. جلب جميع المجموعات للعثور على aaa
        const groupsResponse = await axios.get(`${API_BASE}/groups`);
        console.log('📚 المجموعات الموجودة:');
        
        const groups = groupsResponse.data.data || [];
        groups.forEach(group => {
            console.log(`  - ID: ${group.id}, الاسم: ${group.group_name || group.name}`);
        });
        
        // البحث عن مجموعة aaa
        const aaaGroup = groups.find(g => 
            (g.group_name && g.group_name.toLowerCase().includes('aaa')) ||
            (g.name && g.name.toLowerCase().includes('aaa'))
        );
        
        if (!aaaGroup) {
            console.log('❌ لم يتم العثور على المجموعة aaa');
            return;
        }
        
        console.log(`\n✅ تم العثور على المجموعة aaa:`);
        console.log(`   ID: ${aaaGroup.id}`);
        console.log(`   الاسم: ${aaaGroup.group_name || aaaGroup.name}`);
        
        // 2. جلب طلاب المجموعة
        console.log(`\n👥 جلب طلاب المجموعة ${aaaGroup.id}...`);
        
        const studentsResponse = await axios.get(`${API_BASE}/groups/${aaaGroup.id}/students`);
        const students = studentsResponse.data.data || [];
        
        console.log(`📋 عدد الطلاب: ${students.length}`);
        
        if (students.length > 0) {
            console.log('\n👨‍🎓 قائمة الطلاب:');
            students.forEach((student, index) => {
                console.log(`\n${index + 1}. الطالب:`);
                console.log(`   ID: ${student.id}`);
                console.log(`   الاسم: ${student.student_name || student.name || 'غير محدد'}`);
                console.log(`   رقم الطالب: ${student.student_phone || 'غير محدد'}`);
                console.log(`   رقم الأب: ${student.father_phone || 'غير محدد'}`);
                console.log(`   رقم الأم: ${student.mother_phone || 'غير محدد'}`);
            });
            
            // 3. اختبار إرسال واتساب للمجموعة
            console.log(`\n📱 اختبار إرسال واتساب للمجموعة...`);
            
            try {
                const whatsappResponse = await axios.post(`${API_BASE}/whatsapp/send-bulk`, {
                    group_id: aaaGroup.id,
                    message: `🧪 رسالة اختبار للمجموعة aaa
👥 عدد الطلاب: ${students.length}
⏰ الوقت: ${new Date().toLocaleString('ar-EG')}
✅ اختبار نظام إدارة المراكز التعليمية`
                });
                
                console.log('📤 نتيجة إرسال الواتساب:');
                console.log(`   نجح: ${whatsappResponse.data.success ? 'نعم' : 'لا'}`);
                console.log(`   الرسالة: ${whatsappResponse.data.message}`);
                
                if (whatsappResponse.data.data) {
                    const summary = whatsappResponse.data.data.summary;
                    if (summary) {
                        console.log(`   المجموع: ${summary.total}`);
                        console.log(`   نجح: ${summary.success}`);
                        console.log(`   فشل: ${summary.failed}`);
                    }
                }
                
            } catch (whatsappError) {
                console.log('❌ خطأ في إرسال الواتساب:');
                console.log(`   ${whatsappError.response?.data?.error || whatsappError.message}`);
            }
            
        } else {
            console.log('⚠️ لا يوجد طلاب في المجموعة');
        }
        
    } catch (error) {
        console.error('💥 خطأ في الاختبار:', error.response?.data || error.message);
    }
}

// تشغيل الاختبار
testGroupAAA();
