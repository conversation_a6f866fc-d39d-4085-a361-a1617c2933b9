// خادم واتساب مبسط للاختبار
const express = require('express');
const cors = require('cors');

const app = express();
const PORT = 7080;

// Middleware
app.use(cors());
app.use(express.json());

// حالة الواتساب المحاكاة
let whatsappState = {
    status: 'disconnected',
    isReady: false,
    qrCode: null,
    qrCodeDataURL: null,
    sessionInfo: null,
    lastActivity: null,
    messageQueueLength: 0,
    isProcessingQueue: false,
    reconnectAttempts: 0,
    error: null
};

// Health check
app.get('/api/health', (req, res) => {
    res.json({
        status: 'OK',
        message: 'Simple WhatsApp server is running',
        timestamp: new Date().toISOString(),
        port: PORT
    });
});

// WhatsApp status
app.get('/api/whatsapp/status', (req, res) => {
    console.log('📊 Status requested:', whatsappState.status);
    res.json({
        success: true,
        data: whatsappState
    });
});

// WhatsApp initialize
app.post('/api/whatsapp/initialize', async (req, res) => {
    console.log('🔄 Initializing WhatsApp...');
    
    // محاكاة عملية التهيئة
    whatsappState.status = 'qr_received';
    whatsappState.qrCode = 'mock-qr-code';
    whatsappState.qrCodeDataURL = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';
    
    // بعد 3 ثوانٍ، محاكاة الاتصال
    setTimeout(() => {
        whatsappState.status = 'ready';
        whatsappState.isReady = true;
        whatsappState.qrCode = null;
        whatsappState.qrCodeDataURL = null;
        whatsappState.lastActivity = new Date().toISOString();
        console.log('✅ WhatsApp connected successfully (simulated)');
    }, 3000);
    
    res.json({
        success: true,
        message: 'WhatsApp initialization started',
        data: whatsappState
    });
});

// WhatsApp disconnect
app.post('/api/whatsapp/disconnect', async (req, res) => {
    console.log('🔌 Disconnecting WhatsApp...');
    
    whatsappState = {
        status: 'disconnected',
        isReady: false,
        qrCode: null,
        qrCodeDataURL: null,
        sessionInfo: null,
        lastActivity: null,
        messageQueueLength: 0,
        isProcessingQueue: false,
        reconnectAttempts: 0,
        error: null
    };
    
    res.json({
        success: true,
        message: 'WhatsApp disconnected successfully',
        data: whatsappState
    });
});

// Send bulk messages
app.post('/api/whatsapp/send-bulk', async (req, res) => {
    const { recipients, message, group_id } = req.body;
    
    console.log(`📤 Bulk message request:`, {
        recipientsCount: recipients?.length || 0,
        messageLength: message?.length || 0,
        groupId: group_id,
        whatsappReady: whatsappState.isReady
    });
    
    if (!message || !message.trim()) {
        return res.status(400).json({
            success: false,
            error: 'Message is required'
        });
    }

    if (!whatsappState.isReady) {
        return res.status(400).json({
            success: false,
            error: 'WhatsApp is not ready. Please initialize first.',
            currentStatus: whatsappState.status
        });
    }

    // محاكاة إرسال الرسائل
    const recipientCount = recipients?.length || 0;
    
    // محاكاة تأخير الإرسال
    setTimeout(() => {
        whatsappState.messageQueueLength = 0;
        whatsappState.isProcessingQueue = false;
        whatsappState.lastActivity = new Date().toISOString();
        console.log(`✅ ${recipientCount} messages sent successfully (simulated)`);
    }, 2000);
    
    whatsappState.messageQueueLength = recipientCount;
    whatsappState.isProcessingQueue = true;
    
    res.json({
        success: true,
        message: `Messages queued for ${recipientCount} recipients`,
        data: {
            queued: recipientCount,
            message: message.substring(0, 50) + (message.length > 50 ? '...' : ''),
            estimatedTime: recipientCount * 2 + ' seconds'
        }
    });
});

// Get groups (mock data)
app.get('/api/groups', (req, res) => {
    res.json({
        success: true,
        data: [
            { id: 1, name: 'المجموعة الأولى', student_count: 25 },
            { id: 2, name: 'المجموعة الثانية', student_count: 30 },
            { id: 3, name: 'المجموعة الثالثة', student_count: 20 }
        ]
    });
});

// Get students by group (mock data)
app.get('/api/groups/:groupId/students', (req, res) => {
    const groupId = req.params.groupId;
    res.json({
        success: true,
        data: [
            { 
                id: 1, 
                name: 'أحمد محمد', 
                student_phone: '01234567890',
                father_phone: '01234567891',
                mother_phone: '01234567892'
            },
            { 
                id: 2, 
                name: 'فاطمة علي', 
                student_phone: '01234567893',
                father_phone: '01234567894',
                mother_phone: '01234567895'
            }
        ]
    });
});

// Users endpoint (mock)
app.get('/api/users', (req, res) => {
    res.json({
        success: true,
        data: [
            { id: 1, name: 'المدير', email: '<EMAIL>', role: 'admin' }
        ]
    });
});

// Financial data endpoint (mock)
app.get('/api/financial/reports', (req, res) => {
    res.json({
        success: true,
        data: {
            totalRevenue: 50000,
            totalExpenses: 30000,
            netProfit: 20000
        }
    });
});

// Start server
app.listen(PORT, () => {
    console.log('🎉 ================================');
    console.log(`🚀 SIMPLE WHATSAPP SERVER RUNNING on http://localhost:${PORT}`);
    console.log(`📊 Health check: http://localhost:${PORT}/api/health`);
    console.log(`📱 WhatsApp status: http://localhost:${PORT}/api/whatsapp/status`);
    console.log(`✅ Mock WhatsApp service ready`);
    console.log('🎉 ================================');
});

// Handle process termination
process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down server...');
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\n🛑 Shutting down server...');
    process.exit(0);
});
