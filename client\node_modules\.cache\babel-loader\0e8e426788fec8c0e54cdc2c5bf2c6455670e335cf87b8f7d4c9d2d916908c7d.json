{"ast": null, "code": "var _jsxFileName = \"I:\\\\\\u0628\\u0631\\u0645\\u062C\\u0629\\\\\\u062A\\u0639\\u0644\\u064A\\u0645\\u064A ali2 2\\\\\\u062A\\u0639\\u0644\\u064A\\u0645\\u064A 2\\\\\\u0627\\u062F\\u0627\\u0631\\u0629 \\u0645\\u0631\\u0627\\u0643\\u0632 \\u062A\\u0639\\u0644\\u064A\\u0645\\u064A\\u0629\\\\edu 2\\\\client\\\\src\\\\components\\\\ui\\\\badge.tsx\";\nimport React from 'react';\nimport { Chip } from '@mui/material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const Badge = ({\n  children,\n  variant = 'default',\n  className\n}) => {\n  const getColor = () => {\n    switch (variant) {\n      case 'secondary':\n        return 'default';\n      case 'destructive':\n        return 'error';\n      default:\n        return 'primary';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Chip, {\n    label: children,\n    color: getColor(),\n    size: \"small\",\n    className: className\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 23,\n    columnNumber: 9\n  }, this);\n};\n_c = Badge;\nvar _c;\n$RefreshReg$(_c, \"Badge\");", "map": {"version": 3, "names": ["React", "Chip", "jsxDEV", "_jsxDEV", "Badge", "children", "variant", "className", "getColor", "label", "color", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["I:/برمجة/تعليمي ali2 2/تعليمي 2/ادارة مراكز تعليمية/edu 2/client/src/components/ui/badge.tsx"], "sourcesContent": ["import React from 'react';\nimport { Chip } from '@mui/material';\n\ninterface BadgeProps {\n    children: React.ReactNode;\n    variant?: 'default' | 'secondary' | 'destructive';\n    className?: string;\n}\n\nexport const Badge: React.FC<BadgeProps> = ({ children, variant = 'default', className }) => {\n    const getColor = () => {\n        switch (variant) {\n            case 'secondary':\n                return 'default';\n            case 'destructive':\n                return 'error';\n            default:\n                return 'primary';\n        }\n    };\n\n    return (\n        <Chip\n            label={children}\n            color={getColor()}\n            size=\"small\"\n            className={className}\n        />\n    );\n};\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAQrC,OAAO,MAAMC,KAA2B,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,OAAO,GAAG,SAAS;EAAEC;AAAU,CAAC,KAAK;EACzF,MAAMC,QAAQ,GAAGA,CAAA,KAAM;IACnB,QAAQF,OAAO;MACX,KAAK,WAAW;QACZ,OAAO,SAAS;MACpB,KAAK,aAAa;QACd,OAAO,OAAO;MAClB;QACI,OAAO,SAAS;IACxB;EACJ,CAAC;EAED,oBACIH,OAAA,CAACF,IAAI;IACDQ,KAAK,EAAEJ,QAAS;IAChBK,KAAK,EAAEF,QAAQ,CAAC,CAAE;IAClBG,IAAI,EAAC,OAAO;IACZJ,SAAS,EAAEA;EAAU;IAAAK,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACxB,CAAC;AAEV,CAAC;AAACC,EAAA,GApBWZ,KAA2B;AAAA,IAAAY,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}