// إنشاء بيانات اختبار للواتساب
const { getConnection } = require('./config/database');

async function createTestData() {
    let connection;
    
    try {
        connection = await getConnection();
        console.log('🔗 متصل بقاعدة البيانات...');

        // 1. إنشاء مجموعة اختبار
        console.log('📚 إنشاء مجموعة اختبار...');
        const groupResult = await connection.request()
            .input('name', 'مجموعة اختبار الواتساب')
            .input('description', 'مجموعة لاختبار إرسال رسائل الواتساب')
            .input('teacher_id', 1)
            .input('grade_id', 1)
            .input('subject_id', 1)
            .input('max_students', 50)
            .input('schedule', 'السبت والثلاثاء 4:00 م')
            .input('fees', 200.00)
            .query(`
                INSERT INTO groups (name, description, teacher_id, grade_id, subject_id, max_students, schedule, fees, created_at)
                OUTPUT INSERTED.id
                VALUES (@name, @description, @teacher_id, @grade_id, @subject_id, @max_students, @schedule, @fees, GETDATE())
            `);
        
        const groupId = groupResult.recordset[0].id;
        console.log(`✅ تم إنشاء المجموعة برقم: ${groupId}`);

        // 2. إنشاء طالب اختبار
        console.log('👨‍🎓 إنشاء طالب اختبار...');
        const studentResult = await connection.request()
            .input('name', 'أحمد محمد علي')
            .input('student_phone', '01121381487')
            .input('father_name', 'محمد علي أحمد')
            .input('father_phone', '01008198992')
            .input('mother_name', 'فاطمة حسن')
            .input('mother_phone', '01017839220')
            .input('address', 'القاهرة - مصر الجديدة')
            .input('birth_date', '2008-05-15')
            .input('grade_id', 1)
            .input('national_id', '30805151234567')
            .query(`
                INSERT INTO students (
                    name, student_phone, father_name, father_phone, 
                    mother_name, mother_phone, address, birth_date, 
                    grade_id, national_id, created_at
                )
                OUTPUT INSERTED.id
                VALUES (
                    @name, @student_phone, @father_name, @father_phone,
                    @mother_name, @mother_phone, @address, @birth_date,
                    @grade_id, @national_id, GETDATE()
                )
            `);
        
        const studentId = studentResult.recordset[0].id;
        console.log(`✅ تم إنشاء الطالب برقم: ${studentId}`);

        // 3. ربط الطالب بالمجموعة
        console.log('🔗 ربط الطالب بالمجموعة...');
        await connection.request()
            .input('student_id', studentId)
            .input('group_id', groupId)
            .input('enrollment_date', new Date())
            .input('status', 'active')
            .query(`
                INSERT INTO student_groups (student_id, group_id, enrollment_date, status, created_at)
                VALUES (@student_id, @group_id, @enrollment_date, @status, GETDATE())
            `);
        
        console.log('✅ تم ربط الطالب بالمجموعة');

        // 4. التحقق من البيانات المُنشأة
        console.log('\n📊 التحقق من البيانات المُنشأة:');
        
        const verifyResult = await connection.request()
            .input('group_id', groupId)
            .query(`
                SELECT 
                    s.id,
                    s.name,
                    s.student_phone,
                    s.father_name,
                    s.father_phone,
                    s.mother_name,
                    s.mother_phone,
                    g.name as group_name
                FROM students s
                INNER JOIN student_groups sg ON s.id = sg.student_id
                INNER JOIN groups g ON sg.group_id = g.id
                WHERE g.id = @group_id
            `);

        console.log('📋 بيانات الطلاب في المجموعة:');
        verifyResult.recordset.forEach(student => {
            console.log(`
👨‍🎓 الطالب: ${student.name}
📱 رقم الطالب: ${student.student_phone}
👨 الأب: ${student.father_name} - ${student.father_phone}
👩 الأم: ${student.mother_name} - ${student.mother_phone}
📚 المجموعة: ${student.group_name}
            `);
        });

        console.log(`\n🎯 معرف المجموعة للاختبار: ${groupId}`);
        console.log(`🎯 معرف الطالب للاختبار: ${studentId}`);
        
        return { groupId, studentId };

    } catch (error) {
        console.error('❌ خطأ في إنشاء بيانات الاختبار:', error);
        throw error;
    } finally {
        if (connection) {
            await connection.close();
            console.log('🔒 تم إغلاق الاتصال بقاعدة البيانات');
        }
    }
}

// تشغيل إنشاء البيانات
createTestData()
    .then(result => {
        console.log('\n🎉 تم إنشاء بيانات الاختبار بنجاح!');
        console.log(`📋 استخدم معرف المجموعة: ${result.groupId} للاختبار`);
    })
    .catch(error => {
        console.error('💥 فشل في إنشاء بيانات الاختبار:', error);
        process.exit(1);
    });
